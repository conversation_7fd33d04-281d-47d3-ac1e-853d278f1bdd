/**
 * 文件: unified-config-center.js
 * 路径: js\core\unified-config-center.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.OTA.getService
 * - window.location
 * - window.OTA.container
 * - window.OTA.container.register
 * 
 * === 对外暴露 ===
 * - window.OTA
 * - window.OTA.configCenter
 * - window.OTA.getConfigCenter
 * - window.getConfigCenter
 * 
 * === 类声明 ===
 * - class UnifiedConfigCenter
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.664Z
 */

/**
 * @OTA_CORE 统一配置中心 - 简化版
 * 🏷️ 标签: @CONFIG_CENTER @UNIFIED_CONFIG @SIMPLIFIED
 * 📝 说明: 统一管理所有系统配置，简化版本专注核心功能
 * ⚠️ 警告: 核心配置管理，请勿重复开发
 */

(function() {
    'use strict';

    // 延迟获取依赖，确保加载顺序

    /**
     * @OTA_CORE 统一配置中心类 - 简化版
     * 管理所有系统配置的统一入口，移除过度工程化功能
     */
    class UnifiedConfigCenter {
        constructor() {
            this.logger = window.OTA.getService('logger');

            // 配置存储 - 简化版
            this.configs = new Map();
            this.validators = new Map();

            // 环境检测
            this.environment = this.detectEnvironment();

            // 简化的元数据
            this.metadata = {
                version: '1.0.0-simplified',
                lastUpdate: Date.now(),
                environment: this.environment
            };

            // 初始化默认配置
            this.initializeDefaultConfigs();

            // 初始化基本验证器
            this.initializeBasicValidators();

            this.logger.log('✅ 统一配置中心已初始化（简化版）', 'info', {
                environment: this.environment,
                configCount: this.configs.size
            });
        }

        /**
         * 检测运行环境 - 简化版
         * @returns {string} 环境名称
         */
        detectEnvironment() {
            const hostname = window.location?.hostname || '';
            if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('file://')) {
                return 'development';
            }
            return 'production';
        }

        /**
         * 初始化默认配置 - 简化版
         */
        initializeDefaultConfigs() {
            // API配置 - 简化版
            this.setConfig('api', {
                baseURL: 'https://gomyhire.com.my/api',
                timeout: 15000,
                retries: 3
            });

            // UI配置 - 简化版
            this.setConfig('ui', {
                theme: 'fluent-purple',
                animations: true,
                debounceDelay: 300
            });

            // 性能配置 - 简化版
            this.setConfig('performance', {
                cacheEnabled: true,
                enableMetrics: false // 简化版关闭复杂监控
            });

            // 日志配置 - 简化版
            this.setConfig('logging', {
                level: this.environment === 'development' ? 'debug' : 'info',
                enableConsole: true
            });

            // 功能开关 - 简化版
            this.setConfig('features', {
                multiOrderMode: true,
                imageUpload: true,
                currencyConversion: true,
                realtimeAnalysis: true
            });

            // 安全配置 - 简化版
            this.setConfig('security', {
                tokenExpiry: 24 * 60 * 60 * 1000, // 24小时
                rememberMeExpiry: 7 * 24 * 60 * 60 * 1000 // 7天
            });
        }

        /**
         * 设置配置
         * @param {string} key - 配置键
         * @param {any} value - 配置值
         * @param {Object} options - 选项
         */
        setConfig(key, value, options = {}) {

            
            // 验证配置
            if (this.validators.has(key)) {
                const validator = this.validators.get(key);
                const validationResult = validator(value);
                if (!validationResult.isValid) {
                    this.logger.logError(`配置验证失败 [${key}]`, validationResult.errors);
                    if (options.strict !== false) {
                        throw new Error(`配置验证失败: ${validationResult.errors.join(', ')}`);
                    }
                }
            }
            
            // 设置配置 - 简化版
            this.configs.set(key, {
                value,
                timestamp: Date.now()
            });
            
            // 更新元数据 - 简化版
            this.metadata.lastUpdate = Date.now();

            this.logger.log(`配置已更新: ${key}`, 'debug', { value });
        }

        /**
         * 获取配置
         * @param {string} key - 配置键
         * @param {any} defaultValue - 默认值
         * @returns {any} 配置值
         */
        getConfig(key, defaultValue = null) {
            const config = this.configs.get(key);
            if (!config) {
                if (defaultValue !== null) {
                    return defaultValue;
                }
                this.logger.log(`配置不存在: ${key}`, 'warning');
                return null;
            }
            
            return config.value;
        }

        /**
         * 获取嵌套配置
         * @param {string} path - 配置路径 (如: 'api.timeout')
         * @param {any} defaultValue - 默认值
         * @returns {any} 配置值
         */
        getNestedConfig(path, defaultValue = null) {
            const keys = path.split('.');
            const rootKey = keys[0];
            const config = this.getConfig(rootKey);
            
            if (!config) {
                return defaultValue;
            }
            
            let value = config;
            for (let i = 1; i < keys.length; i++) {
                if (value && typeof value === 'object' && keys[i] in value) {
                    value = value[keys[i]];
                } else {
                    return defaultValue;
                }
            }
            
            return value;
        }

        /**
         * 注册配置验证器 - 简化版
         * @param {string} key - 配置键
         * @param {Function} validator - 验证函数
         */
        registerValidator(key, validator) {
            this.validators.set(key, validator);
            this.logger.log(`已注册配置验证器: ${key}`, 'debug');
        }

        /**
         * 初始化基本验证器 - 简化版
         */
        initializeBasicValidators() {
            // API配置验证器 - 简化版
            this.registerValidator('api', (config) => {
                const errors = [];
                if (!config.baseURL || typeof config.baseURL !== 'string') {
                    errors.push('baseURL必须是有效的字符串');
                }
                if (!config.timeout || config.timeout < 1000) {
                    errors.push('timeout必须大于1000ms');
                }
                return { isValid: errors.length === 0, errors };
            });
        }

        /**
         * 获取所有配置 - 简化版
         * @returns {Object} 所有配置的快照
         */
        getAllConfigs() {
            const snapshot = {};
            for (const [key, config] of this.configs.entries()) {
                snapshot[key] = config.value;
            }
            return snapshot;
        }

        /**
         * 获取配置中心状态 - 简化版
         * @returns {Object} 状态信息
         */
        getStatus() {
            return {
                environment: this.environment,
                configCount: this.configs.size,
                validatorCount: this.validators.size,
                metadata: { ...this.metadata }
            };
        }


    }

    // 创建全局实例
    const unifiedConfigCenter = new UnifiedConfigCenter();

    // 🗑️ 已移除重复注册问题 - 减法重构优化
    // 统一服务注册机制：仅通过依赖容器注册，避免全局变量污染

    window.OTA = window.OTA || {};

    // 优先注册到依赖容器（单例模式）
    if (window.OTA && window.OTA.container) {
        window.OTA.container.register('configCenter', () => unifiedConfigCenter, { singleton: true });
        console.log('✅ ConfigCenter已统一注册到依赖容器');
    } else {
        // 🗑️ 仅在容器不可用时才全局注册 - 减法重构优化
        // 避免重复注册：window.OTA.configCenter 和 window.getConfigCenter
        window.OTA.configCenter = unifiedConfigCenter;
        console.warn('⚠️ ConfigCenter已降级到全局注册');
    }

})();
