/**
 * 价格服务模块 - 微服务架构
 * 负责价格转换、货币处理和价格显示逻辑
 * 从PriceManager转换而来，采用函数式编程风格
 * 
 * <AUTHOR> System
 * @created 2025-01-01
 */

(function() {
    'use strict';

    // 确保全局命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.services = window.OTA.services || {};

    /**
     * 获取依赖服务的辅助函数
     */
    function getLogger() {
        return window.OTA?.logger || window.getLogger?.() || console;
    }

    /**
     * 价格服务状态
     */
    let serviceState = {
        initialized: false,
        elements: {},
        manualEditMode: false,
        isAutoFilling: false,
        editTimeout: null,
        currencyConverter: null
    };

    /**
     * 汇率配置
     */
    const EXCHANGE_RATES = {
        'MYR': 1.0,
        'USD': 4.3,
        'SGD': 3.4,
        'CNY': 0.615
    };

    /**
     * 货币符号配置
     */
    const CURRENCY_SYMBOLS = {
        'MYR': 'RM',
        'USD': '$',
        'SGD': 'S$',
        'CNY': '¥'
    };

    /**
     * 创建简单货币转换器
     */
    function createCurrencyConverter() {
        return {
            /**
             * 转换为马币
             * @param {number} amount - 金额
             * @param {string} currency - 原货币
             * @returns {Object} 转换结果
             */
            convertToMYR(amount, currency) {
                const rate = EXCHANGE_RATES[currency] || 1;
                const convertedAmount = amount * rate;
                return {
                    originalAmount: amount,
                    convertedAmount: convertedAmount,
                    needsConversion: currency !== 'MYR',
                    exchangeRate: rate,
                    originalCurrency: currency
                };
            },

            /**
             * 格式化价格显示
             * @param {number} amount - 金额
             * @param {string} currency - 货币
             * @returns {string} 格式化后的价格
             */
            formatPrice(amount, currency) {
                const symbol = CURRENCY_SYMBOLS[currency] || currency;
                return `${symbol} ${parseFloat(amount).toFixed(2)}`;
            }
        };
    }

    /**
     * 缓存价格相关元素
     */
    function cachePriceElements() {
        serviceState.elements = {
            otaPrice: document.getElementById('otaPrice'),
            currency: document.getElementById('currency'),
            priceGroup: document.querySelector('#otaPriceGroup'),
            conversionDisplay: null // 将动态创建
        };

        getLogger().log('价格元素缓存完成', 'info');
    }

    /**
     * 创建价格转换显示区域
     */
    function createPriceConversionDisplay() {
        const priceGroup = serviceState.elements.priceGroup;
        if (!priceGroup || priceGroup.querySelector('.price-conversion-display')) {
            return;
        }

        const conversionDisplay = document.createElement('div');
        conversionDisplay.className = 'price-conversion-display';
        conversionDisplay.style.cssText = `
            margin-top: 8px;
            padding: 8px 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 14px;
            color: #495057;
            display: none;
        `;

        priceGroup.appendChild(conversionDisplay);
        serviceState.elements.conversionDisplay = conversionDisplay;

        getLogger().log('价格转换显示区域已创建', 'info');
    }

    /**
     * 进入手动编辑模式
     */
    function enterManualEditMode() {
        if (!serviceState.manualEditMode) {
            serviceState.manualEditMode = true;
            addManualEditIndicator();
            getLogger().log('进入手动编辑模式', 'info');
        }
    }

    /**
     * 退出手动编辑模式
     */
    function exitManualEditMode() {
        if (serviceState.manualEditMode) {
            serviceState.manualEditMode = false;
            if (serviceState.editTimeout) {
                clearTimeout(serviceState.editTimeout);
                serviceState.editTimeout = null;
            }
            removeManualEditIndicator();
            getLogger().log('退出手动编辑模式', 'info');
        }
    }

    /**
     * 添加手动编辑模式的视觉提示
     */
    function addManualEditIndicator() {
        const priceGroup = serviceState.elements.priceGroup;
        if (priceGroup && !priceGroup.querySelector('.manual-edit-indicator')) {
            const indicator = document.createElement('div');
            indicator.className = 'manual-edit-indicator';
            indicator.innerHTML = '✏️ 手动编辑中';
            indicator.style.cssText = `
                position: absolute;
                top: -8px;
                right: -8px;
                background: #ffc107;
                color: #000;
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 8px;
                z-index: 10;
                pointer-events: none;
            `;
            priceGroup.style.position = 'relative';
            priceGroup.appendChild(indicator);
        }
    }

    /**
     * 移除手动编辑模式的视觉提示
     */
    function removeManualEditIndicator() {
        const indicator = document.querySelector('.manual-edit-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    /**
     * 更新价格转换显示
     * @param {boolean} force - 是否强制更新
     */
    function updatePriceConversion(force = false) {
        if (!force && serviceState.manualEditMode && !serviceState.isAutoFilling) {
            return;
        }

        const priceInput = serviceState.elements.otaPrice;
        const currencySelect = serviceState.elements.currency;
        const conversionDisplay = serviceState.elements.conversionDisplay;

        if (!priceInput || !currencySelect || !conversionDisplay) {
            return;
        }

        const amount = parseFloat(priceInput.value);
        const fromCurrency = currencySelect.value;

        if (!amount || amount <= 0 || !fromCurrency) {
            conversionDisplay.style.display = 'none';
            return;
        }

        if (serviceState.currencyConverter) {
            const result = serviceState.currencyConverter.convertToMYR(amount, fromCurrency);
            
            if (result.needsConversion) {
                const originalFormatted = serviceState.currencyConverter.formatPrice(result.originalAmount, fromCurrency);
                const convertedFormatted = serviceState.currencyConverter.formatPrice(result.convertedAmount, 'MYR');
                
                conversionDisplay.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>原价: ${originalFormatted}</span>
                        <span style="margin: 0 8px;">→</span>
                        <span style="font-weight: bold;">马币: ${convertedFormatted}</span>
                    </div>
                    <div style="font-size: 12px; color: #6c757d; margin-top: 4px;">
                        汇率: 1 ${fromCurrency} = ${result.exchangeRate} MYR
                    </div>
                `;
                
                conversionDisplay.style.display = 'block';
                getLogger().log('价格转换显示已更新', 'info');
            } else {
                conversionDisplay.style.display = 'none';
            }
        }
    }

    /**
     * 验证价格输入
     */
    function validatePriceInput() {
        const priceInput = serviceState.elements.otaPrice;
        const priceGroup = serviceState.elements.priceGroup;

        if (!priceInput || !priceGroup) return;

        const amount = parseFloat(priceInput.value);
        priceGroup.classList.remove('invalid', 'valid');

        if (priceInput.value && (isNaN(amount) || amount <= 0)) {
            priceGroup.classList.add('invalid');
        } else if (amount > 0) {
            priceGroup.classList.add('valid');
        }
    }

    /**
     * 货币切换时的智能汇率转换
     * @param {string} newCurrency - 新货币
     * @param {string} oldCurrency - 旧货币
     */
    function convertPriceOnCurrencyChange(newCurrency, oldCurrency) {
        if (serviceState.manualEditMode && !serviceState.isAutoFilling) return;

        const priceInput = serviceState.elements.otaPrice;
        if (!priceInput || !priceInput.value || !serviceState.currencyConverter) return;

        const currentAmount = parseFloat(priceInput.value);
        if (isNaN(currentAmount) || currentAmount <= 0) return;

        try {
            // 转换为MYR基准
            const toMYRResult = serviceState.currencyConverter.convertToMYR(currentAmount, oldCurrency);
            
            // 从MYR转换为目标货币
            const newRate = EXCHANGE_RATES[newCurrency] || 1;
            const convertedAmount = Math.round((toMYRResult.convertedAmount / newRate) * 100) / 100;

            priceInput.value = convertedAmount.toFixed(2);
            updatePriceConversion();

            getLogger().log('货币切换转换完成', 'info', {
                from: `${currentAmount} ${oldCurrency}`,
                to: `${convertedAmount} ${newCurrency}`
            });
        } catch (error) {
            getLogger().log('货币切换转换失败', 'error', { error: error.message });
        }
    }

    /**
     * 设置价格验证和事件监听
     */
    function setupPriceValidation() {
        const priceInput = serviceState.elements.otaPrice;
        const currencySelect = serviceState.elements.currency;

        if (priceInput) {
            // 焦点事件
            priceInput.addEventListener('focus', () => {
                if (!serviceState.isAutoFilling) {
                    setTimeout(() => {
                        if (!serviceState.isAutoFilling) {
                            enterManualEditMode();
                        }
                    }, 100);
                }
            });

            // 键盘输入事件
            priceInput.addEventListener('keydown', (event) => {
                if (!serviceState.isAutoFilling) {
                    const inputKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];
                    const isDigitKey = /^[0-9.]$/.test(event.key);
                    
                    if (isDigitKey || inputKeys.includes(event.key)) {
                        enterManualEditMode();
                    }
                }
            });

            // 输入事件
            priceInput.addEventListener('input', () => {
                validatePriceInput();
                if (!serviceState.manualEditMode || serviceState.isAutoFilling) {
                    updatePriceConversion();
                }
            });

            // 失焦事件
            priceInput.addEventListener('blur', () => {
                validatePriceInput();
                if (serviceState.manualEditMode) {
                    serviceState.editTimeout = setTimeout(() => {
                        exitManualEditMode();
                        updatePriceConversion(true);
                    }, 2000);
                }
            });
        }

        // 货币切换监听
        if (currencySelect) {
            let previousCurrency = currencySelect.value || 'MYR';
            currencySelect.addEventListener('change', () => {
                const newCurrency = currencySelect.value;
                convertPriceOnCurrencyChange(newCurrency, previousCurrency);
                previousCurrency = newCurrency;
            });
        }
    }

    /**
     * 设置价格和货币
     * @param {number} amount - 金额
     * @param {string} currency - 货币
     */
    function setPriceAndCurrency(amount, currency) {
        serviceState.isAutoFilling = true;

        if (serviceState.elements.otaPrice) {
            serviceState.elements.otaPrice.value = amount;
        }
        if (serviceState.elements.currency) {
            serviceState.elements.currency.value = currency;
        }

        validatePriceInput();
        updatePriceConversion();
        
        setTimeout(() => {
            serviceState.isAutoFilling = false;
        }, 100);

        getLogger().log('价格和货币已设置', 'info', { amount, currency });
    }

    /**
     * 处理价格转换
     * @param {Object} orderData - 订单数据
     */
    function processPriceConversion(orderData) {
        if (!orderData || !serviceState.currencyConverter) return;

        try {
            if (orderData.ota_price && orderData.currency) {
                setPriceAndCurrency(orderData.ota_price, orderData.currency);
                getLogger().log('价格转换处理完成', 'success');
            }
        } catch (error) {
            getLogger().log('价格转换处理失败', 'error', { error: error.message });
        }
    }

    /**
     * 重置编辑模式并恢复汇率转换
     */
    function resetManualEditModeAndUpdateConversion() {
        if (serviceState.manualEditMode) {
            exitManualEditMode();
            updatePriceConversion(true);
            getLogger().log('用户重置编辑模式并恢复汇率转换', 'info');
        }
    }

    /**
     * 价格服务对象 - 微服务接口
     */
    const PriceService = {
        /**
         * 初始化服务
         */
        init() {
            if (serviceState.initialized) {
                getLogger().log('价格服务已经初始化', 'warning');
                return Promise.resolve();
            }

            try {
                serviceState.currencyConverter = createCurrencyConverter();
                cachePriceElements();
                createPriceConversionDisplay();
                setupPriceValidation();
                
                serviceState.initialized = true;
                getLogger().log('价格服务初始化完成', 'success');
                return Promise.resolve();
            } catch (error) {
                getLogger().log('价格服务初始化失败', 'error', { error: error.message });
                return Promise.reject(error);
            }
        },

        /**
         * 销毁服务
         */
        destroy() {
            exitManualEditMode();
            
            // 移除转换显示区域
            if (serviceState.elements.conversionDisplay) {
                serviceState.elements.conversionDisplay.remove();
            }

            serviceState.initialized = false;
            serviceState.elements = {};
            serviceState.currencyConverter = null;
            
            getLogger().log('价格服务已销毁', 'info');
            return Promise.resolve();
        },

        /**
         * 获取服务状态
         */
        getStatus() {
            return {
                name: 'PriceService',
                initialized: serviceState.initialized,
                state: serviceState.initialized ? 'ready' : 'uninitialized',
                manualEditMode: serviceState.manualEditMode,
                isAutoFilling: serviceState.isAutoFilling,
                version: '1.0.0'
            };
        },

        // 公共API方法
        updatePriceConversion,
        validatePriceInput,
        setPriceAndCurrency,
        processPriceConversion,
        resetManualEditModeAndUpdateConversion,
        enterManualEditMode,
        exitManualEditMode,
        
        // 货币转换器访问
        getCurrencyConverter: () => serviceState.currencyConverter
    };

    // 注册到全局命名空间
    window.OTA.services.priceService = PriceService;

    // 向后兼容性支持
    window.priceService = PriceService;

    // 日志记录
    getLogger().log('价格服务模块已加载', 'info');

})();
