/**
 * API接口定义 - 微服务架构
 * 定义所有服务的标准化公共API接口
 * 实现配置驱动的服务管理
 * 
 * <AUTHOR> System
 * @created 2025-01-01
 */

(function() {
    'use strict';

    // 确保全局命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.interfaces = window.OTA.interfaces || {};

    /**
     * 微服务标准接口定义
     */
    const MicroserviceInterface = {
        /**
         * 必需方法 - 所有微服务必须实现
         */
        required: {
            /**
             * 初始化服务
             * @returns {Promise<void>} 初始化结果
             */
            init: {
                signature: '() => Promise<void>',
                description: '初始化服务，设置必要的状态和依赖',
                required: true
            },

            /**
             * 销毁服务
             * @returns {Promise<void>} 销毁结果
             */
            destroy: {
                signature: '() => Promise<void>',
                description: '清理服务资源，移除事件监听器',
                required: true
            },

            /**
             * 获取服务状态
             * @returns {Object} 服务状态信息
             */
            getStatus: {
                signature: '() => Object',
                description: '返回服务当前状态和健康信息',
                required: true,
                returnFormat: {
                    name: 'string',
                    initialized: 'boolean',
                    state: 'string', // 'uninitialized' | 'ready' | 'error' | 'destroyed'
                    version: 'string',
                    error: 'string?'
                }
            },

            /**
             * 检查服务可用性
             * @returns {boolean} 是否可用
             */
            isAvailable: {
                signature: '() => boolean',
                description: '检查服务是否已初始化且可用',
                required: true
            }
        },

        /**
         * 可选方法 - 根据服务需要实现
         */
        optional: {
            /**
             * 重置服务状态
             * @returns {Promise<void>} 重置结果
             */
            reset: {
                signature: '() => Promise<void>',
                description: '重置服务到初始状态',
                required: false
            },

            /**
             * 配置服务
             * @param {Object} config - 配置对象
             * @returns {Promise<void>} 配置结果
             */
            configure: {
                signature: '(config: Object) => Promise<void>',
                description: '动态配置服务参数',
                required: false
            },

            /**
             * 健康检查
             * @returns {Promise<Object>} 健康检查结果
             */
            healthCheck: {
                signature: '() => Promise<Object>',
                description: '执行深度健康检查',
                required: false,
                returnFormat: {
                    healthy: 'boolean',
                    checks: 'Array<Object>',
                    timestamp: 'string'
                }
            }
        }
    };

    /**
     * 服务状态枚举
     */
    const ServiceStates = {
        UNINITIALIZED: 'uninitialized',
        INITIALIZING: 'initializing',
        READY: 'ready',
        ERROR: 'error',
        DESTROYED: 'destroyed'
    };

    /**
     * 核心服务API接口定义
     */
    const CoreServiceAPIs = {
        /**
         * UI服务API
         */
        uiService: {
            namespace: 'window.OTA.services.uiService',
            methods: {
                // 标准接口
                ...MicroserviceInterface.required,
                ...MicroserviceInterface.optional,
                
                // 专用方法
                showAlert: {
                    signature: '(message: string, type?: string) => void',
                    description: '显示警告消息'
                },
                showError: {
                    signature: '(message: string, error?: Error) => void',
                    description: '显示错误消息'
                },
                showSuccess: {
                    signature: '(message: string) => void',
                    description: '显示成功消息'
                },
                showLoading: {
                    signature: '(message?: string) => void',
                    description: '显示加载状态'
                },
                hideLoading: {
                    signature: '() => void',
                    description: '隐藏加载状态'
                },
                updateLoginUI: {
                    signature: '() => void',
                    description: '更新登录界面状态'
                }
            }
        },

        /**
         * 表单服务API
         */
        formService: {
            namespace: 'window.OTA.services.formService',
            methods: {
                // 标准接口
                ...MicroserviceInterface.required,
                ...MicroserviceInterface.optional,
                
                // 专用方法
                fillFormFromData: {
                    signature: '(data: Object) => void',
                    description: '从数据对象填充表单'
                },
                validateForm: {
                    signature: '() => Object',
                    description: '验证表单数据'
                },
                getFormData: {
                    signature: '() => Object',
                    description: '获取表单数据'
                },
                resetForm: {
                    signature: '() => void',
                    description: '重置表单到初始状态'
                },
                populateFormOptions: {
                    signature: '() => void',
                    description: '填充表单选项数据'
                }
            }
        },

        /**
         * 价格服务API
         */
        priceService: {
            namespace: 'window.OTA.services.priceService',
            methods: {
                // 标准接口
                ...MicroserviceInterface.required,
                ...MicroserviceInterface.optional,
                
                // 专用方法
                convertCurrency: {
                    signature: '(amount: number, fromCurrency: string, toCurrency: string) => number',
                    description: '货币转换'
                },
                updatePriceDisplay: {
                    signature: '(originalPrice: number, currency: string) => void',
                    description: '更新价格显示'
                },
                toggleManualEdit: {
                    signature: '() => void',
                    description: '切换手动编辑模式'
                },
                resetPriceCalculation: {
                    signature: '() => void',
                    description: '重置价格计算'
                }
            }
        },

        /**
         * 状态服务API
         */
        stateService: {
            namespace: 'window.OTA.services.stateService',
            methods: {
                // 标准接口
                ...MicroserviceInterface.required,
                ...MicroserviceInterface.optional,
                
                // 专用方法
                updateAllUIStatus: {
                    signature: '() => void',
                    description: '更新所有UI状态'
                },
                updateGeminiStatus: {
                    signature: '(status: string) => void',
                    description: '更新Gemini服务状态'
                },
                updateConnectionStatus: {
                    signature: '(connected: boolean) => void',
                    description: '更新连接状态'
                },
                updateLoginStatus: {
                    signature: '(isLoggedIn: boolean) => void',
                    description: '更新登录状态'
                },
                toggleTheme: {
                    signature: '() => void',
                    description: '切换主题'
                }
            }
        },

        /**
         * 事件服务API
         */
        eventService: {
            namespace: 'window.OTA.services.eventService',
            methods: {
                // 标准接口
                ...MicroserviceInterface.required,
                ...MicroserviceInterface.optional,
                
                // 专用方法
                addEventHandler: {
                    signature: '(element: Element, eventType: string, handler: Function, options?: Object) => string',
                    description: '添加事件处理器'
                },
                removeEventHandler: {
                    signature: '(key: string) => boolean',
                    description: '移除事件处理器'
                },
                debounce: {
                    signature: '(func: Function, delay: number) => Function',
                    description: '创建防抖函数'
                },
                triggerSave: {
                    signature: '() => void',
                    description: '触发保存操作'
                },
                triggerSubmit: {
                    signature: '() => void',
                    description: '触发提交操作'
                }
            }
        }
    };

    /**
     * 全局API接口定义
     */
    const GlobalAPIs = {
        /**
         * 服务管理API
         */
        serviceManagement: {
            'window.OTA.getService': {
                signature: '(serviceName: string) => any',
                description: '获取服务实例'
            },
            'window.OTA.initializeAllMicroservices': {
                signature: '() => Promise<Object>',
                description: '初始化所有微服务'
            },
            'window.OTA.destroyAllMicroservices': {
                signature: '() => Promise<Object>',
                description: '销毁所有微服务'
            },
            'window.OTA.getMicroservicesStatus': {
                signature: '() => Object',
                description: '获取所有微服务状态'
            },
            'window.OTA.healthCheckMicroservices': {
                signature: '() => Promise<Object>',
                description: '健康检查所有微服务'
            }
        },

        /**
         * 监控API
         */
        monitoring: {
            'window.OTA.getServiceUsageStats': {
                signature: '() => Object',
                description: '获取服务使用统计'
            },
            'window.OTA.getServiceMigrationReport': {
                signature: '() => Object',
                description: '获取服务迁移报告'
            },
            'window.OTA.resetServiceMonitoring': {
                signature: '() => void',
                description: '重置服务监控数据'
            }
        }
    };

    /**
     * API接口验证器
     */
    const InterfaceValidator = {
        /**
         * 验证服务是否符合标准接口
         * @param {Object} service - 服务对象
         * @param {string} serviceName - 服务名称
         * @returns {Object} 验证结果
         */
        validateService(service, serviceName) {
            const result = {
                valid: true,
                errors: [],
                warnings: [],
                serviceName
            };

            // 检查必需方法
            Object.entries(MicroserviceInterface.required).forEach(([methodName, methodDef]) => {
                if (!service[methodName] || typeof service[methodName] !== 'function') {
                    result.valid = false;
                    result.errors.push(`缺少必需方法: ${methodName}`);
                }
            });

            // 检查getStatus返回格式
            if (service.getStatus && typeof service.getStatus === 'function') {
                try {
                    const status = service.getStatus();
                    const requiredFields = ['name', 'initialized', 'state', 'version'];
                    requiredFields.forEach(field => {
                        if (!(field in status)) {
                            result.warnings.push(`getStatus缺少字段: ${field}`);
                        }
                    });
                } catch (error) {
                    result.errors.push(`getStatus方法执行失败: ${error.message}`);
                }
            }

            return result;
        },

        /**
         * 验证所有已注册的服务
         * @returns {Object} 验证报告
         */
        validateAllServices() {
            const report = {
                timestamp: new Date().toISOString(),
                totalServices: 0,
                validServices: 0,
                invalidServices: 0,
                results: []
            };

            // 获取服务配置
            const servicesConfig = window.OTA?.ServicesConfig || window.ServicesConfig;
            if (!servicesConfig || !servicesConfig.services) {
                report.error = '微服务配置不可用';
                return report;
            }

            Object.keys(servicesConfig.services).forEach(serviceName => {
                report.totalServices++;
                
                try {
                    const service = window.OTA?.services?.[serviceName];
                    if (service) {
                        const validation = this.validateService(service, serviceName);
                        report.results.push(validation);
                        
                        if (validation.valid) {
                            report.validServices++;
                        } else {
                            report.invalidServices++;
                        }
                    } else {
                        report.results.push({
                            valid: false,
                            errors: ['服务未加载'],
                            warnings: [],
                            serviceName
                        });
                        report.invalidServices++;
                    }
                } catch (error) {
                    report.results.push({
                        valid: false,
                        errors: [`验证异常: ${error.message}`],
                        warnings: [],
                        serviceName
                    });
                    report.invalidServices++;
                }
            });

            return report;
        }
    };

    // 暴露到全局命名空间
    window.OTA.interfaces = {
        MicroserviceInterface,
        ServiceStates,
        CoreServiceAPIs,
        GlobalAPIs,
        InterfaceValidator
    };

    // 提供便捷的验证函数
    window.OTA.validateServiceInterfaces = function() {
        return InterfaceValidator.validateAllServices();
    };

    // 日志记录
    console.log('✅ API接口定义已加载');

})();
