# OTA系统微服务使用指南

## 📋 文档概述

**更新日期**: 2025-08-02
**版本**: v1.0 (微服务化架构)
**状态**: 微服务使用指南完成

## 🎯 微服务概览

OTA系统包含5个核心微服务，每个微服务负责特定的业务功能：

| 微服务 | 文件路径 | 主要职责 | 依赖关系 |
|--------|----------|----------|----------|
| UIService | `js/services/ui-service.js` | UI管理和用户交互 | logger |
| FormService | `js/services/form-service.js` | 表单处理和数据验证 | uiService, logger |
| PriceService | `js/services/price-service.js` | 价格计算和货币转换 | logger |
| StateService | `js/services/state-service.js` | UI状态和主题管理 | uiService, logger |
| EventService | `js/services/event-service.js` | 事件处理和DOM交互 | logger |

## 🎨 UIService - UI管理微服务

### 功能概述
UIService负责所有用户界面相关的操作，包括消息提示、加载状态、登录界面更新等。

### 核心功能

#### 1. 消息提示系统
```javascript
const uiService = window.OTA.getService('uiService');

// 成功消息
uiService.showAlert('订单创建成功', 'success');

// 错误消息
uiService.showError('网络连接失败，请重试');

// 警告消息
uiService.showAlert('请填写必填字段', 'warning');

// 信息消息
uiService.showAlert('正在处理您的请求...', 'info');
```

#### 2. 加载状态管理
```javascript
// 显示加载状态
uiService.showLoading('正在创建订单...');

// 隐藏加载状态
uiService.hideLoading();

// 带自动隐藏的加载
uiService.showLoading('处理中...', 3000); // 3秒后自动隐藏
```

#### 3. 登录界面更新
```javascript
// 更新登录状态
uiService.updateLoginUI(true, {
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'admin'
});

// 登出状态
uiService.updateLoginUI(false);
```

### 配置选项
```javascript
// UIService配置
const uiConfig = {
    alertDuration: 5000,        // 默认提示持续时间
    loadingMinDuration: 500,    // 最小加载显示时间
    animationDuration: 300,     // 动画持续时间
    theme: 'fluent'            // UI主题
};

uiService.configure(uiConfig);
```

### 最佳实践
- 使用适当的消息类型提供清晰的用户反馈
- 长时间操作时显示加载状态
- 避免同时显示多个相同类型的消息

## 📝 FormService - 表单处理微服务

### 功能概述
FormService处理所有表单相关操作，包括数据填充、验证、选项填充等。

### 核心功能

#### 1. 智能表单填充
```javascript
const formService = window.OTA.getService('formService');

// 从AI解析数据填充表单
const orderData = {
    customer_name: 'John Doe',
    pickup_location: 'KLIA Terminal 1',
    destination: 'KL Sentral',
    pickup_date: '15-08-2025',
    pickup_time: '14:30',
    passenger_count: 2
};

formService.fillFormFromData(orderData);
```

#### 2. 表单验证
```javascript
// 验证表单数据
const formData = formService.getFormData();
const validation = formService.validateForm(formData);

if (!validation.isValid) {
    console.log('验证失败:', validation.errors);
    validation.errors.forEach(error => {
        uiService.showError(error);
    });
}
```

#### 3. 下拉选项填充
```javascript
// 填充车型选项
formService.populateCarTypeOptions();

// 填充语言选项
formService.populateLanguageOptions();

// 填充OTA渠道选项
formService.populateOtaChannelOptions();
```

### 字段映射规则
FormService自动处理API字段名到表单字段名的转换：

| API字段名 | 表单字段名 | 说明 |
|-----------|------------|------|
| customer_name | customerName | 客户姓名 |
| pickup_location | pickupLocation | 接送地点 |
| pickup_date | pickupDate | 接送日期 |
| pickup_time | pickupTime | 接送时间 |
| passenger_count | passengerCount | 乘客人数 |
| car_type_id | carTypeId | 车型ID |

### 配置选项
```javascript
const formConfig = {
    autoValidation: true,       // 自动验证
    realTimeValidation: false,  // 实时验证
    dateFormat: 'DD-MM-YYYY',  // 日期格式
    timeFormat: '24h',         // 时间格式
    currencySymbol: 'RM'       // 货币符号
};

formService.configure(formConfig);
```

## 💰 PriceService - 价格计算微服务

### 功能概述
PriceService处理价格计算、货币转换和价格显示格式化。

### 核心功能

#### 1. 货币转换
```javascript
const priceService = window.OTA.getService('priceService');

// 转换货币
const myrAmount = priceService.convertCurrency(100, 'USD', 'MYR');
console.log(`100 USD = ${myrAmount} MYR`);

// 支持的货币
const supportedCurrencies = ['MYR', 'USD', 'SGD', 'CNY'];
```

#### 2. 价格格式化
```javascript
// 格式化价格显示
const formattedPrice = priceService.formatPrice(150.5, 'MYR');
console.log(formattedPrice); // "RM 150.50"

// 自定义格式化
const customFormat = priceService.formatPrice(1000, 'USD', {
    decimals: 0,
    thousandsSeparator: ',',
    symbol: '$'
});
console.log(customFormat); // "$1,000"
```

#### 3. 价格计算
```javascript
// 计算总价
const basePrice = 100;
const extras = [20, 15, 10]; // 额外费用
const total = priceService.calculateTotal(basePrice, extras);

// 应用折扣
const discountedPrice = priceService.applyDiscount(total, 0.1); // 10%折扣
```

### 汇率配置
```javascript
const exchangeRates = {
    'MYR': 1.0,     // 基准货币
    'USD': 4.3,     // 1 USD = 4.3 MYR
    'SGD': 3.4,     // 1 SGD = 3.4 MYR
    'CNY': 0.615    // 1 CNY = 0.615 MYR
};

priceService.updateExchangeRates(exchangeRates);
```

## 🎛️ StateService - 状态管理微服务

### 功能概述
StateService管理UI状态、主题切换和界面状态同步。

### 核心功能

#### 1. 主题管理
```javascript
const stateService = window.OTA.getService('stateService');

// 切换主题
stateService.setTheme('dark');
stateService.setTheme('light');
stateService.setTheme('auto'); // 跟随系统

// 获取当前主题
const currentTheme = stateService.getCurrentTheme();
```

#### 2. UI状态管理
```javascript
// 设置UI状态
stateService.setUIState('loading', true);
stateService.setUIState('formValid', false);
stateService.setUIState('userLoggedIn', true);

// 获取UI状态
const isLoading = stateService.getUIState('loading');
const isFormValid = stateService.getUIState('formValid');
```

#### 3. 状态监听
```javascript
// 监听状态变化
stateService.onStateChange('theme', (newTheme) => {
    console.log('主题已切换到:', newTheme);
    // 更新UI样式
});

stateService.onStateChange('userLoggedIn', (isLoggedIn) => {
    if (isLoggedIn) {
        // 显示用户界面
    } else {
        // 显示登录界面
    }
});
```

## ⚡ EventService - 事件处理微服务

### 功能概述
EventService处理DOM事件、键盘快捷键和事件委托。

### 核心功能

#### 1. 事件处理器管理
```javascript
const eventService = window.OTA.getService('eventService');

// 添加事件处理器
const handlerId = eventService.addEventHandler(
    document.getElementById('submitBtn'),
    'click',
    (event) => {
        console.log('提交按钮被点击');
        // 处理提交逻辑
    },
    { once: true } // 只执行一次
);

// 移除事件处理器
eventService.removeEventHandler(handlerId);
```

#### 2. 键盘快捷键
```javascript
// 设置键盘快捷键
eventService.setupKeyboardShortcuts({
    'Ctrl+S': () => {
        console.log('保存快捷键');
        // 保存操作
    },
    'Ctrl+Enter': () => {
        console.log('提交快捷键');
        // 提交表单
    },
    'Escape': () => {
        console.log('取消操作');
        // 取消当前操作
    }
});
```

#### 3. 防抖处理
```javascript
// 防抖事件处理
const debouncedHandler = eventService.debounce((event) => {
    console.log('搜索输入:', event.target.value);
    // 执行搜索
}, 300); // 300ms防抖

eventService.addEventHandler(
    document.getElementById('searchInput'),
    'input',
    debouncedHandler
);
```

### 事件配置
```javascript
const eventConfig = {
    debounceDelay: 300,         // 默认防抖延迟
    throttleDelay: 100,         // 默认节流延迟
    enableKeyboardShortcuts: true, // 启用键盘快捷键
    preventDefaultKeys: ['F5', 'Ctrl+R'] // 阻止默认行为的按键
};

eventService.configure(eventConfig);
```

## 🔧 微服务集成使用

### 完整使用示例
```javascript
// 完整的订单处理流程
async function processOrder() {
    const uiService = window.OTA.getService('uiService');
    const formService = window.OTA.getService('formService');
    const priceService = window.OTA.getService('priceService');
    
    try {
        // 1. 显示加载状态
        uiService.showLoading('正在处理订单...');
        
        // 2. 获取并验证表单数据
        const formData = formService.getFormData();
        const validation = formService.validateForm(formData);
        
        if (!validation.isValid) {
            throw new Error('表单验证失败: ' + validation.errors.join(', '));
        }
        
        // 3. 计算价格
        const totalPrice = priceService.calculateTotal(
            formData.basePrice,
            formData.extras
        );
        
        // 4. 提交订单
        const result = await submitOrder({
            ...formData,
            totalPrice
        });
        
        // 5. 显示成功消息
        uiService.hideLoading();
        uiService.showAlert('订单创建成功', 'success');
        
    } catch (error) {
        // 6. 错误处理
        uiService.hideLoading();
        uiService.showError('订单处理失败: ' + error.message);
    }
}
```

### 服务间通信
```javascript
// 服务间通信示例
const formService = window.OTA.getService('formService');
const uiService = window.OTA.getService('uiService');

// FormService通知UIService显示验证错误
formService.onValidationError = (errors) => {
    errors.forEach(error => {
        uiService.showError(error);
    });
};

// UIService通知FormService重置表单
uiService.onFormReset = () => {
    formService.resetForm();
};
```

---

**文档维护**: 随微服务演进持续更新
**最后更新**: 2025-08-02 (微服务化架构重构完成)
**使用指南版本**: v1.0
