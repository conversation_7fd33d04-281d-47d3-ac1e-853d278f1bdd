/**
 * 文件: kimi-service.js
 * 路径: js\ai\kimi-service.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.OTA.container
 * - window.OTA.container.register
 * - fetch (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.OTA
 * - window.OTA.kimiService
 * - window.OTA.getKimiService
 * - window.kimiService
 * - window.getKimiService
 * 
 * === 类声明 ===
 * - class KimiService
 * 
 * === 函数声明 ===
 * - function getKimiService()
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.582Z
 */

/**
 * @OTA_SERVICE Kimi AI服务模块
 * 负责与Kimi (Moonshot) API的交互，提供订单内容智能解析功能
 * 基于官方API文档: https://platform.moonshot.cn/docs/introduction#文本生成模型
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

class KimiService {
    constructor() {
        // API配置 - 优先使用统一管理器，降级到硬编码
        this.apiKey = window.OTA?.apiKeyManager?.getApiKey('kimi') ||
                      'sk-JcWzWW8FEnbrEvmvIY6N4vbcFzPxqXJBiFSbqej3Ff2uJBsY';
        this.baseURL = 'https://api.moonshot.cn/v1/chat/completions';
        this.timeout = 30000;
        
        // 使用推荐的moonshot-v1-8k模型
        this.modelVersion = 'moonshot-v1-8k';
        
        // 默认API配置
        this.defaultAPIConfig = {
            temperature: 0.1,
            max_tokens: 2048,
            top_p: 1,
            presence_penalty: 0,
            frequency_penalty: 0
        };
        
        // 获取logger实例
        this.logger = getLogger();
        
        // 分析状态管理
        this.analysisState = {
            isAnalyzing: false,
            lastAnalyzedText: '',
            currentRequest: null,
            analysisHistory: []
        };
        
        // 与GeminiService保持一致的提示词模板
        this.promptTemplates = {
            systemRole: {
                base: `你是一个高度智能的、专为马来西亚和新加坡设计的用车服务订单处理引擎。`,
                task: `你的 **唯一任务** 是精确地、无逻辑遗漏地解析非结构化订单文本，并根据下方定义的规则，直接计算并输出一个 **JSON数组**。`,
                format: `即使只有一个订单，也必须以数组形式 \`[ { ... } ]\` 返回。`
            },
            
            jsonSchema: {
                header: `### **输出格式契约 (JSON Schema)**\n\n你必须严格按照以下JSON结构输出。**所有未在文本中找到对应信息的字段，其值必须为 \`null\`**，不能省略字段。`,
                structure: `\`\`\`json
[
  {
    "customer_name": "string | null",
    "customer_contact": "string | null", 
    "customer_email": "string | null",
    "ota_reference_number": "string | null",
    "flight_info": "string | null",
    "departure_time": "string | null",
    "arrival_time": "string | null",
    "flight_type": "string ('Arrival' or 'Departure') | null",
    "pickup_date": "string (YYYY-MM-DD) | null",
    "pickup_time": "string (HH:MM) | null",
    "pickup": "string | null",
    "dropoff": "string | null",
    "passenger_count": "number | null",
    "luggage_count": "number | null",
    "sub_category_id": "number | null",
    "car_type_id": "number | null",
    "driving_region_id": "number | null",
    "languages_id_array": "array of numbers | null",
    "baby_chair": "boolean | null",
    "tour_guide": "boolean | null",
    "meet_and_greet": "boolean | null",
    "needs_paging_service": "boolean | null",
    "price": "number | null",
    "currency": "string ('MYR' | 'USD' | 'CNY') | null",
    "extra_requirement": "string | null"
  }
]
\`\`\``
            }
        };
    }

    /**
     * 发送请求到Kimi API
     * @param {object} requestData - 请求数据
     * @returns {Promise<object>} 响应数据
     */
    async sendRequest(requestData) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        const startTime = Date.now();
        
        try {
            this.logger.log('Kimi API请求开始', 'info', {
                model: this.modelVersion,
                messagesCount: requestData.messages?.length
            });
            
            const response = await fetch(this.baseURL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify({
                    model: this.modelVersion,
                    ...this.defaultAPIConfig,
                    ...requestData
                }),
                signal: controller.signal
            });
            
            const duration = Date.now() - startTime;
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                const errorText = await response.text();
                let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                
                try {
                    const errorData = JSON.parse(errorText);
                    errorMessage = errorData.error?.message || errorMessage;
                } catch (e) {
                    // 如果无法解析错误响应，使用默认错误消息
                }
                
                throw new Error(errorMessage);
            }
            
            const data = await response.json();
            
            this.logger.log('Kimi API请求完成', 'success', {
                duration,
                usage: data.usage,
                model: data.model
            });
            
            return data;
            
        } catch (error) {
            const duration = Date.now() - startTime;
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                this.logger.log('Kimi API请求超时', 'error', { duration });
                throw new Error('请求超时');
            }
            
            this.logger.log('Kimi API请求失败', 'error', {
                error: error.message,
                duration,
                model: this.modelVersion
            });
            
            throw error;
        }
    }

    /**
     * 解析订单文本
     * @param {string} orderText - 订单文本
     * @param {object} options - 解析选项
     * @returns {Promise<object>} 解析结果
     */
    async parseOrderText(orderText, options = {}) {
        try {
            const startTime = Date.now();
            this.analysisState.isAnalyzing = true;
            
            if (!orderText || orderText.trim().length === 0) {
                throw new Error('订单文本不能为空');
            }
            
            // 构建提示词
            const systemPrompt = this.buildSystemPrompt(options);
            const userPrompt = `请解析以下订单文本：\n\n${orderText}`;
            
            // 发送请求
            const requestData = {
                messages: [
                    {
                        role: 'system',
                        content: systemPrompt
                    },
                    {
                        role: 'user', 
                        content: userPrompt
                    }
                ]
            };
            
            const response = await this.sendRequest(requestData);
            const duration = Date.now() - startTime;
            
            // 解析响应
            const content = response.choices?.[0]?.message?.content;
            if (!content) {
                throw new Error('API响应格式错误');
            }
            
            // 提取JSON内容
            const parsedOrders = this.extractAndParseJSON(content);
            
            const result = {
                success: true,
                orders: parsedOrders,
                metadata: {
                    model: response.model,
                    usage: response.usage,
                    duration,
                    processingTime: duration,
                    orderCount: parsedOrders.length
                },
                rawResponse: content
            };
            
            this.logger.logUserAction('Kimi订单解析完成', {
                orderCount: parsedOrders.length,
                duration,
                inputLength: orderText.length
            });
            
            return result;
            
        } catch (error) {
            this.logger.log('Kimi订单解析失败', 'error', {
                error: error.message,
                inputText: orderText.substring(0, 200) + '...'
            });
            
            return {
                success: false,
                error: error.message,
                orders: [],
                metadata: {
                    duration: Date.now() - Date.now(),
                    processingTime: 0,
                    orderCount: 0
                }
            };
        } finally {
            this.analysisState.isAnalyzing = false;
        }
    }

    /**
     * 构建系统提示词
     * @param {object} options - 选项
     * @returns {string} 系统提示词
     */
    buildSystemPrompt(options = {}) {
        const templates = this.promptTemplates;
        
        let prompt = [
            templates.systemRole.base,
            templates.systemRole.task,
            templates.systemRole.format,
            '',
            templates.jsonSchema.header,
            templates.jsonSchema.structure
        ].join('\n');
        
        // 添加字段解析规则
        prompt += '\n\n### **字段解析规则**\n\n';
        prompt += this.getFieldParsingRules();
        
        // 添加示例
        if (options.includeExamples !== false) {
            prompt += '\n\n### **解析示例**\n\n';
            prompt += this.getParsingExamples();
        }
        
        return prompt;
    }

    /**
     * 获取字段解析规则
     * @returns {string} 字段解析规则
     */
    getFieldParsingRules() {
        return `
**子分类映射（sub_category_id）：**
- 接机/Airport Pickup → 2
- 送机/Airport Dropoff → 3  
- 包车/Charter → 4

**车型推荐（car_type_id）：**
- 1-3人 → 5 (5 Seater)
- 4-5人 → 15 (7 Seater MPV)
- 6-7人 → 20 (10 Seater MPV)
- 8-10人 → 23 (14 Seater Van)

**地区映射（driving_region_id）：**
- 吉隆坡/雪兰莪/KL → 1
- 槟城/Penang → 2
- 新山/Johor → 3
- 沙巴/Sabah → 4
- 新加坡/Singapore → 5

**语言映射（languages_id_array）：**
- 英文/English → [2]
- 中文/Chinese → [4] 
- 马来文/Malay → [3]

**日期时间格式：**
- pickup_date: YYYY-MM-DD
- pickup_time: HH:MM (24小时制)

**OTA参考号识别：**
- 通常为6-15位字母数字组合
- 排除客户姓名、电话、地址等信息`;
    }

    /**
     * 获取解析示例
     * @returns {string} 解析示例
     */
    getParsingExamples() {
        return `
**示例输入：**
"接机服务 客户：张三 电话：+60123456789 航班：MH123 12月25日 14:30 KLIA2 到酒店 2人 1件行李 参考号：CD123456"

**示例输出：**
\`\`\`json
[{
  "customer_name": "张三",
  "customer_contact": "+60123456789",
  "customer_email": null,
  "ota_reference_number": "CD123456", 
  "flight_info": "MH123",
  "pickup_date": "2024-12-25",
  "pickup_time": "14:30",
  "pickup": "KLIA2",
  "dropoff": "酒店",
  "passenger_count": 2,
  "luggage_count": 1,
  "sub_category_id": 2,
  "car_type_id": 5,
  "driving_region_id": 1,
  "languages_id_array": [4],
  "flight_type": "Arrival",
  "baby_chair": null,
  "tour_guide": null,
  "meet_and_greet": null,
  "needs_paging_service": null,
  "price": null,
  "currency": null,
  "extra_requirement": null
}]
\`\`\``;
    }

    /**
     * 提取并解析JSON内容
     * @param {string} content - API响应内容
     * @returns {Array} 解析后的订单数组
     */
    extractAndParseJSON(content) {
        try {
            // 尝试直接解析
            const parsed = JSON.parse(content);
            if (Array.isArray(parsed)) {
                return parsed;
            }
            return [parsed];
        } catch (e) {
            // 尝试从markdown代码块中提取
            const jsonMatch = content.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
            if (jsonMatch) {
                try {
                    const parsed = JSON.parse(jsonMatch[1]);
                    return Array.isArray(parsed) ? parsed : [parsed];
                } catch (e2) {
                    this.logger.log('JSON解析失败', 'error', { content, error: e2.message });
                }
            }
            
            // 尝试查找JSON数组模式
            const arrayMatch = content.match(/\[\s*\{[\s\S]*\}\s*\]/);
            if (arrayMatch) {
                try {
                    const parsed = JSON.parse(arrayMatch[0]);
                    return Array.isArray(parsed) ? parsed : [parsed];
                } catch (e3) {
                    this.logger.log('数组JSON解析失败', 'error', { content, error: e3.message });
                }
            }
            
            throw new Error('无法从响应中提取有效的JSON格式');
        }
    }

    /**
     * 获取服务状态
     * @returns {object} 服务状态
     */
    getStatus() {
        return {
            serviceName: 'KimiService',
            model: this.modelVersion,
            isAnalyzing: this.analysisState.isAnalyzing,
            apiKey: this.apiKey ? '已配置' : '未配置',
            lastAnalysis: this.analysisState.lastAnalyzedText ? '有历史记录' : '无历史记录'
        };
    }

    /**
     * 清理分析状态
     */
    clearAnalysisState() {
        this.analysisState.lastAnalyzedText = '';
        this.analysisState.analysisHistory = [];
        this.analysisState.currentRequest = null;
    }
}

    // 创建全局Kimi服务实例（单例模式）
    let kimiServiceInstance = null;

    /**
     * 获取Kimi服务实例
     * @returns {KimiService} Kimi服务实例
     */
    function getKimiService() {
        if (!kimiServiceInstance) {
            kimiServiceInstance = new KimiService();
        }
        return kimiServiceInstance;
    }

    // 暴露到OTA命名空间
    window.OTA.kimiService = getKimiService();
    window.OTA.getKimiService = getKimiService;

    // 向后兼容：暴露到全局window对象
    window.kimiService = getKimiService();
    window.getKimiService = getKimiService;

    // 🔧 方案A：统一注册到dependency-container系统
    // 移除ota-registry注册，确保架构统一
    if (window.OTA && window.OTA.container) {
        window.OTA.container.register('kimiService', () => getKimiService(), { singleton: true });
    }

})();