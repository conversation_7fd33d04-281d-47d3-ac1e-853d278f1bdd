/**
 * 文件: warning-manager.js
 * 路径: js\core\warning-manager.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - console (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.OTA
 * - window.OTA.core
 * - window.OTA.core.warningManager
 * - window.OTA.warn
 * - window.getWarningManager
 * 
 * === 类声明 ===
 * - class WarningManager
 * 
 * === 函数声明 ===
 * - function warn()
 * - function getWarningManager()
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.669Z
 */

/**
 * 简化警告管理器
 * 提供基础的警告输出功能，支持频率控制
 *
 * 🗑️ 减法重构优化：移除复杂的环境检测、批量处理等功能
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 简化警告管理器类
     */
    class WarningManager {
        constructor() {
            // 警告计数器
            this.warningCounts = new Map();

            // 简化配置
            this.config = {
                // 每个警告类型的最大显示次数
                maxWarningsPerType: 3,

                // 警告级别配置
                levels: {
                    CRITICAL: { prefix: '🚨' },
                    WARNING: { prefix: '⚠️' },
                    INFO: { prefix: 'ℹ️' },
                    DEBUG: { prefix: '🔧' }
                }
            };

            console.log('✅ 简化警告管理器已初始化');
        }

        /**
         * 发出警告
         * @param {string} type - 警告类型
         * @param {string} message - 警告消息
         * @param {string} level - 警告级别
         * @param {Object} data - 附加数据
         */
        warn(type, message, level = 'WARNING', data = {}) {
            // 频率控制检查
            const warningKey = `${type}:${level}`;
            const currentCount = this.warningCounts.get(warningKey) || 0;

            if (currentCount >= this.config.maxWarningsPerType) {
                return; // 超过最大次数，不再显示
            }

            // 更新计数器
            this.warningCounts.set(warningKey, currentCount + 1);

            // 输出警告
            const levelConfig = this.config.levels[level] || this.config.levels.WARNING;
            const prefix = levelConfig.prefix;
            let fullMessage = `${prefix} [${type}] ${message}`;

            // 添加频率信息
            if (currentCount + 1 === this.config.maxWarningsPerType) {
                fullMessage += ` (最后一次警告，后续将被抑制)`;
            } else if (currentCount > 0) {
                fullMessage += ` (${currentCount + 1}/${this.config.maxWarningsPerType})`;
            }

            // 输出到控制台
            if (level === 'CRITICAL') {
                console.error(fullMessage, data);
            } else if (level === 'WARNING') {
                console.warn(fullMessage, data);
            } else {
                console.log(fullMessage, data);
            }
        }
    }

    // 创建全局唯一的警告管理器实例
    const warningManager = new WarningManager();

    // 暴露到OTA命名空间
    window.OTA.core.warningManager = warningManager;

    // 提供便捷的全局函数
    window.OTA.warn = function(type, message, level, data) {
        return warningManager.warn(type, message, level, data);
    };

    // 向后兼容：暴露到全局（带废弃警告）
    window.getWarningManager = function() {
        warningManager.warn('DEPRECATED_API', 'window.getWarningManager() 已废弃，请使用 window.OTA.core.warningManager', 'WARNING');
        return warningManager;
    };

    console.log('✅ 简化警告管理器模块已加载');

})();
