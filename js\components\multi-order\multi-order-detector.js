/**
 * 文件: multi-order-detector.js
 * 路径: js\components\multi-order\multi-order-detector.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.getLogger
 * - window.FIELD_MAPPING_CONFIG
 * - window.getGeminiService
 * - console (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.OTA
 * - window.OTA.MultiOrderDetector
 * - window.MultiOrderDetector
 * 
 * === 类声明 ===
 * - class MultiOrderDetector
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.607Z
 */

/**
 * @OTA_DETECTOR 多订单检测服务
 * 🏷️ 标签: @OTA_MULTI_ORDER_DETECTOR
 * 📝 说明: 负责AI检测和文本分析逻辑，识别多订单内容
 * ⚠️ 警告: 已注册，请勿重复开发
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.MultiOrderDetector) {
    console.log('多订单检测服务已存在，跳过重复加载');
} else {

/**
 * 多订单检测服务类
 * 提供AI检测和传统检测功能
 */
class MultiOrderDetector {
    constructor(config = {}) {
        this.config = {
            minInputLength: config.minInputLength || 50,
            confidenceThreshold: config.confidenceThreshold || 0.7,
            debounceDelay: config.debounceDelay || 1200,
            ...config
        };
        
        this.logger = this.getLogger();
    }

    /**
     * 获取日志服务
     * @returns {Object} 日志服务实例
     */
    getLogger() {
        return window.getLogger?.() || {
            log: console.log.bind(console),
            logError: console.error.bind(console)
        };
    }

    /**
     * 统一多订单检测入口
     * @param {string} text - 输入文本
     * @param {Object} options - 检测选项
     * @returns {Promise<Object>} 检测结果
     */
    async detectMultiOrder(text, options = {}) {
        const { forceDetection = false, source = 'auto' } = options;

        // 基本验证
        if (!forceDetection && (!text || text.trim().length < this.config.minInputLength)) {
            return {
                isMultiOrder: false,
                orderCount: 0,
                confidence: 0,
                reason: '文本长度不足',
                orders: []
            };
        }

        this.logger?.log(`🔍 开始多订单检测，文本长度: ${text.length}字符`, 'info');

        try {
            // 优先使用AI检测
            const aiResult = await this.detectMultiOrderAI(text);
            
            if (aiResult.isMultiOrder && aiResult.confidence > this.config.confidenceThreshold) {
                this.logger?.log(`✅ AI检测确认多订单: ${aiResult.orderCount}个订单`, 'success');
                return aiResult;
            }

            // AI检测不确定时，使用传统检测作为补充
            const traditionalResult = this.detectMultiOrderTraditional(text);
            
            if (traditionalResult.isMultiOrder && traditionalResult.confidence > 0.5) {
                this.logger?.log(`✅ 传统检测确认多订单`, 'info');
                // 结合AI和传统检测结果
                return {
                    ...aiResult,
                    isMultiOrder: true,
                    confidence: Math.max(aiResult.confidence, traditionalResult.confidence),
                    reason: `AI检测: ${aiResult.reason}; 传统检测: ${traditionalResult.reason}`
                };
            }

            this.logger?.log(`📋 检测为单订单`, 'info');
            return {
                isMultiOrder: false,
                orderCount: 1,
                confidence: Math.max(aiResult.confidence, traditionalResult.confidence),
                reason: `AI和传统检测都未确认多订单`,
                orders: aiResult.orders || []
            };

        } catch (error) {
            this.logger?.logError('多订单检测失败', error);
            return {
                isMultiOrder: false,
                orderCount: 0,
                confidence: 0,
                reason: `检测失败: ${error.message}`,
                orders: []
            };
        }
    }

    /**
     * AI多订单检测（调用Gemini服务）
     * @param {string} text - 输入文本
     * @returns {Promise<Object>} AI检测结果
     */
    async detectMultiOrderAI(text) {
        try {
            const geminiService = this.getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini AI服务不可用');
            }

            // 获取Gemini配置
            const geminiConfig = this.getGeminiConfig();
            
            // 调用Gemini服务进行检测和解析
            const result = await geminiService.detectAndSplitMultiOrdersWithVerification(text, geminiConfig);
            
            // 确保返回的订单数据格式正确
            if (result.orders && Array.isArray(result.orders)) {
                result.orders = result.orders.map(order => this.normalizeOrderFields(order));
            }

            return {
                isMultiOrder: result.isMultiOrder || false,
                orderCount: result.orderCount || (result.orders ? result.orders.length : 1),
                confidence: result.confidence || 0,
                reason: result.analysis || 'Gemini AI分析',
                orders: result.orders || [],
                rawResult: result
            };

        } catch (error) {
            this.logger?.logError('AI多订单检测失败', error);
            return {
                isMultiOrder: false,
                orderCount: 1,
                confidence: 0,
                reason: `AI检测失败: ${error.message}`,
                orders: []
            };
        }
    }

    /**
     * 传统多订单检测（基于模式匹配）
     * @param {string} text - 输入文本
     * @returns {Object} 检测结果
     */
    detectMultiOrderTraditional(text) {
        if (!text || typeof text !== 'string') {
            return { isMultiOrder: false, confidence: 0, reason: '文本无效' };
        }

        const cleanText = text.trim();
        let score = 0;
        const reasons = [];

        // 1. 检查明确的订单标识
        const orderMarkers = [
            /订单\s*[：:]\s*\d+/gi,
            /order\s*[：:]\s*\d+/gi,
            /第\s*\d+\s*个?订单/gi,
            /\d+\s*[、.]\s*订单/gi
        ];
        
        for (const pattern of orderMarkers) {
            const matches = cleanText.match(pattern);
            if (matches && matches.length > 1) {
                score += 40;
                reasons.push(`发现${matches.length}个订单标识`);
                break;
            }
        }

        // 2. 检查数字列表模式
        const numberListPattern = /^\s*\d+\s*[、.]/gm;
        const numberMatches = cleanText.match(numberListPattern);
        if (numberMatches && numberMatches.length > 1) {
            score += 30;
            reasons.push(`发现${numberMatches.length}个数字列表项`);
        }

        // 3. 检查日期时间密度
        const dateTimePatterns = [
            /\d{4}[-\/]\d{1,2}[-\/]\d{1,2}/g,
            /\d{1,2}[:：]\d{2}/g,
            /(今天|明天|后天|昨天)/g,
            /(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)/gi,
            /(周一|周二|周三|周四|周五|周六|周日)/g
        ];
        
        let totalDateTimeMatches = 0;
        dateTimePatterns.forEach(pattern => {
            const matches = cleanText.match(pattern) || [];
            totalDateTimeMatches += matches.length;
        });
        
        if (totalDateTimeMatches > 2) {
            score += 20;
            reasons.push(`发现${totalDateTimeMatches}个时间日期`);
        }

        // 4. 检查分隔符
        const separators = [
            /\n\s*[-=]{3,}\s*\n/g,
            /\n\s*\n\s*\n/g,
            /={3,}/g,
            /-{3,}/g
        ];
        
        for (const pattern of separators) {
            const matches = cleanText.match(pattern);
            if (matches && matches.length > 0) {
                score += 15;
                reasons.push('发现明确分隔符');
                break;
            }
        }

        // 5. 检查多个客户姓名
        const namePatterns = [
            /姓名\s*[：:]\s*([^\n\r]+)/gi,
            /客户\s*[：:]\s*([^\n\r]+)/gi,
            /乘客\s*[：:]\s*([^\n\r]+)/gi
        ];
        
        let nameCount = 0;
        namePatterns.forEach(pattern => {
            const matches = cleanText.match(pattern) || [];
            nameCount += matches.length;
        });
        
        if (nameCount > 1) {
            score += 25;
            reasons.push(`发现${nameCount}个客户姓名`);
        }

        // 6. 文本长度和复杂度分析
        const lines = cleanText.split('\n').filter(line => line.trim().length > 10);
        if (lines.length > 8) {
            score += 10;
            reasons.push(`文本有${lines.length}行，可能包含多个订单`);
        }

        const confidence = Math.min(score / 100, 1);
        const isMultiOrder = confidence > 0.3;

        return {
            isMultiOrder,
            confidence,
            score,
            orderCount: isMultiOrder ? Math.max(2, Math.floor(confidence * 5)) : 1,
            reason: reasons.join('; ') || '未发现多订单特征'
        };
    }

    /**
     * 智能分割订单文本
     * @param {string} text - 输入文本
     * @returns {Promise<Array>} 分割后的订单对象数组
     */
    async smartSplitOrderText(text) {
        if (!text || typeof text !== 'string') {
            return [text || ''];
        }

        try {
            const geminiService = this.getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini AI服务不可用');
            }
            
            // 使用Gemini AI进行智能分割
            const result = await geminiService.detectAndSplitMultiOrdersWithVerification(text, this.getGeminiConfig());
            
            // 标准化订单字段
            const normalizedOrders = (result.orders || []).map(order => this.normalizeOrderFields(order));
            
            this.logger?.log(`📋 智能分割完成: ${normalizedOrders.length} 个订单`, 'success');
            
            return normalizedOrders.length > 0 ? normalizedOrders : [text];
            
        } catch (error) {
            this.logger?.logError('智能分割失败，返回原文本', error);
            return [text];
        }
    }

    /**
     * 标准化订单字段（处理Gemini返回的字段格式）
     * @param {Object} order - 原始订单对象
     * @returns {Object} 标准化后的订单对象
     */
    normalizeOrderFields(order) {
        if (!order || typeof order !== 'object') {
            return order;
        }

        const config = window.OTA?.FieldMappingConfig || window.FIELD_MAPPING_CONFIG;
        if (!config) {
            this.logger?.log('字段映射配置未加载，跳过字段标准化', 'warn');
            return order;
        }

        const normalizedOrder = { ...order };

        // 应用AI到前端的字段映射
        Object.entries(config.AI_TO_FRONTEND).forEach(([aiField, frontendField]) => {
            if (order.hasOwnProperty(aiField) && !normalizedOrder.hasOwnProperty(frontendField)) {
                normalizedOrder[frontendField] = order[aiField];
                // 保留原始字段以便调试
                // delete normalizedOrder[aiField];
            }
        });

        // 保存原始文本
        if (!normalizedOrder.rawText && order.rawText) {
            normalizedOrder.rawText = order.rawText;
        }

        return normalizedOrder;
    }

    /**
     * 分析时间点和航班特征
     * @param {string} text - 输入文本
     * @returns {Object} 时间分析结果
     */
    analyzeTimePointsAndFlights(text) {
        const analysis = {
            timePoints: [],
            flightNumbers: [],
            dateCount: 0,
            timeCount: 0,
            hasMultipleTimePoints: false
        };

        // 提取日期
        const datePatterns = [
            /\d{4}[-\/]\d{1,2}[-\/]\d{1,2}/g,
            /\d{1,2}[-\/]\d{1,2}[-\/]\d{4}/g
        ];
        
        datePatterns.forEach(pattern => {
            const matches = text.match(pattern) || [];
            analysis.dateCount += matches.length;
            analysis.timePoints.push(...matches);
        });

        // 提取时间
        const timePattern = /\d{1,2}[:：]\d{2}/g;
        const timeMatches = text.match(timePattern) || [];
        analysis.timeCount = timeMatches.length;
        analysis.timePoints.push(...timeMatches);

        // 提取航班号 - 使用增强的航班号识别模式
        const flightPatterns = [
            // 标准格式：2-3位字母 + 1-4位数字
            /[A-Z]{2,3}\d{1,4}[A-Z]?/g,
            // 分段航班：航班号/数字
            /[A-Z]{2,3}\d{1,4}\/\d{1,3}/g,
            // 带连字符的航班号
            /[A-Z]{2,3}-?\d{1,4}[A-Z]?/g,
            // 数字开头的航班号
            /[0-9][A-Z]\d{3,4}/g,
            // 明确标注的航班信息
            /航班\s*[：:]\s*([A-Z0-9\/\-]+)/gi,
            /Flight\s*[：:]\s*([A-Z0-9\/\-]+)/gi
        ];

        flightPatterns.forEach(pattern => {
            const matches = text.match(pattern) || [];
            if (pattern.source.includes('航班') || pattern.source.includes('Flight')) {
                // 对于明确标注的航班，提取括号内容
                matches.forEach(match => {
                    const extracted = match.match(/([A-Z0-9\/\-]+)$/i);
                    if (extracted) analysis.flightNumbers.push(extracted[1]);
                });
            } else {
                analysis.flightNumbers.push(...matches);
            }
        });

        analysis.hasMultipleTimePoints = analysis.timePoints.length > 2;

        return analysis;
    }

    /**
     * 获取Gemini服务实例
     * @returns {Object} Gemini服务实例
     */
    getGeminiService() {
        return window.getGeminiService?.() || null;
    }

    /**
     * 获取Gemini配置
     * @returns {Object} Gemini配置对象
     */
    getGeminiConfig() {
        return {
            confidenceThreshold: this.config.confidenceThreshold,
            maxRetries: 3,
            timeout: 30000
        };
    }
}

// 导出检测服务
window.OTA = window.OTA || {};
window.OTA.MultiOrderDetector = MultiOrderDetector;

// 向后兼容
window.MultiOrderDetector = MultiOrderDetector;

console.log('✅ 多订单检测服务已加载');

// 结束防重复加载检查
}
