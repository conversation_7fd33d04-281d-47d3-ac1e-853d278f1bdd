/**
 * 文件: multi-order-batch-manager.js
 * 路径: js\components\multi-order\multi-order-batch-manager.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.OTA.getService
 * - console (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.getMultiOrderBatchManager
 * - window.MultiOrderBatchManager
 * - window.OTA
 * - window.OTA
 * - window.OTA.multiOrder
 * - window.OTA.multiOrder
 * - window.OTA.multiOrder.BatchManager
 * - window.OTA.multiOrder.getBatchManager
 * 
 * === 类声明 ===
 * - class MultiOrderBatchManager
 * 
 * === 函数声明 ===
 * - function createMultiOrderBatchManager()
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.598Z
 */

/**
 * 🚀 多订单批量操作管理器
 * 负责处理多订单系统的批量处理、批量创建、批量验证和进度管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-20
 */

(function() {
    'use strict';

    /**
     * 多订单批量操作管理器类
     * 专门处理批量订单创建、批量设置、进度跟踪和错误处理
     */
    class MultiOrderBatchManager {
        /**
         * 构造函数
         * @param {Object} dependencies - 依赖注入对象
         * @param {Object} dependencies.logger - 日志服务
         * @param {Object} dependencies.config - 配置对象
         * @param {Object} dependencies.stateManager - 状态管理器
         * @param {Object} dependencies.validationManager - 验证管理器
         * @param {Object} dependencies.apiService - API服务
         */
        constructor(dependencies = {}) {
            this.logger = dependencies.logger || this.getLogger();
            this.config = {
                // 批量操作配置
                batchSize: 5,
                maxConcurrent: 3,
                retryAttempts: 3,
                retryDelay: 1000,
                progressUpdateInterval: 500,
                timeoutPerOrder: 30000,
                ...dependencies.config
            };

            // 依赖注入
            this.stateManager = dependencies.stateManager;
            this.validationManager = dependencies.validationManager;
            this.apiService = dependencies.apiService;

            // 批量操作状态
            this.batchState = {
                isRunning: false,
                currentBatch: [],
                processingQueue: [],
                completedOrders: [],
                failedOrders: [],
                retryQueue: []
            };

            // 进度跟踪
            this.progressTracker = {
                startTime: null,
                estimatedTimeRemaining: 0,
                averageProcessingTime: 0,
                successRate: 1.0
            };

            // 并发控制
            this.concurrencyController = {
                activePromises: new Set(),
                maxConcurrent: this.config.maxConcurrent
            };

            // 初始化
            this.init();
        }

        /**
         * 初始化批量操作管理器
         */
        init() {
            this.logger?.log('🚀 批量操作管理器初始化开始', 'info');
            
            try {
                // 设置进度更新定时器
                this.setupProgressUpdater();
                
                this.logger?.log('✅ 批量操作管理器初始化完成', 'success');
            } catch (error) {
                this.logger?.logError('批量操作管理器初始化失败', error);
                throw error;
            }
        }

        /**
         * 设置进度更新定时器
         */
        setupProgressUpdater() {
            if (this.config.progressUpdateInterval > 0) {
                this.progressUpdateTimer = setInterval(() => {
                    if (this.batchState.isRunning) {
                        this.updateProgressEstimation();
                    }
                }, this.config.progressUpdateInterval);
            }
        }

        /**
         * 批量创建订单
         * @param {Array} selectedOrders - 选中的订单数组
         * @param {Object} options - 批量创建选项
         * @returns {Promise<Object>} 批量创建结果
         */
        async batchCreateOrders(selectedOrders, options = {}) {
            if (this.batchState.isRunning) {
                throw new Error('批量操作正在进行中，请等待完成');
            }

            this.logger?.log(`开始批量创建订单，总数: ${selectedOrders.length}`, 'info');

            try {
                // 初始化批量状态
                this.initializeBatchState(selectedOrders);

                // 验证所有订单
                const validationResults = await this.batchValidateOrders(selectedOrders);
                if (validationResults.hasErrors) {
                    throw new Error(`订单验证失败: ${validationResults.errors.length} 个错误`);
                }

                // 开始批量处理
                const results = await this.processBatchOrders(selectedOrders, options);

                // 完成批量操作
                this.completeBatchOperation(results);

                return results;

            } catch (error) {
                this.handleBatchError(error);
                throw error;
            }
        }

        /**
         * 初始化批量状态
         * @param {Array} orders - 订单数组
         */
        initializeBatchState(orders) {
            this.batchState = {
                isRunning: true,
                currentBatch: [],
                processingQueue: [...orders],
                completedOrders: [],
                failedOrders: [],
                retryQueue: []
            };

            this.progressTracker = {
                startTime: Date.now(),
                estimatedTimeRemaining: 0,
                averageProcessingTime: 0,
                successRate: 1.0
            };

            // 通知状态管理器
            if (this.stateManager) {
                this.stateManager.initBatchProgress(orders.length);
            }
        }

        /**
         * 批量验证订单
         * @param {Array} orders - 订单数组
         * @returns {Promise<Object>} 验证结果
         */
        async batchValidateOrders(orders) {
            const errors = [];
            const warnings = [];

            for (let i = 0; i < orders.length; i++) {
                const order = orders[i];
                
                try {
                    // 使用验证管理器验证订单
                    if (this.validationManager) {
                        const validationResult = this.validationManager.validateAndFormatOrderFields(order);
                        
                        if (validationResult.errors && validationResult.errors.length > 0) {
                            errors.push({
                                orderIndex: i,
                                order,
                                errors: validationResult.errors
                            });
                        }

                        if (validationResult.warnings && validationResult.warnings.length > 0) {
                            warnings.push({
                                orderIndex: i,
                                order,
                                warnings: validationResult.warnings
                            });
                        }
                    }
                } catch (error) {
                    errors.push({
                        orderIndex: i,
                        order,
                        errors: [`验证过程中发生错误: ${error.message}`]
                    });
                }
            }

            return {
                hasErrors: errors.length > 0,
                hasWarnings: warnings.length > 0,
                errors,
                warnings,
                totalOrders: orders.length,
                validOrders: orders.length - errors.length
            };
        }

        /**
         * 处理批量订单
         * @param {Array} orders - 订单数组
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async processBatchOrders(orders, options = {}) {
            const results = {
                successful: [],
                failed: [],
                total: orders.length,
                startTime: Date.now(),
                endTime: null
            };

            // 分批处理
            const batches = this.createBatches(orders, this.config.batchSize);
            
            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                const batch = batches[batchIndex];
                this.batchState.currentBatch = batch;

                this.logger?.log(`处理批次 ${batchIndex + 1}/${batches.length}，订单数: ${batch.length}`, 'info');

                // 并发处理当前批次
                const batchResults = await this.processBatch(batch, options);
                
                // 合并结果
                results.successful.push(...batchResults.successful);
                results.failed.push(...batchResults.failed);

                // 更新进度
                this.updateBatchProgress(results);

                // 检查是否需要停止
                if (options.stopOnError && batchResults.failed.length > 0) {
                    this.logger?.log('检测到错误，停止批量处理', 'warn');
                    break;
                }

                // 批次间延迟
                if (batchIndex < batches.length - 1 && this.config.batchDelay) {
                    await this.delay(this.config.batchDelay);
                }
            }

            results.endTime = Date.now();
            results.duration = results.endTime - results.startTime;

            return results;
        }

        /**
         * 处理单个批次
         * @param {Array} batch - 批次订单数组
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 批次处理结果
         */
        async processBatch(batch, options = {}) {
            const batchResults = {
                successful: [],
                failed: []
            };

            // 创建并发处理Promise
            const promises = batch.map((order, index) => 
                this.processOrderWithConcurrencyControl(order, index, options)
            );

            // 等待所有Promise完成
            const results = await Promise.allSettled(promises);

            // 处理结果
            results.forEach((result, index) => {
                const order = batch[index];
                
                if (result.status === 'fulfilled') {
                    batchResults.successful.push({
                        order,
                        result: result.value,
                        index
                    });
                } else {
                    batchResults.failed.push({
                        order,
                        error: result.reason,
                        index
                    });
                }
            });

            return batchResults;
        }

        /**
         * 带并发控制的订单处理
         * @param {Object} order - 订单对象
         * @param {number} index - 订单索引
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async processOrderWithConcurrencyControl(order, index, options = {}) {
            // 等待并发槽位
            while (this.concurrencyController.activePromises.size >= this.concurrencyController.maxConcurrent) {
                await this.delay(100);
            }

            const promise = this.processOrder(order, index, options);
            this.concurrencyController.activePromises.add(promise);

            try {
                const result = await promise;
                return result;
            } finally {
                this.concurrencyController.activePromises.delete(promise);
            }
        }

        /**
         * 处理单个订单
         * @param {Object} order - 订单对象
         * @param {number} index - 订单索引
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async processOrder(order, index, options = {}) {
            const startTime = Date.now();
            
            try {
                // 更新当前处理状态
                if (this.stateManager) {
                    this.stateManager.updateBatchProgress({
                        processingOrder: index
                    });
                }

                // 调用API创建订单
                let result;
                if (this.apiService && this.apiService.createOrder) {
                    result = await this.apiService.createOrder(order);
                } else {
                    // 模拟API调用
                    await this.delay(1000 + Math.random() * 2000);
                    result = { success: true, orderId: `ORDER_${Date.now()}_${index}` };
                }

                const processingTime = Date.now() - startTime;
                this.updateProcessingMetrics(processingTime, true);

                this.logger?.log(`订单 ${index} 创建成功`, 'success', result);
                return result;

            } catch (error) {
                const processingTime = Date.now() - startTime;
                this.updateProcessingMetrics(processingTime, false);

                this.logger?.logError(`订单 ${index} 创建失败`, error);
                
                // 重试逻辑
                if (options.enableRetry && this.shouldRetry(error)) {
                    return this.retryOrder(order, index, options);
                }

                throw error;
            }
        }

        /**
         * 重试订单处理
         * @param {Object} order - 订单对象
         * @param {number} index - 订单索引
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 重试结果
         */
        async retryOrder(order, index, options = {}) {
            const maxRetries = options.maxRetries || this.config.retryAttempts;
            let retryCount = 0;

            while (retryCount < maxRetries) {
                retryCount++;

                this.logger?.log(`重试订单 ${index}，第 ${retryCount}/${maxRetries} 次`, 'warn');

                // 重试延迟
                await this.delay(this.config.retryDelay * retryCount);

                try {
                    const result = await this.processOrder(order, index, { ...options, enableRetry: false });
                    this.logger?.log(`订单 ${index} 重试成功`, 'success');
                    return result;
                } catch (error) {
                    this.logger?.logError(`订单 ${index} 重试失败 (${retryCount}/${maxRetries})`, error);

                    if (retryCount >= maxRetries) {
                        throw new Error(`订单 ${index} 重试 ${maxRetries} 次后仍然失败: ${error.message}`);
                    }
                }
            }
        }

        /**
         * 判断是否应该重试
         * @param {Error} error - 错误对象
         * @returns {boolean} 是否应该重试
         */
        shouldRetry(error) {
            // 网络错误或临时错误应该重试
            const retryableErrors = [
                'NetworkError',
                'TimeoutError',
                'ServiceUnavailable',
                'InternalServerError'
            ];

            return retryableErrors.some(errorType =>
                error.name === errorType ||
                error.message.includes(errorType) ||
                error.status >= 500
            );
        }

        /**
         * 创建批次
         * @param {Array} items - 要分批的项目
         * @param {number} batchSize - 批次大小
         * @returns {Array} 批次数组
         */
        createBatches(items, batchSize) {
            const batches = [];
            for (let i = 0; i < items.length; i += batchSize) {
                batches.push(items.slice(i, i + batchSize));
            }
            return batches;
        }

        /**
         * 更新批量处理进度
         * @param {Object} results - 当前结果
         */
        updateBatchProgress(results) {
            const completed = results.successful.length + results.failed.length;
            const total = results.total;

            if (this.stateManager) {
                this.stateManager.updateBatchProgress({
                    completed,
                    failed: results.failed.length
                });
            }

            // 更新进度跟踪
            this.updateProgressEstimation();

            this.logger?.log(`批量处理进度: ${completed}/${total} (${Math.round(completed/total*100)}%)`, 'info');
        }

        /**
         * 更新进度估算
         */
        updateProgressEstimation() {
            const now = Date.now();
            const elapsed = now - this.progressTracker.startTime;

            if (this.stateManager) {
                const progress = this.stateManager.getBatchProgress();
                const completed = progress.completed;
                const total = progress.total;

                if (completed > 0) {
                    // 计算平均处理时间
                    this.progressTracker.averageProcessingTime = elapsed / completed;

                    // 估算剩余时间
                    const remaining = total - completed;
                    this.progressTracker.estimatedTimeRemaining =
                        remaining * this.progressTracker.averageProcessingTime;

                    // 计算成功率
                    this.progressTracker.successRate =
                        (completed - progress.failed) / completed;
                }
            }
        }

        /**
         * 更新处理指标
         * @param {number} processingTime - 处理时间
         * @param {boolean} success - 是否成功
         */
        updateProcessingMetrics(processingTime, success) {
            // 更新平均处理时间
            if (this.progressTracker.averageProcessingTime === 0) {
                this.progressTracker.averageProcessingTime = processingTime;
            } else {
                this.progressTracker.averageProcessingTime =
                    (this.progressTracker.averageProcessingTime + processingTime) / 2;
            }
        }

        /**
         * 完成批量操作
         * @param {Object} results - 批量操作结果
         */
        completeBatchOperation(results) {
            this.batchState.isRunning = false;

            if (this.stateManager) {
                this.stateManager.completeBatchProgress();
            }

            // 清理并发控制
            this.concurrencyController.activePromises.clear();

            const successRate = results.successful.length / results.total;
            const duration = results.duration;

            this.logger?.log(`批量操作完成`, 'success', {
                total: results.total,
                successful: results.successful.length,
                failed: results.failed.length,
                successRate: Math.round(successRate * 100) + '%',
                duration: duration + 'ms'
            });
        }

        /**
         * 处理批量错误
         * @param {Error} error - 错误对象
         */
        handleBatchError(error) {
            this.batchState.isRunning = false;

            if (this.stateManager) {
                this.stateManager.addErrorLog({
                    type: 'batchError',
                    message: error.message,
                    stack: error.stack
                });
            }

            this.logger?.logError('批量操作失败', error);
        }

        /**
         * 批量应用设置
         * @param {Array} selectedOrders - 选中的订单
         * @param {Object} settings - 要应用的设置
         * @returns {Object} 应用结果
         */
        batchApplySettings(selectedOrders, settings) {
            let updatedCount = 0;
            const errors = [];

            selectedOrders.forEach((order, index) => {
                try {
                    // 应用语言设置
                    if (settings.language) {
                        order.languagesIdArray = [parseInt(settings.language)];
                        updatedCount++;
                    }

                    // 应用车型设置
                    if (settings.carType) {
                        order.carTypeId = parseInt(settings.carType);
                        updatedCount++;
                    }

                    // 应用负责人设置
                    if (settings.backendUser) {
                        order.backendUserId = parseInt(settings.backendUser);
                        updatedCount++;
                    }

                    // 应用其他设置
                    Object.keys(settings).forEach(key => {
                        if (!['language', 'carType', 'backendUser'].includes(key)) {
                            order[key] = settings[key];
                        }
                    });

                } catch (error) {
                    errors.push({
                        orderIndex: index,
                        error: error.message
                    });
                }
            });

            this.logger?.log(`批量设置应用完成，更新了 ${updatedCount} 个字段`, 'success');

            return {
                updatedCount,
                errors,
                success: errors.length === 0
            };
        }

        /**
         * 延迟函数
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise} 延迟Promise
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 获取批量操作统计信息
         * @returns {Object} 统计信息
         */
        getBatchStats() {
            return {
                isRunning: this.batchState.isRunning,
                currentBatchSize: this.batchState.currentBatch.length,
                queueSize: this.batchState.processingQueue.length,
                completedCount: this.batchState.completedOrders.length,
                failedCount: this.batchState.failedOrders.length,
                retryQueueSize: this.batchState.retryQueue.length,
                activeConcurrency: this.concurrencyController.activePromises.size,
                maxConcurrency: this.concurrencyController.maxConcurrent,
                averageProcessingTime: this.progressTracker.averageProcessingTime,
                estimatedTimeRemaining: this.progressTracker.estimatedTimeRemaining,
                successRate: this.progressTracker.successRate
            };
        }

        /**
         * 停止批量操作
         */
        stopBatchOperation() {
            if (this.batchState.isRunning) {
                this.batchState.isRunning = false;
                this.concurrencyController.activePromises.clear();

                if (this.stateManager) {
                    this.stateManager.updateBatchProgress({ isRunning: false });
                }

                this.logger?.log('批量操作已停止', 'warn');
            }
        }

        /**
         * 清理批量操作管理器
         */
        cleanup() {
            // 停止正在进行的操作
            this.stopBatchOperation();

            // 清理定时器
            if (this.progressUpdateTimer) {
                clearInterval(this.progressUpdateTimer);
                this.progressUpdateTimer = null;
            }

            // 重置状态
            this.batchState = {
                isRunning: false,
                currentBatch: [],
                processingQueue: [],
                completedOrders: [],
                failedOrders: [],
                retryQueue: []
            };

            this.logger?.log('🚀 批量操作管理器已清理', 'info');
        }

        /**
         * 销毁批量操作管理器
         */
        destroy() {
            this.cleanup();
            this.logger?.log('🚀 批量操作管理器已销毁', 'info');
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务对象
         */
        getLogger() {
            return window.OTA.getService('logger') || {
                log: console.log.bind(console),
                logError: console.error.bind(console)
            };
        }
    }

    /**
     * 创建批量操作管理器实例的工厂函数
     * @param {Object} dependencies - 依赖注入对象
     * @returns {MultiOrderBatchManager} 批量操作管理器实例
     */
    function createMultiOrderBatchManager(dependencies = {}) {
        return new MultiOrderBatchManager(dependencies);
    }

    // 导出到全局作用域
    window.getMultiOrderBatchManager = createMultiOrderBatchManager;
    window.MultiOrderBatchManager = MultiOrderBatchManager;

    // 确保OTA命名空间存在
    if (typeof window.OTA === 'undefined') {
        window.OTA = {};
    }
    if (typeof window.OTA.multiOrder === 'undefined') {
        window.OTA.multiOrder = {};
    }

    // 注册到OTA命名空间
    window.OTA.multiOrder.BatchManager = MultiOrderBatchManager;
    window.OTA.multiOrder.getBatchManager = createMultiOrderBatchManager;

})();
