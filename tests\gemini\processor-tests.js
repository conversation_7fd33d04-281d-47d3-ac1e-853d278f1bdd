/**
 * 文件: processor-tests.js
 * 路径: tests\gemini\processor-tests.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.getLogger
 * - window.OTA.Registry
 * - window.OTA.Registry.registerService
 * - window.OTA.Registry.registerFactory
 * - console (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.OTA
 * - window.OTA.gemini
 * - window.OTA.gemini.tests
 * - window.OTA.gemini.tests.processorTestSuite
 * - window.OTA.gemini.tests.ProcessorTestSuite
 * - window.OTA.gemini.tests.getProcessorTestSuite
 * - window.getProcessorTestSuite
 * 
 * === 类声明 ===
 * - class ProcessorTestSuite
 * 
 * === 函数声明 ===
 * - function getProcessorTestSuite()
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.921Z
 */

/**
 * @TEST_SUITE OTA处理器单元测试套件
 * 🏷️ 标签: @PROCESSOR_TESTS
 * 📝 说明: 为所有OTA专用处理器提供全面的单元测试，包括功能测试、边界测试和错误处理测试
 * 🎯 功能: 处理器测试、数据验证、性能测试、错误处理测试
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.tests = window.OTA.gemini.tests || {};

(function() {
    'use strict';

    /**
     * OTA处理器测试套件类
     * 提供全面的处理器测试功能
     */
    class ProcessorTestSuite {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.testResults = [];
            this.testStats = {
                totalTests: 0,
                passedTests: 0,
                failedTests: 0,
                skippedTests: 0,
                startTime: null,
                endTime: null,
                duration: 0
            };
            
            // 测试数据
            this.testData = this.initializeTestData();
            
            // 处理器实例
            this.processors = {};
            
            this.logger.log('OTA处理器测试套件初始化完成', 'info');
        }

        /**
         * 初始化测试数据
         * @returns {Object} 测试数据集合
         */
        initializeTestData() {
            return {
                // Chong Dealer测试数据
                chongDealer: {
                    valid: `
                        订单号: CD123456789
                        乘客姓名: 张三
                        联系电话: +60123456789
                        接送地点: 吉隆坡国际机场
                        目的地: 双威酒店
                        服务日期: 2024-01-15
                        服务时间: 14:30
                        航班号: MH370
                        人数: 2人
                        行李: 3件
                        特殊要求: 需要举牌接机
                    `,
                    invalid: `
                        订单号: 
                        乘客姓名: 
                        联系电话: 无效电话
                        接送地点: 
                        目的地: 
                        服务日期: 无效日期
                        服务时间: 25:70
                    `,
                    edge: `
                        订单号: CD999999999999999999
                        乘客姓名: 非常长的姓名测试数据超过正常长度限制的情况下系统应该如何处理
                        联系电话: +60123456789012345678901234567890
                        接送地点: 非常长的地址信息测试数据
                        目的地: 非常长的目的地信息测试数据
                        服务日期: 2024-12-31
                        服务时间: 23:59
                        人数: 999人
                        行李: 999件
                    `
                },
                
                // Fliggy测试数据
                fliggy: {
                    valid: `
                        飞猪订单号: FG987654321
                        旅客姓名: 李四
                        联系方式: 13812345678
                        出发地: 上海浦东机场
                        目的地: 北京首都机场
                        出行日期: 2024-02-20
                        出行时间: 09:15
                        航班信息: CA1234
                        乘客数量: 1人
                        行李数量: 2件
                        订单金额: ¥350.00
                        备注: 商务舱乘客
                    `,
                    invalid: `
                        飞猪订单号: 
                        旅客姓名: 
                        联系方式: 无效手机号
                        出发地: 
                        目的地: 
                        出行日期: 无效日期
                        出行时间: 无效时间
                    `,
                    edge: `
                        飞猪订单号: FG123456789012345678901234567890
                        旅客姓名: 测试用户名包含特殊字符@#$%^&*()
                        联系方式: +86138123456789012345
                        出发地: 测试地址包含特殊字符和超长内容
                        目的地: 测试目的地包含特殊字符和超长内容
                        出行日期: 2099-12-31
                        出行时间: 00:00
                        乘客数量: 0人
                        行李数量: 0件
                        订单金额: ¥0.00
                    `
                },
                
                // JRCoach测试数据
                jrcoach: {
                    valid: `
                        JRCoach订单: JR456789123
                        客户姓名: Ahmad bin Abdullah
                        联系电话: 0123456789
                        接载地点: Kuala Lumpur Sentral
                        送达地点: KLIA Terminal 1
                        服务日期: 15/01/2024
                        服务时间: 2:30 PM
                        航班号: AK6049
                        乘客人数: 3
                        行李件数: 4
                        服务类型: 机场接送
                        车型要求: MPV
                    `,
                    invalid: `
                        JRCoach订单: 
                        客户姓名: 
                        联系电话: 无效号码
                        接载地点: 
                        送达地点: 
                        服务日期: 无效日期
                        服务时间: 无效时间
                    `,
                    edge: `
                        JRCoach订单: JR999999999999999999999999999999
                        客户姓名: Very Long Customer Name That Exceeds Normal Length Limits
                        联系电话: 01234567890123456789
                        接载地点: Very Long Pickup Location Address That Exceeds Normal Length
                        送达地点: Very Long Dropoff Location Address That Exceeds Normal Length
                        服务日期: 31/12/2099
                        服务时间: 11:59 PM
                        乘客人数: 50
                        行李件数: 100
                    `
                },
                
                // KKday测试数据
                kkday: {
                    valid: `
                        KKday订单编号: KK789123456
                        预订人姓名: 王五
                        联系电话: +886912345678
                        活动名称: 台北101观景台门票
                        使用日期: 2024-03-10
                        使用时间: 10:00
                        参与人数: 2人
                        订单金额: NT$800
                        凭证代码: KKDAY2024
                        特殊需求: 轮椅通道
                    `,
                    invalid: `
                        KKday订单编号: 
                        预订人姓名: 
                        联系电话: 无效电话
                        活动名称: 
                        使用日期: 无效日期
                        使用时间: 无效时间
                        参与人数: 无效人数
                        订单金额: 无效金额
                    `,
                    edge: `
                        KKday订单编号: KK999999999999999999999999999999
                        预订人姓名: 超长姓名测试数据包含各种特殊字符
                        联系电话: +886912345678901234567890
                        活动名称: 超长活动名称测试数据包含各种特殊字符和符号
                        使用日期: 2099-12-31
                        使用时间: 23:59
                        参与人数: 999人
                        订单金额: NT$999999.99
                    `
                },
                
                // Klook测试数据
                klook: {
                    valid: `
                        Klook订单号: KL654321987
                        客户姓名: 陈六
                        手机号码: +85298765432
                        产品名称: 香港迪士尼乐园门票
                        游玩日期: 2024-04-05
                        游玩时间: 09:00
                        游客人数: 4人
                        订单总额: HK$2400
                        确认码: KLOOK2024
                        备注信息: 儿童票2张，成人票2张
                    `,
                    invalid: `
                        Klook订单号: 
                        客户姓名: 
                        手机号码: 无效号码
                        产品名称: 
                        游玩日期: 无效日期
                        游玩时间: 无效时间
                        游客人数: 无效人数
                        订单总额: 无效金额
                    `,
                    edge: `
                        Klook订单号: KL999999999999999999999999999999
                        客户姓名: 超长客户姓名测试数据包含各种语言字符
                        手机号码: +85298765432012345678901234567890
                        产品名称: 超长产品名称测试数据包含各种特殊字符和符号
                        游玩日期: 2099-12-31
                        游玩时间: 23:59
                        游客人数: 999人
                        订单总额: HK$999999.99
                    `
                },
                
                // Ctrip测试数据
                ctrip: {
                    valid: `
                        携程订单号: CT123987456
                        旅客姓名: 赵七
                        联系电话: 13987654321
                        接送地点: 北京首都机场T3航站楼
                        目的地: 北京王府井希尔顿酒店
                        服务日期: 2024-05-15
                        服务时间: 16:45
                        航班号: CA1357
                        乘客人数: 2人
                        行李数量: 3件
                        订单金额: ¥280.00
                        订单状态: 已确认
                        特殊要求: 需要儿童安全座椅
                    `,
                    invalid: `
                        携程订单号: 
                        旅客姓名: 
                        联系电话: 无效手机
                        接送地点: 
                        目的地: 
                        服务日期: 无效日期
                        服务时间: 无效时间
                    `,
                    edge: `
                        携程订单号: CT999999999999999999999999999999
                        旅客姓名: 超长旅客姓名测试数据包含各种中文字符
                        联系电话: 13987654321012345678901234567890
                        接送地点: 超长接送地点测试数据包含各种地址信息
                        目的地: 超长目的地测试数据包含各种地址信息
                        服务日期: 2099-12-31
                        服务时间: 23:59
                        乘客人数: 999人
                        行李数量: 999件
                        订单金额: ¥999999.99
                    `
                },
                
                // Agoda测试数据
                agoda: {
                    valid: `
                        Agoda预订号: AG789456123
                        客人姓名: John Smith
                        联系电话: +60123456789
                        酒店名称: Mandarin Oriental Kuala Lumpur
                        入住日期: 2024-06-20
                        入住时间: 15:00
                        退房日期: 2024-06-23
                        房型: Deluxe Room
                        客人数: 2
                        预订金额: $450.00
                        预订状态: Confirmed
                        特殊要求: Late check-in
                    `,
                    invalid: `
                        Agoda预订号: 
                        客人姓名: 
                        联系电话: 无效电话
                        酒店名称: 
                        入住日期: 无效日期
                        入住时间: 无效时间
                        客人数: 无效人数
                        预订金额: 无效金额
                    `,
                    edge: `
                        Agoda预订号: AG999999999999999999999999999999
                        客人姓名: Very Long Guest Name With Special Characters @#$%
                        联系电话: +60123456789012345678901234567890
                        酒店名称: Very Long Hotel Name That Exceeds Normal Length Limits
                        入住日期: 2099-12-31
                        入住时间: 23:59
                        退房日期: 2099-12-31
                        客人数: 999
                        预订金额: $999999.99
                    `
                },
                
                // Booking.com测试数据
                booking: {
                    valid: `
                        Booking.com预订确认号: BK456123789
                        主客人: Maria Garcia
                        联系电话: +34612345678
                        酒店名称: Hotel Ritz Madrid
                        入住日期: 2024-07-10
                        入住时间: 14:00
                        退房日期: 2024-07-13
                        房间类型: Superior Double Room
                        客人数量: 2
                        预订总额: €480.00
                        预订状态: 已确认
                        特殊要求: Airport transfer needed
                    `,
                    invalid: `
                        Booking.com预订确认号: 
                        主客人: 
                        联系电话: 无效电话
                        酒店名称: 
                        入住日期: 无效日期
                        入住时间: 无效时间
                        客人数量: 无效人数
                        预订总额: 无效金额
                    `,
                    edge: `
                        Booking.com预订确认号: BK999999999999999999999999999999
                        主客人: Very Long Guest Name With International Characters ñáéíóú
                        联系电话: +34612345678901234567890123456789
                        酒店名称: Very Long Hotel Name That Exceeds Normal Length Limits
                        入住日期: 2099-12-31
                        入住时间: 23:59
                        退房日期: 2099-12-31
                        客人数量: 999
                        预订总额: €999999.99
                    `
                }
            };
        }

        /**
         * 初始化处理器实例
         */
        async initializeProcessors() {
            try {
                // 获取所有处理器实例
                this.processors = {
                    chongDealer: window.OTA?.gemini?.processors?.getChongDealerProcessor?.(),
                    fliggy: window.OTA?.gemini?.processors?.getFliggyProcessor?.(),
                    jrcoach: window.OTA?.gemini?.processors?.getJRCoachProcessor?.(),
                    kkday: window.OTA?.gemini?.processors?.getKKdayProcessor?.(),
                    klook: window.OTA?.gemini?.processors?.getKlookProcessor?.(),
                    ctrip: window.OTA?.gemini?.processors?.getCtripProcessor?.(),
                    agoda: window.OTA?.gemini?.processors?.getAgodaProcessor?.(),
                    booking: window.OTA?.gemini?.processors?.getBookingProcessor?.()
                };
                
                // 验证处理器是否正确加载
                const missingProcessors = [];
                for (const [name, processor] of Object.entries(this.processors)) {
                    if (!processor) {
                        missingProcessors.push(name);
                    }
                }
                
                if (missingProcessors.length > 0) {
                    throw new Error(`缺少处理器: ${missingProcessors.join(', ')}`);
                }
                
                this.logger.log('所有处理器实例初始化完成', 'info');
                
            } catch (error) {
                this.logger.logError('处理器初始化失败', error);
                throw error;
            }
        }

        /**
         * 运行所有测试
         * @returns {Promise<Object>} 测试结果
         */
        async runAllTests() {
            this.testStats.startTime = Date.now();
            this.testResults = [];
            
            try {
                this.logger.log('开始运行OTA处理器测试套件', 'info');
                
                // 初始化处理器
                await this.initializeProcessors();
                
                // 运行各个处理器的测试
                for (const [processorName, processor] of Object.entries(this.processors)) {
                    if (processor) {
                        await this.runProcessorTests(processorName, processor);
                    }
                }
                
                // 运行性能测试
                await this.runPerformanceTests();
                
                // 运行错误处理测试
                await this.runErrorHandlingTests();
                
                // 计算测试统计
                this.calculateTestStats();
                
                this.logger.log('OTA处理器测试套件运行完成', 'info');
                
                return this.generateTestReport();
                
            } catch (error) {
                this.logger.logError('测试套件运行失败', error);
                throw error;
            }
        }

        /**
         * 运行单个处理器的测试
         * @param {string} processorName - 处理器名称
         * @param {Object} processor - 处理器实例
         */
        async runProcessorTests(processorName, processor) {
            this.logger.log(`开始测试${processorName}处理器`, 'info');
            
            const testData = this.testData[processorName];
            if (!testData) {
                this.addTestResult(processorName, 'setup', false, '缺少测试数据');
                return;
            }
            
            // 测试有效数据处理
            await this.testValidDataProcessing(processorName, processor, testData.valid);
            
            // 测试无效数据处理
            await this.testInvalidDataProcessing(processorName, processor, testData.invalid);
            
            // 测试边界数据处理
            await this.testEdgeDataProcessing(processorName, processor, testData.edge);
            
            // 测试处理器特定功能
            await this.testProcessorSpecificFeatures(processorName, processor);
            
            this.logger.log(`${processorName}处理器测试完成`, 'info');
        }

        /**
         * 测试有效数据处理
         * @param {string} processorName - 处理器名称
         * @param {Object} processor - 处理器实例
         * @param {string} validData - 有效测试数据
         */
        async testValidDataProcessing(processorName, processor, validData) {
            try {
                const result = await processor.processOrder(validData, { test: true });
                
                // 验证处理结果
                const isValid = this.validateProcessingResult(result, true);
                
                this.addTestResult(
                    processorName, 
                    'valid_data_processing', 
                    isValid, 
                    isValid ? '有效数据处理成功' : '有效数据处理失败',
                    result
                );
                
            } catch (error) {
                this.addTestResult(
                    processorName, 
                    'valid_data_processing', 
                    false, 
                    `有效数据处理异常: ${error.message}`
                );
            }
        }

        /**
         * 测试无效数据处理
         * @param {string} processorName - 处理器名称
         * @param {Object} processor - 处理器实例
         * @param {string} invalidData - 无效测试数据
         */
        async testInvalidDataProcessing(processorName, processor, invalidData) {
            try {
                const result = await processor.processOrder(invalidData, { test: true });
                
                // 无效数据应该返回降级结果或包含错误信息
                const isHandledCorrectly = !result.success || result.metadata?.fallback || result.confidence < 0.5;
                
                this.addTestResult(
                    processorName, 
                    'invalid_data_processing', 
                    isHandledCorrectly, 
                    isHandledCorrectly ? '无效数据处理正确' : '无效数据处理不当',
                    result
                );
                
            } catch (error) {
                // 无效数据抛出异常是可以接受的
                this.addTestResult(
                    processorName, 
                    'invalid_data_processing', 
                    true, 
                    `无效数据正确抛出异常: ${error.message}`
                );
            }
        }

        /**
         * 测试边界数据处理
         * @param {string} processorName - 处理器名称
         * @param {Object} processor - 处理器实例
         * @param {string} edgeData - 边界测试数据
         */
        async testEdgeDataProcessing(processorName, processor, edgeData) {
            try {
                const result = await processor.processOrder(edgeData, { test: true });
                
                // 边界数据应该能够处理，但可能置信度较低
                const isHandledCorrectly = result !== null && result !== undefined;
                
                this.addTestResult(
                    processorName, 
                    'edge_data_processing', 
                    isHandledCorrectly, 
                    isHandledCorrectly ? '边界数据处理成功' : '边界数据处理失败',
                    result
                );
                
            } catch (error) {
                this.addTestResult(
                    processorName, 
                    'edge_data_processing', 
                    false, 
                    `边界数据处理异常: ${error.message}`
                );
            }
        }

        /**
         * 测试处理器特定功能
         * @param {string} processorName - 处理器名称
         * @param {Object} processor - 处理器实例
         */
        async testProcessorSpecificFeatures(processorName, processor) {
            try {
                // 测试获取处理器信息
                const info = processor.getProcessorInfo?.();
                const hasInfo = info && info.name && info.platform;
                
                this.addTestResult(
                    processorName, 
                    'processor_info', 
                    hasInfo, 
                    hasInfo ? '处理器信息获取成功' : '处理器信息获取失败',
                    info
                );
                
                // 测试获取统计信息
                const stats = processor.getStats?.();
                const hasStats = stats && typeof stats.totalProcessed === 'number';
                
                this.addTestResult(
                    processorName, 
                    'processor_stats', 
                    hasStats, 
                    hasStats ? '处理器统计获取成功' : '处理器统计获取失败',
                    stats
                );
                
                // 测试重置统计
                if (processor.resetStats) {
                    processor.resetStats();
                    const resetStats = processor.getStats?.();
                    const isReset = resetStats && resetStats.totalProcessed === 0;
                    
                    this.addTestResult(
                        processorName, 
                        'stats_reset', 
                        isReset, 
                        isReset ? '统计重置成功' : '统计重置失败',
                        resetStats
                    );
                }
                
            } catch (error) {
                this.addTestResult(
                    processorName, 
                    'processor_specific_features', 
                    false, 
                    `处理器特定功能测试异常: ${error.message}`
                );
            }
        }

        /**
         * 运行性能测试
         */
        async runPerformanceTests() {
            this.logger.log('开始性能测试', 'info');
            
            const performanceResults = {};
            
            for (const [processorName, processor] of Object.entries(this.processors)) {
                if (!processor) continue;
                
                const testData = this.testData[processorName]?.valid;
                if (!testData) continue;
                
                try {
                    // 运行多次测试以获取平均性能
                    const iterations = 10;
                    const times = [];
                    
                    for (let i = 0; i < iterations; i++) {
                        const startTime = performance.now();
                        await processor.processOrder(testData, { test: true });
                        const endTime = performance.now();
                        times.push(endTime - startTime);
                    }
                    
                    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
                    const minTime = Math.min(...times);
                    const maxTime = Math.max(...times);
                    
                    performanceResults[processorName] = {
                        averageTime: avgTime,
                        minTime: minTime,
                        maxTime: maxTime,
                        iterations: iterations
                    };
                    
                    // 性能测试通过标准：平均处理时间小于1000ms
                    const passed = avgTime < 1000;
                    
                    this.addTestResult(
                        processorName, 
                        'performance', 
                        passed, 
                        `平均处理时间: ${avgTime.toFixed(2)}ms`,
                        performanceResults[processorName]
                    );
                    
                } catch (error) {
                    this.addTestResult(
                        processorName, 
                        'performance', 
                        false, 
                        `性能测试异常: ${error.message}`
                    );
                }
            }
            
            this.logger.log('性能测试完成', 'info');
        }

        /**
         * 运行错误处理测试
         */
        async runErrorHandlingTests() {
            this.logger.log('开始错误处理测试', 'info');
            
            const errorTestCases = [
                { name: 'null_input', data: null },
                { name: 'undefined_input', data: undefined },
                { name: 'empty_string', data: '' },
                { name: 'non_string_input', data: 123 },
                { name: 'object_input', data: {} },
                { name: 'array_input', data: [] }
            ];
            
            for (const [processorName, processor] of Object.entries(this.processors)) {
                if (!processor) continue;
                
                for (const testCase of errorTestCases) {
                    try {
                        const result = await processor.processOrder(testCase.data, { test: true });
                        
                        // 错误输入应该被正确处理（返回错误结果或抛出异常）
                        const isHandledCorrectly = !result.success || result.metadata?.fallback;
                        
                        this.addTestResult(
                            processorName, 
                            `error_handling_${testCase.name}`, 
                            isHandledCorrectly, 
                            isHandledCorrectly ? `${testCase.name}错误处理正确` : `${testCase.name}错误处理不当`,
                            result
                        );
                        
                    } catch (error) {
                        // 抛出异常也是正确的错误处理方式
                        this.addTestResult(
                            processorName, 
                            `error_handling_${testCase.name}`, 
                            true, 
                            `${testCase.name}正确抛出异常: ${error.message}`
                        );
                    }
                }
            }
            
            this.logger.log('错误处理测试完成', 'info');
        }

        /**
         * 验证处理结果
         * @param {Object} result - 处理结果
         * @param {boolean} shouldSucceed - 是否应该成功
         * @returns {boolean} 验证结果
         */
        validateProcessingResult(result, shouldSucceed = true) {
            if (!result) return false;
            
            // 基本结构验证
            const hasBasicStructure = result.hasOwnProperty('success') && 
                                    result.hasOwnProperty('processor') && 
                                    result.hasOwnProperty('platform') && 
                                    result.hasOwnProperty('confidence') && 
                                    result.hasOwnProperty('data') && 
                                    result.hasOwnProperty('metadata');
            
            if (!hasBasicStructure) return false;
            
            // 成功结果验证
            if (shouldSucceed) {
                return result.success === true && 
                       result.confidence > 0 && 
                       result.data && 
                       result.data.ota_reference_number;
            }
            
            return true;
        }

        /**
         * 添加测试结果
         * @param {string} processorName - 处理器名称
         * @param {string} testName - 测试名称
         * @param {boolean} passed - 是否通过
         * @param {string} message - 测试消息
         * @param {Object} data - 测试数据
         */
        addTestResult(processorName, testName, passed, message, data = null) {
            const testResult = {
                processor: processorName,
                test: testName,
                passed: passed,
                message: message,
                data: data,
                timestamp: new Date().toISOString()
            };
            
            this.testResults.push(testResult);
            this.testStats.totalTests++;
            
            if (passed) {
                this.testStats.passedTests++;
            } else {
                this.testStats.failedTests++;
            }
            
            // 记录测试结果
            const logLevel = passed ? 'info' : 'error';
            this.logger.log(`[${processorName}] ${testName}: ${message}`, logLevel);
        }

        /**
         * 计算测试统计
         */
        calculateTestStats() {
            this.testStats.endTime = Date.now();
            this.testStats.duration = this.testStats.endTime - this.testStats.startTime;
            
            // 计算成功率
            this.testStats.successRate = this.testStats.totalTests > 0 ? 
                (this.testStats.passedTests / this.testStats.totalTests) * 100 : 0;
        }

        /**
         * 生成测试报告
         * @returns {Object} 测试报告
         */
        generateTestReport() {
            const report = {
                summary: {
                    ...this.testStats,
                    successRate: this.testStats.successRate.toFixed(2) + '%'
                },
                results: this.testResults,
                processorSummary: this.generateProcessorSummary(),
                recommendations: this.generateRecommendations()
            };
            
            this.logger.log('测试报告生成完成', 'info');
            return report;
        }

        /**
         * 生成处理器摘要
         * @returns {Object} 处理器摘要
         */
        generateProcessorSummary() {
            const summary = {};
            
            for (const processorName of Object.keys(this.processors)) {
                const processorResults = this.testResults.filter(r => r.processor === processorName);
                const passed = processorResults.filter(r => r.passed).length;
                const total = processorResults.length;
                
                summary[processorName] = {
                    totalTests: total,
                    passedTests: passed,
                    failedTests: total - passed,
                    successRate: total > 0 ? ((passed / total) * 100).toFixed(2) + '%' : '0%'
                };
            }
            
            return summary;
        }

        /**
         * 生成建议
         * @returns {Array} 建议列表
         */
        generateRecommendations() {
            const recommendations = [];
            const failedTests = this.testResults.filter(r => !r.passed);
            
            if (failedTests.length > 0) {
                recommendations.push('存在失败的测试用例，建议检查相关处理器的实现');
            }
            
            if (this.testStats.successRate < 90) {
                recommendations.push('测试成功率低于90%，建议进行代码审查和优化');
            }
            
            // 检查性能问题
            const performanceTests = this.testResults.filter(r => r.test === 'performance' && !r.passed);
            if (performanceTests.length > 0) {
                recommendations.push('存在性能问题，建议优化处理器性能');
            }
            
            // 检查错误处理问题
            const errorHandlingTests = this.testResults.filter(r => r.test.startsWith('error_handling') && !r.passed);
            if (errorHandlingTests.length > 0) {
                recommendations.push('错误处理存在问题，建议加强异常处理机制');
            }
            
            if (recommendations.length === 0) {
                recommendations.push('所有测试通过，系统运行良好');
            }
            
            return recommendations;
        }

        /**
         * 获取测试结果
         * @returns {Object} 测试结果
         */
        getTestResults() {
            return {
                stats: this.testStats,
                results: this.testResults
            };
        }

        /**
         * 清理测试数据
         */
        cleanup() {
            this.testResults = [];
            this.testStats = {
                totalTests: 0,
                passedTests: 0,
                failedTests: 0,
                skippedTests: 0,
                startTime: null,
                endTime: null,
                duration: 0
            };
            
            this.logger.log('测试数据清理完成', 'info');
        }
    }

    // 创建全局单例实例
    function getProcessorTestSuite() {
        if (!window.OTA.gemini.tests.processorTestSuite) {
            window.OTA.gemini.tests.processorTestSuite = new ProcessorTestSuite();
        }
        return window.OTA.gemini.tests.processorTestSuite;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.tests.ProcessorTestSuite = ProcessorTestSuite;
    window.OTA.gemini.tests.getProcessorTestSuite = getProcessorTestSuite;

    // 向后兼容
    window.getProcessorTestSuite = getProcessorTestSuite;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('processorTestSuite', getProcessorTestSuite, '@PROCESSOR_TEST_SUITE');
        window.OTA.Registry.registerFactory('getProcessorTestSuite', getProcessorTestSuite, '@PROCESSOR_TEST_SUITE_FACTORY');
    }

    console.log('✅ OTA处理器测试套件已加载');

})();
