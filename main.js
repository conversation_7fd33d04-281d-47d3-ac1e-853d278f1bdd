/**
 * 文件: main.js
 * 路径: main.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.OTA.container
 * - window.OTA.ApplicationBootstrap
 * - window.OTA.serviceLocator
 * - window.OTA.getService
 * - window.OTA
 * - window.OTA.container
 * - window.OTA.ApplicationBootstrap
 * - window.OTA.ApplicationBootstrap
 * - window.OTA.ApplicationBootstrap
 * - window.OTA.container
 * - window.OTA.serviceLocator
 * - window.OTA.getService
 * - window.location
 * - window.location
 * - window.location
 * - window.fetch
 * - window.localStorage
 * - window.Promise
 * - window.URLSearchParams
 * - window.addEventListener
 * - window.addEventListener
 * - window.addEventListener
 * - window.OTA
 * - window.OTA.configCenter
 * - window.getI18nManager
 * - window.getI18nManager
 * - window.getMultiOrderManager
 * - window.OTA.getService
 * - window.getOrderHistoryManager
 * - window.getOrderHistoryManager
 * - window.addEventListener
 * - window.addEventListener
 * - window.innerWidth
 * - window.innerHeight
 * - window.addEventListener
 * - window.localStorage
 * - window.appState
 * - window.apiService
 * - window.geminiService
 * - window.uiManager
 * - window.logger
 * - window.removeEventListener
 * - window.removeEventListener
 * - window.removeEventListener
 * - window.OTA.logger
 * - window.OTA
 * - window.OTA.logger
 * - window.OTA.appState
 * - window.OTA.geminiService
 * - window.OTA.apiService
 * - window.OTA.uiManager
 * - window.OTA.imageUploadManager
 * - window.OTA.currencyConverter
 * - window.OTA.multiOrderManager
 * - window.OTA.pagingServiceManager
 * - window.OTA.orderHistoryManager
 * - window.OTA.i18nManager
 * - window.performance
 * - window.performance.memory
 * - window.performance.memory
 * - window.OTA
 * - window.location
 * - document (DOM API)
 * - localStorage (浏览器API)
 * - console (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.OTA.app
 * - window.app
 * - window.monitoring
 * 
 * === 类声明 ===
 * - class LegacyOTAApplication
 * 
 * === 函数声明 ===
 * - function performSystemHealthCheck()
 * - function setupMonitoringCommands()
 * 
 * === 事件监听 ===
 * - DOMContentLoaded 事件
 * - error 事件
 * - unhandledrejection 事件
 * - beforeunload 事件
 * - online 事件
 * - offline 事件
 * - visibilitychange 事件
 * - resize 事件
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.900Z
 */

/**
 * OTA订单处理系统 - 主入口文件 (重构版)
 * 使用新的启动协调器统一管理初始化流程
 * 解决架构混乱问题
 * 
 * === 依赖关系分析 ===
 * 外部依赖:
 * - window.OTA.container: 依赖容器系统 (来自 js/core/dependency-container.js)
 * - window.OTA.ApplicationBootstrap: 应用启动器 (来自 js/bootstrap/application-bootstrap.js)
 * - window.OTA.serviceLocator: 服务定位器 (来自 js/core/service-locator.js)
 * - window.OTA.getService: 服务获取函数 (来自服务定位器)
 * 
 * 全局变量声明:
 * - window.OTA.app: 主应用实例对象
 * - window.app: 开发环境全局应用实例 (仅开发环境)
 * - window.monitoring: 监控系统命令集合
 * 
 * 核心初始化流程:
 * 1. DOMContentLoaded 事件监听
 * 2. 核心模块检查和验证
 * 3. 应用启动器实例化
 * 4. 系统启动和服务注册
 * 5. 全局变量暴露和环境判断
 * 6. 错误处理和降级策略
 */

// === 主应用启动流程 ===
// 等待DOM加载完成后启动应用 (依赖: DOM API)
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 开始启动OTA订单处理系统...');

    try {
        // === 依赖检查阶段 ===
        // 检查核心模块是否已加载 (必须依赖项验证)
        // 这些依赖项由HTML中的script标签按顺序加载
        if (!window.OTA || !window.OTA.container || !window.OTA.ApplicationBootstrap) {
            throw new Error('核心架构模块未正确加载，请检查script标签顺序');
        }

        // === 启动器实例化阶段 ===
        // 创建启动协调器实例 (依赖: window.OTA.ApplicationBootstrap 构造函数)
        const bootstrap = new window.OTA.ApplicationBootstrap();

        // === 系统启动阶段 ===
        // 启动应用 (调用启动器的start方法，返回Promise<StartupResult>)
        const result = await bootstrap.start();

        if (result.success) {
            console.log(`✅ OTA系统启动成功，耗时: ${result.duration.toFixed(2)}ms`);

            // === 全局变量注册阶段 ===
            // 暴露到OTA命名空间（生产环境和开发环境都可用）
            // 注册主应用对象到全局OTA命名空间
            window.OTA.app = {
                bootstrap,                              // 启动器实例引用
                container: window.OTA.container,        // 依赖容器引用
                serviceLocator: window.OTA.serviceLocator, // 服务定位器引用
                getService: window.OTA.getService,      // 服务获取函数引用
                version: '2.0.0-refactored',           // 应用版本号 (静态配置)
                startTime: Date.now()                   // 启动时间戳 (动态生成)
            };

            // === 登录表单事件处理 ===
            // 设置登录表单事件监听器
            setupLoginHandlers();

            // === 环境判断和开发工具暴露 ===
            // 仅在开发环境暴露全局应用实例（用于调试）
            // 环境检测: localhost, file协议, 127.0.0.1
            if (typeof window !== 'undefined' &&
                (window.location?.hostname === 'localhost' ||
                 window.location?.protocol === 'file:' ||
                 window.location?.hostname === '127.0.0.1')) {
                // 开发环境: 暴露window.app全局变量用于调试
                window.app = window.OTA.app;
                console.log('🔧 开发环境：全局 window.app 已暴露用于调试');
            } else {
                // 生产环境: 不暴露window.app，保持命名空间清洁
                console.log('🏭 生产环境：全局 window.app 未暴露，请使用 window.OTA.app');
            }

        } else {
            throw new Error(`系统启动失败: ${result.error}`);
        }

    } catch (error) {
        console.error('❌ 系统启动失败:', error);

        // 显示错误信息给用户
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: #ff4444; color: white; padding: 20px; border-radius: 8px;
            font-family: Arial, sans-serif; z-index: 10000; max-width: 500px;
        `;
        errorDiv.innerHTML = `
            <h3>系统启动失败</h3>
            <p>${error.message}</p>
            <button onclick="location.reload()" style="background: white; color: #ff4444; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                重新加载
            </button>
        `;
        document.body.appendChild(errorDiv);
    }
});

// === 登录处理函数 ===
/**
 * 初始化工作区 - 简化版 (避免与ApplicationBootstrap重复)
 * @deprecated 微服务初始化已统一到ApplicationBootstrap中
 * 此函数仅处理工作区特定的UI绑定和用户交互
 */
async function initializeWorkspace() {
    try {
        console.log('🔧 开始初始化工作区UI绑定...');

        // 🗑️ 已移除重复的服务初始化 - 减法重构优化
        // 微服务初始化已统一到ApplicationBootstrap的services阶段
        // 避免重复初始化：formService, eventService, stateService

        // 检查微服务是否已由ApplicationBootstrap初始化
        const servicesReady = await checkMicroservicesReady();
        if (!servicesReady) {
            console.warn('⚠️ 微服务未就绪，跳过工作区初始化');
            return;
        }

        // 🗑️ 已移除重复的价格服务初始化 - 减法重构优化
        // 价格服务初始化已统一到ApplicationBootstrap中
        console.log('✅ 工作区UI绑定完成（微服务由ApplicationBootstrap管理）');
        
        // 初始化历史订单管理器
        const orderHistoryManager = window.OTA.managers?.orderHistoryManager || window.orderHistoryManager;
        if (orderHistoryManager && typeof orderHistoryManager.init === 'function') {
            await orderHistoryManager.init();
            console.log('✅ 历史订单管理器初始化完成');
        } else {
            console.warn('⚠️ 历史订单管理器不可用');
        }
        
        // 初始化实时分析管理器
        const realtimeAnalysisManager = window.OTA.managers?.realtimeAnalysisManager || 
                                       window.getRealtimeAnalysisManager?.();
        if (realtimeAnalysisManager && typeof realtimeAnalysisManager.init === 'function') {
            await realtimeAnalysisManager.init();
            console.log('✅ 实时分析管理器初始化完成');
        } else {
            console.warn('⚠️ 实时分析管理器不可用');
        }
        
        // 绑定createOrder按钮事件
        const createOrderBtn = document.getElementById('createOrder');
        const formService = window.OTA.getService('formService');
        if (createOrderBtn && formService) {
            createOrderBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                console.log('🚀 创建订单按钮被点击');

                try {
                    // 收集表单数据
                    const formData = formService.collectFormData();
                    console.log('📋 表单数据已收集:', formData);

                    // 验证表单
                    const isValid = formService.validateForm();
                    if (!isValid) {
                        console.warn('⚠️ 表单验证失败');
                        return;
                    }

                    // 触发订单创建事件
                    document.dispatchEvent(new CustomEvent('createOrderRequested', {
                        detail: { formData }
                    }));

                } catch (error) {
                    console.error('❌ 创建订单过程中出错:', error);
                }
            });

            console.log('✅ createOrder按钮事件已绑定');
        } else {
            console.warn('⚠️ createOrder按钮或表单服务不可用');
        }
        
        // 绑定orderInput实时分析功能
        const orderInput = document.getElementById('orderInput');
        if (orderInput) {
            // 添加实时分析类
            orderInput.classList.add('realtime');
            
            // 使用事件服务的防抖功能进行实时分析
            const eventService = window.OTA.getService('eventService');
            const debouncedAnalysis = eventService?.debounce(async (inputText) => {
                try {
                    console.log('🔍 触发实时分析:', inputText);

                    // 触发实时分析事件
                    document.dispatchEvent(new CustomEvent('orderInputAnalysis', {
                        detail: {
                            inputText,
                            timestamp: Date.now()
                        }
                    }));
                } catch (error) {
                    console.error('❌ 实时分析出错:', error);
                }
            }, 500, 'orderInput_analysis');
            
            orderInput.addEventListener('input', (e) => {
                const text = e.target.value.trim();
                if (text.length > 5) {  // 至少5个字符才触发分析
                    debouncedAnalysis?.(text);
                }
            });
            
            console.log('✅ orderInput实时分析功能已激活');
        } else {
            console.warn('⚠️ orderInput元素不可用');
        }
        
        // === 绑定所有按钮事件 ===
        
        // 绑定历史订单按钮
        const historyBtn = document.getElementById('historyBtn');
        if (historyBtn && orderHistoryManager) {
            historyBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('📋 历史订单按钮被点击');
                try {
                    orderHistoryManager.showHistoryPanel();
                } catch (error) {
                    console.error('❌ 显示历史订单失败:', error);
                }
            });
            console.log('✅ 历史订单按钮事件已绑定');
        } else {
            console.warn('⚠️ 历史订单按钮或管理器不可用');
        }
        
        // 绑定退出登录按钮
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🚪 退出登录按钮被点击');
                try {
                    const apiService = window.OTA.getService('apiService');
                    if (apiService && typeof apiService.logout === 'function') {
                        apiService.logout();
                    } else {
                        console.warn('⚠️ API服务logout方法不可用');
                    }
                } catch (error) {
                    console.error('❌ 退出登录失败:', error);
                }
            });
            console.log('✅ 退出登录按钮事件已绑定');
        } else {
            console.warn('⚠️ 退出登录按钮不可用');
        }
        
        // 绑定主题切换按钮 (通过StateService)
        const themeToggle = document.getElementById('themeToggle');
        const stateService = window.OTA.getService('stateService');
        if (themeToggle && stateService) {
            // StateService已经处理了主题切换的绑定，这里只是确保它已激活
            console.log('✅ 主题切换功能通过StateService已激活');
        } else {
            console.warn('⚠️ 主题切换按钮或状态服务不可用');
        }
        
        // 绑定语言切换选择器
        const languageSelect = document.getElementById('languageSelect');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                const selectedLanguage = e.target.value;
                console.log('🌐 语言切换被触发:', selectedLanguage);
                try {
                    // 🔧 修复：使用正确的服务名 'i18nManager'
                    const i18nManager = window.OTA.getService('i18nManager') || window.getI18nManager?.() || window.i18nManager;
                    if (i18nManager && typeof i18nManager.setLanguage === 'function') {
                        i18nManager.setLanguage(selectedLanguage);
                        console.log('✅ 语言已切换为:', selectedLanguage);
                    } else {
                        console.warn('⚠️ i18n管理器不可用');
                    }
                } catch (error) {
                    console.error('❌ 语言切换失败:', error);
                }
            });
            console.log('✅ 语言切换功能已绑定');
        } else {
            console.warn('⚠️ 语言选择器不可用');
        }

        // 🔧 修复：添加登录状态变化监听器
        document.addEventListener('loginStatusChanged', (event) => {
            const { isLoggedIn, user } = event.detail;
            console.log('🔐 登录状态变化事件:', { isLoggedIn, user });

            // 确保UI正确更新
            const uiManager = window.OTA?.uiManager;
            if (uiManager && typeof uiManager.updateLoginUI === 'function') {
                uiManager.updateLoginUI(isLoggedIn);
            }

            // 更新状态管理器
            const stateService = window.OTA?.getService?.('stateService');
            if (stateService && typeof stateService.updateAllUIStatus === 'function') {
                stateService.updateAllUIStatus();
            }
        });
        console.log('✅ 登录状态变化监听器已绑定');

        // === 确保系统数据可用，然后填充下拉菜单数据 ===
        
        if (formService) {
            try {
                const appState = window.OTA.getService('appState');
                const apiService = window.OTA.getService('apiService');
                
                if (appState) {
                    let systemData = appState.get('systemData');
                    
                    // 如果系统数据不完整，尝试初始化API服务以加载静态数据
                    if (!systemData || !systemData.languages || systemData.languages.length === 0) {
                        console.log('🔄 系统数据不完整，尝试从API服务获取静态数据...');
                        if (apiService && typeof apiService.init === 'function') {
                            await apiService.init();
                            systemData = appState.get('systemData');
                        }
                    }
                    
                    // 🗑️ 已移除重复的populate调用 - 减法重构优化
                    // 下拉菜单填充已统一到FormService.populateDropdowns()
                    formService.populateDropdowns();
                    console.log('✅ 下拉菜单填充已统一到FormService');
                } else {
                    console.warn('⚠️ 应用状态不可用，跳过下拉菜单填充');
                }
            } catch (error) {
                console.error('❌ 下拉菜单填充失败:', error);
                // 🗑️ 已移除重复的应急填充逻辑 - 减法重构优化
                // 应急填充已统一到FormService.populateDropdowns()的内部逻辑
                console.error('❌ 下拉菜单填充失败，FormService内部会处理应急填充');
            }
        }
        
        // === 连接实时分析管理器到orderInput事件 ===
        
        if (realtimeAnalysisManager) {
            // 监听orderInputAnalysis事件并连接到实时分析管理器
            document.addEventListener('orderInputAnalysis', async (event) => {
                try {
                    const inputText = event.detail.inputText;
                    console.log('🔍 实时分析管理器收到分析请求:', inputText);
                    
                    // 调用实时分析管理器的分析方法
                    if (typeof realtimeAnalysisManager.analyzeText === 'function') {
                        await realtimeAnalysisManager.analyzeText(inputText);
                    } else if (typeof realtimeAnalysisManager.processInput === 'function') {
                        await realtimeAnalysisManager.processInput(inputText);
                    } else {
                        console.warn('⚠️ 实时分析管理器缺少分析方法');
                    }
                } catch (error) {
                    console.error('❌ 实时分析处理失败:', error);
                }
            });
            console.log('✅ 实时分析管理器已连接到orderInput事件');
        }
        
        // 触发工作区初始化完成事件
        document.dispatchEvent(new CustomEvent('workspaceInitialized', {
            detail: {
                timestamp: Date.now(),
                services: {
                    formService: !!formService,
                    eventService: !!eventService,
                    stateService: !!stateService,
                    priceService: !!priceService,
                    orderHistoryManager: !!orderHistoryManager,
                    realtimeAnalysisManager: !!realtimeAnalysisManager
                },
                buttons: {
                    createOrder: !!document.getElementById('createOrder'),
                    historyBtn: !!document.getElementById('historyBtn'),
                    logoutBtn: !!document.getElementById('logoutBtn'),
                    themeToggle: !!document.getElementById('themeToggle'),
                    languageSelect: !!document.getElementById('languageSelect')
                },
                features: {
                    formValidation: !!formService,
                    realtimeAnalysis: !!realtimeAnalysisManager,
                    themeToggle: !!stateService,
                    orderHistory: !!orderHistoryManager,
                    dropdownPopulation: !!formService,
                    languageSwitch: !!document.getElementById('languageSelect'),
                    priceConversion: !!priceService
                }
            }
        }));
        
        console.log('🎉 工作区初始化完成');
        
    } catch (error) {
        console.error('❌ 工作区初始化失败:', error);
    }
}

/**
 * 检查微服务是否已就绪
 * @returns {Promise<boolean>} 微服务就绪状态
 */
async function checkMicroservicesReady() {
    const requiredServices = ['formService', 'eventService', 'stateService'];

    for (const serviceName of requiredServices) {
        const service = window.OTA?.getService?.(serviceName);
        if (!service || !service.isAvailable?.()) {
            console.warn(`⚠️ 微服务 ${serviceName} 未就绪`);
            return false;
        }
    }

    console.log('✅ 所有必需微服务已就绪');
    return true;
}

/**
 * 设置登录表单事件处理器
 */
function setupLoginHandlers() {
    const loginForm = document.getElementById('loginForm');
    if (!loginForm) {
        console.warn('登录表单未找到');
        return;
    }

    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        if (!email || !password) {
            const uiService = window.OTA.getService('uiService');
            uiService?.showError('请输入邮箱和密码');
            return;
        }

        try {
            // 显示加载状态
            const loginBtn = document.getElementById('loginBtn');
            const btnText = loginBtn?.querySelector('.btn-text');
            const loadingSpinner = loginBtn?.querySelector('.loading-spinner');
            const uiService = window.OTA.getService('uiService');
            
            if (loginBtn) {
                loginBtn.disabled = true;
                if (btnText) btnText.textContent = '登录中...';
                if (loadingSpinner) {
                    loadingSpinner.classList.remove('hidden');
                }
            }
            
            uiService?.showLoading('正在登录...');

            // 调用API服务进行登录
            const apiService = window.OTA.getService('apiService');
            const result = await apiService.login(email, password, rememberMe);

            if (result.success) {
                // 登录成功，更新UI状态
                uiService?.hideLoading();
                uiService?.showSuccess('登录成功');

                // 🔧 修复：确保UI状态正确更新
                // 1. 通过uiService更新登录UI
                if (uiService && typeof uiService.updateLoginUI === 'function') {
                    uiService.updateLoginUI(true);
                }

                // 2. 通过uiManager更新登录UI（双重保险）
                const uiManager = window.OTA?.uiManager;
                if (uiManager && typeof uiManager.updateLoginUI === 'function') {
                    uiManager.updateLoginUI(true);
                }

                // 3. 触发状态更新事件
                document.dispatchEvent(new CustomEvent('loginStatusChanged', {
                    detail: { isLoggedIn: true, user: result.user }
                }));

                // 初始化工作区和微服务
                await initializeWorkspace();

                console.log('登录成功:', result);
            } else {
                throw new Error(result.message || '登录失败');
            }

        } catch (error) {
            console.error('登录失败:', error);
            const uiService = window.OTA.getService('uiService');
            uiService?.hideLoading();
            uiService?.showError(error.message || '登录失败，请重试');
        } finally {
            // 恢复登录按钮状态
            const loginBtn = document.getElementById('loginBtn');
            const btnText = loginBtn?.querySelector('.btn-text');
            const loadingSpinner = loginBtn?.querySelector('.loading-spinner');
            
            if (loginBtn) {
                loginBtn.disabled = false;
                if (btnText) btnText.textContent = '登录';
                if (loadingSpinner) {
                    loadingSpinner.classList.add('hidden');
                }
            }
        }
    });

    console.log('✅ 登录表单事件处理器已设置');
}

// 以下代码保留用于向后兼容和紧急情况下的降级启动

/**
 * 传统应用程序类 (降级使用)
 * 当新的启动协调器失败时的备用方案
 */
class LegacyOTAApplication {
    constructor() {
        this.isInitialized = false;
        this.startTime = Date.now();
        this.version = '1.0.0';
        
        // 绑定方法上下文
        this.handleUnload = this.handleUnload.bind(this);
        this.handleError = this.handleError.bind(this);
        this.handleUnhandledRejection = this.handleUnhandledRejection.bind(this);
    }
    
    /**
     * 初始化应用程序
     */
    async init() {
        try {
            logger.log('开始初始化OTA订单处理系统', 'info', {
                version: this.version,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            });
            
            // 设置性能监控
            utils.performanceMonitor.mark('app-init');
            
            // 检查浏览器兼容性
            this.checkBrowserCompatibility();
            
            // 设置错误处理
            this.setupErrorHandling();
            
            // 设置环境变量（如果有）
            this.loadEnvironmentVariables();
            
            // 初始化各个模块
            await this.initializeModules();
            
            // 设置系统事件监听
            this.setupEventListeners();
            
            // 标记初始化完成
            this.isInitialized = true;
            
            // 启动token自动检查
            this.setupTokenRefresh();
            
            const initTime = utils.performanceMonitor.measure('app-init');
            logger.log('OTA订单处理系统初始化完成', 'success', {
                initTime: `${initTime.toFixed(2)}ms`,
                modules: ['appState', 'apiService', 'geminiService', 'uiManager', 'logger']
            });
            
            // 显示系统信息
            this.displaySystemInfo();
            
        } catch (error) {
            logger.logError('系统初始化失败', error);
            this.showInitializationError(error);
        }
    }
    
    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility() {
        const browserInfo = utils.getBrowserInfo();
        const requiredFeatures = [
            'fetch',
            'localStorage',
            'Promise',
            'URLSearchParams'
        ];
        
        const missingFeatures = requiredFeatures.filter(feature => {
            switch (feature) {
                case 'fetch':
                    return !window.fetch;
                case 'localStorage':
                    return !window.localStorage;
                case 'Promise':
                    return !window.Promise;
                case 'URLSearchParams':
                    return !window.URLSearchParams;
                default:
                    return false;
            }
        });
        
        if (missingFeatures.length > 0) {
            const message = `浏览器不支持以下功能: ${missingFeatures.join(', ')}`;
            logger.log('浏览器兼容性检查失败', 'error', { 
                browserInfo, 
                missingFeatures 
            });
            throw new Error(message);
        }
        
        logger.log('浏览器兼容性检查通过', 'success', browserInfo);
    }
    
    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', this.handleError);
        
        // Promise错误处理
        window.addEventListener('unhandledrejection', this.handleUnhandledRejection);
        
        // 页面卸载处理
        window.addEventListener('beforeunload', this.handleUnload);
        
        logger.log('错误处理机制已设置', 'info');
    }
    
    /**
     * 加载环境变量
     */
    loadEnvironmentVariables() {
        // 尝试从meta标签获取配置
        const configMeta = document.querySelector('meta[name="ota-config"]');
        if (configMeta) {
            try {
                const config = JSON.parse(configMeta.content);
                Object.entries(config).forEach(([key, value]) => {
                    appState.set(`config.${key}`, value, false);
                });
                logger.log('环境配置已加载', 'info', config);
            } catch (error) {
                logger.log('环境配置解析失败', 'error', error);
            }
        }
        
        // 检查URL参数中的配置
        const urlParams = utils.parseUrlParams();
        if (urlParams.debug === 'true') {
            appState.setDebugMode(true);
            logger.log('调试模式已启用（URL参数）', 'info');
        }
        
        if (urlParams.theme) {
            appState.setTheme(urlParams.theme);
            logger.log(`主题已设置为: ${urlParams.theme}`, 'info');
        }
    }
    
    /**
     * 初始化各个模块
     */
    async initializeModules() {
        logger.log('开始初始化模块', 'info');

        // 🔧 初始化统一配置管理系统
        if (window.OTA && window.OTA.configCenter) {
            // 🗑️ 配置迁移工具已删除 - 减法重构优化
            // 配置中心已自动初始化默认配置，无需迁移
            logger.log('✅ 统一配置管理系统已初始化', 'success');
        }

        // 🚀 移除模块懒加载系统 - 避免CORS问题
        // 改用传统script标签静态加载方式
        logger.log('✅ 使用传统静态加载方式，跳过懒加载系统', 'success');

        // **修复1**: 优先初始化国际化管理器，确保在UI管理器之前完成
        if (window.getI18nManager) {
            const i18nManager = window.getI18nManager();
            i18nManager.init();
            logger.log('国际化管理器初始化完成', 'success');

            // 等待国际化管理器完全初始化
            await new Promise(resolve => setTimeout(resolve, 50));
        }

        // 🗑️ 已移除重复的微服务初始化 - 减法重构优化
        // 微服务初始化已统一到ApplicationBootstrap中，避免重复初始化
        utils.performanceMonitor.mark('services-init');

        try {
            // 🗑️ 已移除重复的配置驱动管理器初始化
            // 配置管理已统一到ApplicationBootstrap的dependencies阶段

            // 🗑️ 已移除重复的微服务初始化机制
            // 微服务初始化已统一到ApplicationBootstrap的services阶段
            logger.log('微服务初始化已委托给ApplicationBootstrap', 'info');

            // 🗑️ 已移除重复的微服务结果处理 - 减法重构优化
            // 微服务状态监控已统一到ApplicationBootstrap中
        } catch (error) {
            logger.log('微服务系统初始化异常', 'error', { error: error.message });
        }

        const servicesInitTime = utils.performanceMonitor.measure('services-init');
        logger.log('微服务系统初始化完成', 'success', {
            time: `${servicesInitTime.toFixed(2)}ms`
        });

        // 🔧 修复初始化时序：延迟初始化多订单管理器，避免循环依赖
        if (window.getMultiOrderManager) {
            // 延迟初始化，避免在系统启动时立即创建实例
            setTimeout(() => {
                try {
                    const multiOrderManager = window.OTA.getService('multiOrderManager');
                    if (multiOrderManager && typeof multiOrderManager.init === 'function') {
                        multiOrderManager.init();
                        logger.log('✅ 多订单管理器已在正确时序初始化', 'success');
                    }
                } catch (error) {
                    logger.log('多订单管理器初始化失败', 'error', error);
                }
            }, 100); // 100ms延迟，确保依赖容器完全初始化
        }

        // **修复2**: 初始化历史订单管理器
        if (window.getOrderHistoryManager) {
            const orderHistoryManager = window.getOrderHistoryManager();
            orderHistoryManager.init();
            logger.log('历史订单管理器初始化完成', 'success');
        }

        // 注意：UI-Manager已经初始化了自己的GridResizer，这里不再重复初始化
        // 避免双重初始化导致的冲突和意外的自动调整
        logger.log('跳过额外的GridResizer初始化，使用UI-Manager的实例', 'info');
        
        // 恢复登录状态（如果有有效的token）
        await this.restoreLoginState();
        
        // 如果用户已登录，尝试获取系统数据
        if (appState.get('auth.isLoggedIn')) {
            try {
                utils.performanceMonitor.mark('system-data-load');
                
                // 检查缓存的系统数据是否过期
                const lastUpdated = appState.get('systemData.lastUpdated');
                const needsRefresh = !lastUpdated || 
                    (Date.now() - new Date(lastUpdated).getTime()) > 24 * 60 * 60 * 1000;
                
                if (needsRefresh) {
                    logger.log('系统数据已过期，重新获取', 'info');
                    await apiService.getAllSystemData();
                } else {
                    logger.log('使用缓存的系统数据', 'info');
                }
                
                const dataLoadTime = utils.performanceMonitor.measure('system-data-load');
                logger.log('系统数据加载完成', 'success', { 
                    time: `${dataLoadTime.toFixed(2)}ms`,
                    fromCache: !needsRefresh
                });

                // 系统数据加载完成，UI组件选项填充将由微服务处理
                logger.log('系统数据加载完成，等待微服务初始化', 'info');
                
            } catch (error) {
                logger.log('系统数据加载失败', 'error', error);
                // 不阻断初始化，使用静态数据
            }
        }
        
        // 检查Gemini API可用性（已内嵌配置）
        if (geminiService.isAvailable()) {
            logger.log('Gemini AI服务可用', 'success', {
                model: geminiService.modelVersion,
                apiKeyConfigured: true
            });
        } else {
            logger.log('Gemini AI服务配置异常', 'error');
        }
    }

    /**
     * 验证微服务初始化状态
     */
    verifyServicesInitialization() {
        // 微服务验证逻辑将在后续阶段实现
        logger.log('微服务验证功能准备就绪', 'info');
        return true;
    }

    /**
     * 注册核心模块到懒加载系统 - 已移除
     * 现在使用传统的script标签静态加载，避免CORS问题
     */
    // registerCoreModulesForLazyLoading() - 功能已移除

    /**
     * 设置系统事件监听
     */
    setupEventListeners() {
        // 网络状态监听
        window.addEventListener('online', () => {
            logger.log('网络连接已恢复', 'success');
            appState.set('system.connected', true);
        });
        
        window.addEventListener('offline', () => {
            logger.log('网络连接已断开', 'warning');
            appState.set('system.connected', false);
        });
        
        // 可见性变化监听
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                logger.logUserAction('页面重新激活');
                // 检查是否需要刷新数据
                this.checkDataFreshness();
            } else {
                logger.logUserAction('页面进入后台');
            }
        });
        
        // 窗口大小变化监听
        const resizeHandler = utils.debounce(() => {
            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight,
                isMobile: utils.isMobile()
            };
            logger.logUserAction('窗口大小变化', viewport);
        }, 500);
        
        window.addEventListener('resize', resizeHandler);
        
        logger.log('系统事件监听已设置', 'info');
    }
    
    /**
     * 检查数据新鲜度
     */
    async checkDataFreshness() {
        if (!appState.get('auth.isLoggedIn')) return;
        
        const lastUpdated = appState.get('systemData.lastUpdated');
        if (!lastUpdated) return;
        
        const hoursAgo = (Date.now() - new Date(lastUpdated).getTime()) / (1000 * 60 * 60);
        
        if (hoursAgo > 4) { // 4小时后检查更新
            try {
                logger.log('检查系统数据更新', 'info');
                await apiService.getAllSystemData();
                logger.log('系统数据已更新', 'success');
                
                // 🗑️ 已移除重复的UI组件填充逻辑 - 减法重构优化
                // UI组件选项填充已统一到FormService.populateDropdowns()
                const formService = window.OTA?.getService?.('formService');
                if (formService && formService.populateDropdowns) {
                    formService.populateDropdowns();
                    logger.log('UI组件选项已通过FormService重新填充', 'info');
                }
            } catch (error) {
                logger.log('系统数据更新失败', 'error', error);
            }
        }
    }
    
    /**
     * 设置token自动刷新
     * 在token即将过期前尝试刷新
     */
    setupTokenRefresh() {
        const checkInterval = 30 * 60 * 1000; // 每30分钟检查一次
        
        setInterval(() => {
            const isLoggedIn = appState.get('auth.isLoggedIn');
            const tokenExpiry = appState.get('auth.tokenExpiry');
            const rememberMe = appState.get('auth.rememberMe');
            
            if (!isLoggedIn || !tokenExpiry) return;
            
            const expiryDate = new Date(tokenExpiry);
            const now = new Date();
            const timeUntilExpiry = expiryDate - now;
            const hoursUntilExpiry = timeUntilExpiry / (1000 * 60 * 60);
            
            // 如果token在1小时内过期且用户选择了记住登录
            if (hoursUntilExpiry < 1 && hoursUntilExpiry > 0 && rememberMe) {
                logger.log('Token即将过期，提醒用户', 'warning', {
                    expiresIn: `${Math.floor(hoursUntilExpiry * 60)}分钟`
                });
                
                uiManager.showAlert('登录状态即将过期，请重新登录以继续使用', 'warning', 10000);
            } else if (timeUntilExpiry <= 0) {
                // Token已过期
                logger.log('Token已过期，清除登录状态', 'info');
                appState.clearAuth();
                uiManager.updateLoginUI(false);
                uiManager.showAlert('登录状态已过期，请重新登录', 'info');
            }
        }, checkInterval);
        
        logger.log('Token自动检查已启动', 'info', { checkInterval: '30分钟' });
    }

    /**
     * 恢复登录状态
     * 检查是否有有效的token并尝试自动登录
     */
    async restoreLoginState() {
        try {
            const isLoggedIn = appState.get('auth.isLoggedIn');
            
            if (isLoggedIn) {
                logger.log('恢复登录状态成功', 'success', {
                    user: appState.get('auth.user'),
                    tokenExpiry: appState.get('auth.tokenExpiry')
                });
            } else {
                logger.log('没有有效的登录状态可恢复', 'info');
            }

            // **修复**: 移除对UI管理器的直接调用，UI更新将由状态监听器自动处理
            // uiManager.updateUI();
            
        } catch (error) {
            logger.log('恢复登录状态失败', 'error', error);
            appState.clearAuth();
            // uiManager.updateUI(); // 同样移除错误处理中的UI调用
        }
    }

    /**
     * 处理全局错误
     * @param {ErrorEvent} event - 错误事件
     */
    handleError(event) {
        const error = {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error?.stack
        };
        
        logger.log('全局JavaScript错误', 'error', error);
        
        // 如果是关键错误，显示用户友好的错误信息
        if (this.isCriticalError(event.error)) {
            this.showCriticalError(event.error);
        }
    }
    
    /**
     * 处理未捕获的Promise拒绝
     * @param {PromiseRejectionEvent} event - Promise拒绝事件
     */
    handleUnhandledRejection(event) {
        const error = {
            reason: event.reason,
            promise: event.promise
        };
        
        logger.log('未捕获的Promise拒绝', 'error', error);
        
        // 阻止默认的错误显示
        event.preventDefault();
    }
    
    /**
     * 处理页面卸载
     * @param {BeforeUnloadEvent} event - 卸载事件
     */
    handleUnload(event) {
        // 检查是否有未保存的数据
        const hasUnsavedData = this.checkUnsavedData();
        
        if (hasUnsavedData) {
            const message = '您有未保存的数据，确定要离开吗？';
            event.returnValue = message;
            return message;
        }
        
        // 记录会话结束
        const sessionDuration = Date.now() - this.startTime;
        logger.logUserAction('会话结束', {
            duration: `${(sessionDuration / 1000).toFixed(1)}秒`,
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * 检查是否有未保存的数据
     * @returns {boolean} 是否有未保存的数据
     */
    checkUnsavedData() {
        const currentOrder = appState.get('currentOrder');
        
        // 检查是否有正在编辑的订单
        if (currentOrder && currentOrder.status === 'editing') {
            return true;
        }
        
        // 检查表单是否有内容
        const orderInput = document.getElementById('orderInput');
        if (orderInput && orderInput.value.trim()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 判断是否为关键错误
     * @param {Error} error - 错误对象
     * @returns {boolean} 是否为关键错误
     */
    isCriticalError(error) {
        if (!error) return false;
        
        const criticalErrorPatterns = [
            /memory/i,
            /network/i,
            /security/i,
            /permission/i
        ];
        
        return criticalErrorPatterns.some(pattern => 
            pattern.test(error.message) || pattern.test(error.name)
        );
    }
    
    /**
     * 显示初始化错误
     * @param {Error} error - 错误对象
     */
    showInitializationError(error) {
        const errorContainer = document.createElement('div');
        errorContainer.className = 'init-error';
        errorContainer.innerHTML = `
            <div class="error-content">
                <h2>🚨 系统初始化失败</h2>
                <p><strong>错误信息:</strong> ${error.message}</p>
                <p>请尝试以下解决方案：</p>
                <ul>
                    <li>刷新页面重试</li>
                    <li>清除浏览器缓存</li>
                    <li>检查网络连接</li>
                    <li>更新浏览器版本</li>
                </ul>
                <button onclick="location.reload()" class="btn btn-primary">
                    重新加载
                </button>
            </div>
        `;
        
        errorContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        `;
        
        document.body.appendChild(errorContainer);
    }
    
    /**
     * 显示关键错误
     * @param {Error} error - 错误对象
     */
    showCriticalError(error) {
        // 创建错误提示
        const errorAlert = document.createElement('div');
        errorAlert.className = 'critical-error-alert';
        errorAlert.innerHTML = `
            <div class="alert alert-error">
                <span class="alert-icon">⚠️</span>
                <span class="alert-message">发生严重错误，请刷新页面重试</span>
                <button class="alert-action" onclick="location.reload()">刷新</button>
            </div>
        `;
        
        document.body.appendChild(errorAlert);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (errorAlert.parentNode) {
                errorAlert.parentNode.removeChild(errorAlert);
            }
        }, 3000);
    }
    
    /**
     * 显示系统信息
     */
    displaySystemInfo() {
        const systemInfo = {
            version: this.version,
            initTime: `${Date.now() - this.startTime}ms`,
            browser: utils.getBrowserInfo(),
            features: {
                geminiAI: geminiService.isAvailable(),
                localStorage: !!window.localStorage,
                clipboard: !!navigator.clipboard
            },
            performance: utils.performanceMonitor.getStats()
        };
        
        logger.log('系统信息', 'info', systemInfo);
        
        // 在调试模式下显示到控制台
        if (appState.get('config.debugMode')) {
            console.group('🚗 OTA订单处理系统');
            console.log('版本:', this.version);
            console.log('初始化时间:', systemInfo.initTime);
            console.log('浏览器:', systemInfo.browser);
            console.log('功能支持:', systemInfo.features);
            console.groupEnd();
        }
    }
    
    /**
     * 获取应用状态
     * @returns {object} 应用状态信息
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            version: this.version,
            startTime: this.startTime,
            uptime: Date.now() - this.startTime,
            modules: {
                appState: !!window.appState,
                apiService: !!window.apiService,
                geminiService: !!window.geminiService,
                uiManager: !!window.uiManager,
                logger: !!window.logger
            }
        };
    }
    
    /**
     * 重启应用
     */
    async restart() {
        logger.log('重启应用程序', 'info');
        
        try {
            // 清理现有状态
            this.cleanup();
            
            // 重新初始化
            await this.init();
            
            logger.log('应用程序重启完成', 'success');
        } catch (error) {
            logger.log('应用程序重启失败', 'error', error);
            throw error;
        }
    }
    
    /**
     * 清理应用资源
     */
    cleanup() {
        // 移除事件监听器
        window.removeEventListener('error', this.handleError);
        window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);
        window.removeEventListener('beforeunload', this.handleUnload);
        
        // 清理性能监控
        utils.performanceMonitor.clear();
        
        this.isInitialized = false;
        
        logger.log('应用程序清理完成', 'info');
    }
}

// 执行系统健康检查
function performSystemHealthCheck() {
    const logger = window.OTA.logger;
    logger.log('🔍 开始系统健康检查...', 'info');

    const checks = [
        // 核心服务检查
        { name: 'Logger服务', check: () => window.OTA && window.OTA.logger },
        { name: 'AppState服务', check: () => typeof getAppState === 'function' && window.OTA.appState },
        { name: 'Gemini服务', check: () => typeof getGeminiService === 'function' && window.OTA.geminiService },
        { name: 'API服务', check: () => typeof getAPIService === 'function' && window.OTA.apiService },
        { name: 'UI服务', check: () => typeof window.OTA?.services?.uiService === 'object' },

        // 微服务架构检查
        { name: '服务定位器', check: () => window.OTA && window.OTA.serviceLocator },
        { name: '依赖容器', check: () => window.OTA && window.OTA.container },
        { name: '微服务配置', check: () => window.OTA && window.OTA.ServicesConfig },

        // 功能管理器检查
        { name: '图片上传管理器', check: () => typeof getImageUploadManager === 'function' && window.OTA.imageUploadManager },
        { name: '货币转换器', check: () => typeof getCurrencyConverter === 'function' && window.OTA.currencyConverter },
        { name: '多订单管理器', check: () => typeof getMultiOrderManager === 'function' && window.OTA.multiOrderManager },
        { name: '分页服务管理器', check: () => typeof getPagingServiceManager === 'function' && window.OTA.pagingServiceManager },
        { name: '订单历史管理器', check: () => typeof getOrderHistoryManager === 'function' && window.OTA.orderHistoryManager },
        { name: '国际化管理器', check: () => typeof getI18nManager === 'function' && window.OTA.i18nManager },

        // DOM元素检查
        { name: '登录面板', check: () => document.getElementById('loginPanel') },
        { name: '工作区', check: () => document.getElementById('workspace') },
        { name: '订单输入框', check: () => document.getElementById('orderInput') },
        { name: '创建订单按钮', check: () => document.getElementById('createOrder') }
    ];

    let passedChecks = 0;
    let totalChecks = checks.length;

    checks.forEach(({ name, check }) => {
        try {
            const result = check();
            if (result) {
                logger.log(`✅ ${name}: 正常`, 'success');
                passedChecks++;
            } else {
                logger.log(`❌ ${name}: 未找到或未初始化`, 'error');
            }
        } catch (error) {
            logger.log(`❌ ${name}: 检查失败 - ${error.message}`, 'error');
        }
    });

    const healthScore = Math.round((passedChecks / totalChecks) * 100);
    logger.log(`🏥 系统健康检查完成: ${passedChecks}/${totalChecks} 项通过 (${healthScore}%)`,
        healthScore >= 80 ? 'success' : healthScore >= 60 ? 'warning' : 'error');

    // 性能监控信息
    if (window.performance && window.performance.memory) {
        const memory = window.performance.memory;
        logger.log(`💾 内存使用: ${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB / ${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`, 'info');
    }

    return {
        score: healthScore,
        passed: passedChecks,
        total: totalChecks,
        timestamp: new Date().toISOString()
    };
}

/**
 * 设置监控控制台命令
 * @param {object} logger - Logger实例
 */
function setupMonitoringCommands(logger) {
    // 添加全局监控控制命令
    window.monitoring = {
        /**
         * 显示监控报告
         */
        report: () => {
            return logger.printMonitoringReport();
        },
        
        /**
         * 启用/禁用实时监控
         * @param {boolean} enabled - 是否启用
         */
        setRealTime: (enabled) => {
            logger.monitoring.realTimeConsole = Boolean(enabled);
            logger.log(`实时控制台监控${enabled ? '启用' : '禁用'}`, 'info');
        },
        
        /**
         * 启用/禁用调试模式
         * @param {boolean} enabled - 是否启用
         */
        setDebug: (enabled) => {
            logger.setDebugMode(enabled);
        },
        
        /**
         * 清除监控数据
         */
        clear: () => {
            logger.monitoring.factoryFunctions.clear();
            logger.monitoring.performanceMetrics.clear();
            logger.monitoring.userInteractions = [];
            logger.monitoring.systemStates.clear();
            logger.monitoring.errorTracking.clear();
            logger.log('监控数据已清除', 'info');
        },
        
        /**
         * 获取工厂函数统计
         */
        getFactoryStats: () => {
            return logger.getFactoryFunctionStats();
        },
        
        /**
         * 获取性能统计
         */
        getPerformanceStats: () => {
            return logger.getPerformanceStats();
        },
        
        /**
         * 测试工厂函数性能
         */
        testFactoryFunctions: () => {
            logger.log('🧪 开始工厂函数性能测试...', 'info');
            
            const factoryFunctions = [
                'getAppState',
                'getGeminiService',
                'getAPIService',
                'getImageUploadManager',
                'getCurrencyConverter'
            ];
            
            factoryFunctions.forEach(funcName => {
                if (window[funcName]) {
                    const start = performance.now();
                    try {
                        const result = window[funcName]();
                        const duration = performance.now() - start;
                        logger.log(`${funcName}: ${duration.toFixed(2)}ms`, 'info');
                    } catch (error) {
                        logger.log(`${funcName}: ERROR - ${error.message}`, 'error');
                    }
                }
            });
        },
        
        /**
         * 导出监控数据
         * @param {string} format - 导出格式 (json, csv, txt)
         */
        export: (format = 'json') => {
            const report = logger.getMonitoringReport();
            const data = JSON.stringify(report, null, 2);
            
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `monitoring-report-${new Date().toISOString().slice(0, 19)}.${format}`;
            a.click();
            URL.revokeObjectURL(url);
            
            logger.log('监控报告已导出', 'success');
        }
    };
    
    logger.log('📋 监控控制台命令已设置', 'success', {
        type: 'monitoring_commands_ready',
        commands: [
            'monitoring.report() - 显示监控报告',
            'monitoring.setRealTime(true/false) - 启用/禁用实时监控',
            'monitoring.setDebug(true/false) - 启用/禁用调试模式',
            'monitoring.clear() - 清除监控数据',
            'monitoring.testFactoryFunctions() - 测试工厂函数性能',
            'monitoring.export("json") - 导出监控数据'
        ]
    });
    
    // 在控制台显示使用提示（仅在调试模式下）
    if (window.OTA?.debugMode || window.location?.hostname === 'localhost') {
        console.info(
            '%c🔍 全局监控系统已启动',
            'color: #3498db; font-size: 14px; font-weight: bold;'
        );
        console.info(
            '%c使用 monitoring.report() 查看监控报告',
            'color: #2ecc71; font-size: 12px;'
        );
        console.info(
            '%c使用 monitoring.setDebug(true) 启用调试模式',
            'color: #e67e22; font-size: 12px;'
        );
    }
}