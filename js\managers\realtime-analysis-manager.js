/**
 * 文件: realtime-analysis-manager.js
 * 路径: js\managers\realtime-analysis-manager.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.OTA.getService
 * - window.OTA.geminiService
 * - window.OTA.geminiService
 * - window.geminiService
 * - window.geminiService
 * - window.OTA.getService
 * - window.OTA.geminiService
 * - window.geminiService
 * - window.OTA.managers.PriceManager
 * - window.OTA.managers.PriceManager
 * - window.OTA.managers.StateManager
 * - window.OTA.managers.StateManager
 * - window.formManager
 * - window.OTA.getService
 * - window.OTA.uiManager
 * - window.uiManager
 * - window.OTA.container
 * - document (DOM API)
 * - console (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.OTA
 * - window.OTA.managers
 * - window.getGeminiService
 * - window.getGeminiService
 * - window.OTA.managers.RealtimeAnalysisManager
 * - window.OTA.getRealtimeAnalysisManager
 * - window.getRealtimeAnalysisManager
 * - window.OTA.container.register
 * 
 * === 类声明 ===
 * - class RealtimeAnalysisManager
 * 
 * === 函数声明 ===
 * - function getRealtimeAnalysisManager()
 * 
 * === 事件监听 ===
 * - input 事件
 * - paste 事件
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.830Z
 */

/**
 * 实时分析管理器模块 - 简化架构版本
 * 负责实时订单分析、进度显示和结果处理
 * 
 * 🚀 架构重构说明（2025-07-18）：
 * - 新架构：直接使用 parseOrder() 方法，根据返回数组长度决定单/多订单处理
 * - 自动触发 & 手动解析 现在使用完全相同的代码路径
 * - 废弃了复杂的 detectAndSplitMultiOrdersWithVerification 预检测流程
 * 
 * ⚠️ 重要提醒：
 * - 请勿恢复使用 detectAndSplitMultiOrdersWithVerification 方法
 * - 请勿恢复使用 validateMultiOrderResult 和 analyzeTextFeatures 方法
 * - 新架构更简单、更可靠、更一致
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器

    /**
     * 实时分析管理器类
     * 负责实时分析相关的所有操作
     */
    class RealtimeAnalysisManager {
        constructor(elements, uiManager) {
            this.elements = elements;
            this.uiManager = uiManager;
            
            // 实时分析相关状态（🔧 修复：移除独立的isAnalyzing状态，使用Gemini服务的统一状态）
            this.realtimeAnalysis = {
                enabled: true,
                debounceTimer: null,
                lastAnalysisTime: 0,
                progressIndicator: null
            };

            // 绑定方法上下文
            this.handleRealtimeInput = this.handleRealtimeInput.bind(this);
        }

        /**
         * 初始化实时分析管理器
         */
        init() {
            this.setupRealtimeAnalysis();
            this.createProgressIndicator();
            this.bindInputEvents();
            getLogger().log('实时分析管理器初始化完成', 'success');
        }

        /**
         * 获取Gemini服务实例（支持多种降级机制）
         * @returns {Object|null} Gemini服务实例
         */
        getGeminiService() {
            try {
                // 方案1: 通过服务定位器获取
                return window.OTA.getService('geminiService');
            } catch (error) {
                // 方案2: 直接从OTA命名空间获取
                if (window.OTA.geminiService) {
                    return window.OTA.geminiService;
                }

                // 方案3: 从全局获取
                if (window.geminiService) {
                    return window.geminiService;
                }

                // 方案4: 通过全局函数获取
                if (typeof window.getGeminiService === 'function') {
                    return window.getGeminiService();
                }

                getLogger().log('无法获取Gemini服务', 'error', error);
                return null;
            }
        }

        /**
         * 设置实时分析功能
         * 🚀 新架构说明：使用简化的parseOrder方法，根据返回数组长度决定处理方式
         */
        setupRealtimeAnalysis() {
            // 🔧 修复：使用更可靠的服务获取方式，支持多种降级机制
            let geminiService = null;

            try {
                // 方案1: 通过服务定位器获取
                geminiService = window.OTA.getService('geminiService');
            } catch (error) {
                getLogger().log('通过服务定位器获取Gemini服务失败，尝试降级方案', 'warning', error);

                // 方案2: 直接从OTA命名空间获取
                geminiService = window.OTA.geminiService;

                // 方案3: 从全局获取
                if (!geminiService) {
                    geminiService = window.geminiService;
                }

                // 方案4: 通过全局函数获取
                if (!geminiService && typeof window.getGeminiService === 'function') {
                    geminiService = window.getGeminiService();
                }
            }

            if (!geminiService) {
                getLogger().log('无法获取Gemini服务，实时分析功能将被禁用', 'error');
                this.realtimeAnalysis.enabled = false;
                return;
            }

            // 配置实时分析参数
            if (typeof geminiService.configureRealtimeAnalysis === 'function') {
                geminiService.configureRealtimeAnalysis({
                    enabled: true,
                    debounceDelay: 1500,
                    minInputLength: 15,
                    onProgress: (message, progress) => this.handleAnalysisProgress(message, progress),
                    onResult: (result) => this.handleAnalysisResult(result),
                    onError: (error) => this.handleAnalysisError(error)
                });

                getLogger().log('实时分析功能已配置（使用简化parseOrder架构）', 'info');
            } else {
                getLogger().log('Gemini服务不支持configureRealtimeAnalysis方法', 'warning');
                // 继续运行，但可能功能受限
            }
        }

        /**
         * 创建进度指示器
         */
        createProgressIndicator() {
            const indicator = document.createElement('div');
            indicator.className = 'realtime-progress';
            indicator.innerHTML = `
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="progress-text">分析中...</div>
            `;
            indicator.style.display = 'none';

            // 添加样式
            if (!document.getElementById('realtime-progress-styles')) {
                const style = document.createElement('style');
                style.id = 'realtime-progress-styles';
                style.textContent = `
                    .realtime-progress {
                        position: absolute;
                        top: 100%;
                        left: 0;
                        right: 0;
                        background: var(--color-surface);
                        border: 1px solid var(--color-border);
                        border-top: none;
                        border-radius: 0 0 8px 8px;
                        padding: 12px;
                        z-index: 1000;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    }
                    .progress-bar {
                        width: 100%;
                        height: 4px;
                        background: var(--color-border);
                        border-radius: 2px;
                        overflow: hidden;
                        margin-bottom: 8px;
                    }
                    .progress-fill {
                        height: 100%;
                        background: var(--color-primary);
                        border-radius: 2px;
                        transition: width 0.3s ease;
                        width: 0%;
                    }
                    .progress-text {
                        font-size: 12px;
                        color: var(--color-text-secondary);
                        text-align: center;
                    }
                `;
                document.head.appendChild(style);
            }

            // 添加到输入容器
            const inputContainer = this.elements.orderInput?.parentElement;
            if (inputContainer) {
                inputContainer.style.position = 'relative';
                inputContainer.appendChild(indicator);
            }

            this.realtimeAnalysis.progressIndicator = indicator;
        }

        /**
         * 绑定输入事件
         */
        bindInputEvents() {
            if (this.elements.orderInput) {
                this.elements.orderInput.addEventListener('input', this.handleRealtimeInput.bind(this));
                this.elements.orderInput.addEventListener('paste', (e) => {
                    // 粘贴后稍微延迟处理，确保内容已更新
                    setTimeout(() => this.handleRealtimeInput(e), 100);
                });
            }
        }

        /**
         * 处理实时输入
         * @param {Event} event - 输入事件
         */
        handleRealtimeInput(event) {
            // 🔧 修复：防止事件递归 - 忽略程序触发的事件
            // 暂时注释掉这个检查来测试
            // if (event.isTrusted === false && event._programmaticTrigger) {
            //     getLogger().log('⚠️ 忽略程序触发的input事件，防止递归', 'info');
            //     return;
            // }

            // 添加调试日志
            getLogger().log('🔍 handleRealtimeInput被调用', 'info', {
                isTrusted: event.isTrusted,
                inputLength: event.target.value.length
            });
            
            const inputText = event.target.value;
            
            // 清除之前的防抖定时器
            if (this.realtimeAnalysis.debounceTimer) {
                clearTimeout(this.realtimeAnalysis.debounceTimer);
            }

            // 如果输入为空，清除分析状态
            if (!inputText.trim()) {
                this.clearRealtimeAnalysis();
                this.updateGeminiStatus('请输入订单描述');
                return;
            }

            // 自动检测中文并选择语言（默认英文，含中文时选择中文）
            this.detectAndSetChineseLanguage(inputText);

            // 检查最小输入长度
            if (inputText.trim().length < 15) {
                this.updateGeminiStatus(`请继续输入... (${inputText.trim().length}/15)`);
                return;
            }

            // 设置防抖定时器
            this.realtimeAnalysis.debounceTimer = setTimeout(() => {
                this.triggerRealtimeAnalysis(inputText);
            }, 1500);

            this.updateGeminiStatus('准备分析...');
        }

        /**
         * 触发实时分析 - 简化为直接使用parseOrder（修复版）
         * 🚀 新架构：直接调用parseOrder，根据返回数组长度决定后续处理
         * @param {string} orderText - 订单文本
         */
        async triggerRealtimeAnalysis(orderText) {
            console.group('🔍 多订单数据流追踪 - 第1步：实时分析触发');
            console.log('输入文本长度:', orderText?.length);
            console.log('输入文本预览:', orderText?.substring(0, 200) + '...');
            console.groupEnd();
            
            // 🔧 修复：使用可靠的服务获取方式
            const geminiService = this.getGeminiService();
            if (!orderText || !geminiService || !geminiService.isAvailable()) {
                console.warn('❌ 输入验证失败或Gemini服务不可用');
                return;
            }

            // 防止重复分析
            if (geminiService.getStatus && geminiService.getStatus().isAnalyzing) {
                console.warn('⚠️ 分析正在进行中，跳过');
                return;
            }
            this.realtimeAnalysis.lastAnalysisTime = Date.now();
            this.showProgressIndicator();

            try {
                console.group('🔍 多订单数据流追踪 - 第2步：调用Gemini解析');
                getLogger().log('🔄 开始实时订单解析...', 'info');
                this.updateGeminiStatus('🤖 AI 解析订单内容...');
                
                // 🚀 核心修复：直接使用parseOrder方法（与手动解析保持一致）
                const parseResult = await geminiService.parseOrder(orderText, true);
                console.log('Gemini parseResult:', parseResult);
                console.groupEnd();
                
                // 添加详细的调试日志
                getLogger().log('🔍 parseOrder返回结果调试', 'info', {
                    parseResult: parseResult,
                    isArray: Array.isArray(parseResult),
                    length: parseResult?.length,
                    type: typeof parseResult
                });
                
                if (!parseResult || !Array.isArray(parseResult) || parseResult.length === 0) {
                    throw new Error('解析失败或无有效订单数据');
                }

                getLogger().log(`✅ 解析完成，检测到 ${parseResult.length} 个订单`, 'success');

                console.group('🔍 多订单数据流追踪 - 第3步：解析结果处理');
                // 🎯 根据解析结果数量决定处理方式
                getLogger().log(`🎯 根据解析结果数量决定处理方式`, 'info', {
                    orderCount: parseResult.length,
                    willTriggerMultiOrder: parseResult.length > 1
                });
                console.log('解析结果数量:', parseResult.length);
                console.log('是否触发多订单:', parseResult.length > 1);
                
                if (parseResult.length > 1) {
                    console.group('🔍 多订单数据流追踪 - 第4步：多订单处理');
                    // 多订单：触发多订单面板
                    this.updateGeminiStatus(`✅ 检测到 ${parseResult.length} 个订单`);
                    getLogger().log(`✅ 检测到 ${parseResult.length} 个订单`, 'success');
                    
                    // 构造与多订单管理器兼容的结果格式
                    const multiOrderResult = {
                        isMultiOrder: true,
                        orderCount: parseResult.length,
                        orders: parseResult,
                        confidence: this.calculateAverageConfidence(parseResult),
                        analysis: `检测到${parseResult.length}个订单`
                    };
                    console.log('构造的multiOrderResult:', multiOrderResult);
                    
                    getLogger().log('🔧 构造多订单结果格式', 'info', {
                        multiOrderResult: multiOrderResult,
                        ordersData: parseResult
                    });
                    
                    // 触发多订单事件
                    const event = new CustomEvent('multiOrderDetected', {
                        detail: {
                            multiOrderResult: multiOrderResult,
                            orderText: orderText
                        }
                    });
                    console.log('创建的event:', event);
                    console.log('event.detail:', event.detail);
                    
                    getLogger().log('🎉 即将触发多订单事件', 'info', {
                        eventType: 'multiOrderDetected',
                        orderCount: multiOrderResult.orderCount,
                        confidence: multiOrderResult.confidence
                    });
                    
                    console.log('即将dispatch事件...');
                    document.dispatchEvent(event);
                    console.log('✅ 事件已dispatch');
                    console.groupEnd();
                    
                    this.hideProgressIndicator();
                    getLogger().log('🎉 多订单事件已触发', 'success');
                    
                } else {
                    // 单订单：直接处理
                    this.updateGeminiStatus('✅ 解析完成');
                    
                    const orderData = parseResult[0];
                    this.handleAnalysisResult({
                        success: true,
                        data: orderData,
                        confidence: this.calculateDataConfidence(orderData),
                        timestamp: Date.now(),
                        source: 'parseOrder-realtime'
                    });
                    
                    getLogger().log('✅ 单订单处理完成', 'success');
                }
                
            } catch (error) {
                getLogger().logError('实时订单解析失败', error);
                this.handleAnalysisError(error);
            } finally {
                this.realtimeAnalysis.lastAnalysisTime = Date.now();
                getLogger().log('🔄 实时分析处理完成', 'info');
            }
        }

        /**
         * 计算订单数组的平均置信度
         * @param {Array} orders - 订单数组
         * @returns {number} 平均置信度
         */
        calculateAverageConfidence(orders) {
            if (!orders || orders.length === 0) return 0;
            
            const confidences = orders.map(order => this.calculateDataConfidence(order));
            return Math.round(confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length);
        }

        /**
         * ⚠️ 【已废弃】验证多订单检测结果的准确性（故障保险机制）
         * 🚫 此方法已废弃，不再使用！新架构直接使用parseOrder的返回结果
         * 
         * @deprecated 此方法属于过度设计的复杂流程，已被简化架构取代
         * @param {object} result - Gemini返回的原始结果
         * @param {string} orderText - 原始订单文本
         * @returns {object} - 验证后的结果
         */
        validateMultiOrderResult_DEPRECATED(result, orderText) {
            getLogger().log('⚠️ 警告：调用了已废弃的validateMultiOrderResult方法', 'warning');
            // 直接返回原始结果，不进行复杂验证
            return result;
        }

        /**
         * ⚠️ 【已废弃】分析文本特征以验证多订单检测
         * 🚫 此方法已废弃，不再使用！新架构依赖parseOrder的智能解析
         * 
         * @deprecated 此方法属于过度设计的复杂流程，已被简化架构取代
         * @param {string} text - 订单文本
         * @returns {object} - 文本分析结果
         */
        analyzeTextFeatures_DEPRECATED(text) {
            getLogger().log('⚠️ 警告：调用了已废弃的analyzeTextFeatures方法', 'warning');
            // 返回默认结果，不进行复杂分析
            return {
                shouldBeMultiOrder: false,
                estimatedOrderCount: 1,
                hasPagingService: false,
                hasMultipleReferences: false,
                hasMultipleDates: false,
                hasMultipleFlights: false
            };
        }

        /**
         * 处理分析进度
         * @param {string} message - 进度消息
         * @param {number} progress - 进度百分比
         */
        handleAnalysisProgress(message, progress) {
            this.updateGeminiStatus(message);
            this.updateProgressIndicator(progress);
        }

        /**
         * 处理分析结果
         * @param {object} result - 分析结果
         */
        handleAnalysisResult(result) {
            // 🔧 修复：Gemini服务已自动管理isAnalyzing状态，此处无需手动设置
            this.hideProgressIndicator();

            if (result.success && result.data) {
                // 更新应用状态
                getAppState().setCurrentOrder({
                    rawText: this.elements.orderInput.value,
                    parsedData: result.data,
                    confidence: result.confidence || this.calculateDataConfidence(result.data),
                    timestamp: result.timestamp || Date.now(),
                    source: 'realtime'
                });

                // 更新状态显示
                const confidence = result.confidence || this.calculateDataConfidence(result.data);
                this.updateGeminiStatus(`✅ 分析完成 (置信度: ${confidence}%)`);

                // 显示预览
                this.showPreviewModal();

                // 处理价格转换 - 使用简化工具
                if (result.data && window.simplePriceUtils) {
                    // 简单的价格处理
                    if (result.data.ota_price && result.data.currency) {
                        const formattedPrice = window.simplePriceUtils.formatPrice(result.data.ota_price, result.data.currency);
                        console.log('价格格式化结果:', formattedPrice);
                    }
                }

                // 🔄 改进说明：多订单检测现在在统一入口处理，此处不再需要触发
                // 单订单模式下的数据已经经过统一入口的多订单检测筛选
                // 只有orderCount=1的情况才会到达这里，无需再次触发多订单检测

                getLogger().log('实时分析完成', 'success', {
                    confidence: confidence,
                    dataKeys: Object.keys(result.data)
                });
            } else {
                this.handleAnalysisError(new Error(result.message || '分析失败'));
            }
        }

        /**
         * 处理分析错误
         * @param {Error} error - 错误对象
         */
        handleAnalysisError(error) {
            // 🔧 修复：Gemini服务已自动管理isAnalyzing状态，此处无需手动设置  
            this.hideProgressIndicator();
            this.updateGeminiStatus('❌ 分析失败，请检查输入');
            getLogger().log('实时分析失败', 'error', { error: error.message });
        }

        /**
         * 显示进度指示器
         */
        showProgressIndicator() {
            if (this.realtimeAnalysis.progressIndicator) {
                this.realtimeAnalysis.progressIndicator.style.display = 'block';
            }
        }

        /**
         * 隐藏进度指示器
         */
        hideProgressIndicator() {
            if (this.realtimeAnalysis.progressIndicator) {
                this.realtimeAnalysis.progressIndicator.style.display = 'none';
            }
        }

        /**
         * 更新进度指示器
         * @param {number} progress - 进度百分比
         */
        updateProgressIndicator(progress) {
            if (this.realtimeAnalysis.progressIndicator) {
                const progressFill = this.realtimeAnalysis.progressIndicator.querySelector('.progress-fill');
                if (progressFill) {
                    progressFill.style.width = `${Math.min(progress, 100)}%`;
                }
            }
        }

        /**
         * 清除实时分析状态
         */
        clearRealtimeAnalysis() {
            // 清除定时器
            if (this.realtimeAnalysis.debounceTimer) {
                clearTimeout(this.realtimeAnalysis.debounceTimer);
                this.realtimeAnalysis.debounceTimer = null;
            }

            // 重置状态（🔧 修复：Gemini服务已自动管理isAnalyzing状态）
            this.hideProgressIndicator();
        }

        /**
         * 更新Gemini状态显示
         * @param {string} status - 状态文本
         */
        updateGeminiStatus(status) {
            // 使用简化的消息显示
            if (window.simpleUI && window.simpleUI.showMessage) {
                window.simpleUI.showMessage(status, 'info');
            } else {
                console.log('Gemini状态:', status);
            }
        }

        /**
         * 显示预览模态框
         */
        showPreviewModal() {
            if (this.uiManager && this.uiManager.showPreviewModal) {
                this.uiManager.showPreviewModal();
            }
        }

        /**
         * 检测中文字符并自动选择语言
         * 使用统一的中文检测器确保与多订单模组逻辑同步
         * @param {string} text - 输入文本
         */
        detectAndSetChineseLanguage(text) {
            try {
                // 使用统一的扩展中文检测正则表达式
                const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;
                const hasChinese = chineseRegex.test(text);

                // 简化的语言选择策略：单一语言选择
                const languageIds = hasChinese ? [4] : [2]; // 中文[4] 或 英文[2]
                const languageDesc = hasChinese ? '中文' : '英文';

                // 应用语言选择 - 使用简化工具
                try {
                    if (window.simpleLanguageUtils) {
                        window.simpleLanguageUtils.setSelectedLanguages(languageIds);
                        getLogger().log(`检测到${languageDesc}内容，已自动设置${languageDesc}语言要求`, 'info');
                    } else {
                        getLogger().log('simpleLanguageUtils不可用', 'warning');
                    }
                } catch (e) {
                    getLogger().log('语言选择设置失败', 'warning', { error: e.message });
                }

            } catch (error) {
                getLogger().logError('自动语言检测失败', error);
            }
        }

        /**
         * 计算数据置信度
         * @param {object} orderData - 订单数据
         * @returns {number} 置信度百分比
         */
        calculateDataConfidence(orderData) {
            if (!orderData || typeof orderData !== 'object') {
                return 0;
            }

            // 重要字段权重
            const importantFields = {
                'customer_name': 15,
                'customer_contact': 10,
                'pickup_location': 20,
                'dropoff_location': 20,
                'pickup_date': 15,
                'pickup_time': 10,
                'ota_price': 5,
                'ota_reference_number': 5
            };

            let filledWeight = 0;
            let totalWeight = 0;

            for (const [field, weight] of Object.entries(importantFields)) {
                totalWeight += weight;
                const value = orderData[field];
                if (value !== null && value !== undefined && value !== '' && value !== 0) {
                    filledWeight += weight;
                }
            }

            return Math.round((filledWeight / totalWeight) * 100);
        }



        /**
         * 启用/禁用实时分析
         * @param {boolean} enabled - 是否启用
         */
        setRealtimeAnalysisEnabled(enabled) {
            this.realtimeAnalysis.enabled = enabled;
            
            if (!enabled) {
                this.clearRealtimeAnalysis();
            }

            getLogger().log(`实时分析${enabled ? '已启用' : '已禁用'}`, 'info');
        }

        /**
         * 获取实时分析状态
         * @returns {object} 实时分析状态
         */
        getRealtimeAnalysisStatus() {
            return {
                enabled: this.realtimeAnalysis.enabled,
                isAnalyzing: window.OTA.getService('geminiService').getStatus().isAnalyzing, // 🔧 修复：使用Gemini服务的统一状态
                lastAnalysisTime: this.realtimeAnalysis.lastAnalysisTime
            };
        }
    }

    // 导出到全局命名空间
    window.OTA.managers.RealtimeAnalysisManager = RealtimeAnalysisManager;

    // 🔧 修复：添加缺失的工厂函数
    let realtimeAnalysisManagerInstance = null;

    /**
     * 获取实时分析管理器实例（工厂函数）
     * @returns {RealtimeAnalysisManager} 实时分析管理器实例
     */
    function getRealtimeAnalysisManager() {
        if (!realtimeAnalysisManagerInstance) {
            // 获取UI管理器和DOM元素
            const uiManager = window.OTA.uiManager || window.uiManager;
            const elements = uiManager?.elements || {};

            realtimeAnalysisManagerInstance = new RealtimeAnalysisManager(elements, uiManager);
        }
        return realtimeAnalysisManagerInstance;
    }

    // 暴露工厂函数到全局
    window.OTA.getRealtimeAnalysisManager = getRealtimeAnalysisManager;
    window.getRealtimeAnalysisManager = getRealtimeAnalysisManager;

    // 注册到依赖容器（如果可用）
    if (window.OTA && window.OTA.container && typeof window.OTA.container.register === 'function') {
        try {
            window.OTA.container.register('realtimeAnalysisManager', () => getRealtimeAnalysisManager(), {
                singleton: true
            });
        } catch (error) {
            console.warn('[RealtimeAnalysisManager] 注册到依赖容器失败:', error.message);
        }
    }

})();
