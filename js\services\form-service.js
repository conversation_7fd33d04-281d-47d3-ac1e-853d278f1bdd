/**
 * 表单服务模块 - 微服务架构
 * 负责表单数据处理、验证和UI交互
 * 从FormManager转换而来，采用函数式编程风格
 * 
 * <AUTHOR> System
 * @created 2025-01-01
 */

(function() {
    'use strict';

    // 确保全局命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.services = window.OTA.services || {};

    /**
     * 获取依赖服务的辅助函数
     */
    function getLogger() {
        return window.OTA?.logger || window.getLogger?.() || console;
    }

    function getAppState() {
        return window.OTA?.appState || window.appState;
    }

    function getAPIService() {
        return window.OTA?.apiService || window.getAPIService?.();
    }

    /**
     * 表单服务状态
     */
    let serviceState = {
        initialized: false,
        elements: {},
        fieldMappings: {},
        validationRules: {}
    };

    /**
     * 创建字段映射关系
     * 确保Gemini返回、表单填充、API字段规格完全一致
     */
    function createFieldMappings() {
        return {
            // === API标准字段 → HTML元素ID (基于GoMyHire API文档) ===
            // 基础信息字段
            'customer_name': 'customerName',
            'customer_contact': 'customerContact',
            'customer_email': 'customerEmail',
            'sub_category_id': 'subCategoryId',
            'car_type_id': 'carTypeId',
            'driving_region_id': 'drivingRegionId',
            'incharge_by_backend_user_id': 'inchargeByBackendUserId',
            
            // 日期和时间字段 - 统一为YYYY-MM-DD格式
            'date': 'pickupDate',
            'time': 'pickupTime',
            'pickup_date': 'pickupDate',
            'pickup_time': 'pickupTime',
            'return_date': 'returnDate',
            'return_time': 'returnTime',
            
            // 地点字段
            'pickup': 'pickupLocation',
            'pickup_location': 'pickupLocation',
            'destination': 'dropoffLocation',
            'dropoff_location': 'dropoffLocation',
            
            // 订单信息字段
            'ota_reference_number': 'otaReferenceNumber',
            'ota': 'ota',
            'ota_price': 'otaPrice',
            'passenger_count': 'passengerCount',
            'luggage_number': 'luggageCount',
            'special_requirements': 'specialRequirements',
            'remarks': 'remarks',
            
            // 语言字段
            'languages_id_array': 'languagesIdArray'
        };
    }

    /**
     * 创建验证规则
     */
    function createValidationRules() {
        return {
            customerName: { required: true, minLength: 2 },
            customerContact: { required: true, pattern: /^[\d\s\-\+\(\)]+$/ },
            customerEmail: { required: false, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
            pickupDate: { required: true, type: 'date' },
            pickupTime: { required: true, type: 'time' },
            otaPrice: { required: false, type: 'number', min: 0 },
            passengerCount: { required: false, type: 'number', min: 1, max: 50 }
        };
    }

    /**
     * 缓存表单元素
     */
    function cacheFormElements() {
        const elementIds = [
            'customerName', 'customerContact', 'customerEmail',
            'pickupDate', 'pickupTime', 'returnDate', 'returnTime',
            'pickupLocation', 'dropoffLocation',
            'otaReferenceNumber', 'ota', 'otaPrice',
            'passengerCount', 'luggageCount',
            'subCategoryId', 'carTypeId', 'drivingRegionId',
            'inchargeByBackendUserId', 'specialRequirements', 'remarks'
        ];

        serviceState.elements = {};
        elementIds.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                serviceState.elements[id] = element;
            }
        });

        // 缓存特殊元素
        serviceState.elements.languagesCheckboxes = document.querySelectorAll('input[name="languagesIdArray"]');
        serviceState.elements.submitButton = document.getElementById('createOrder');
        serviceState.elements.form = document.getElementById('orderForm');

        getLogger().log('表单元素缓存完成', 'info', {
            cached: Object.keys(serviceState.elements).length
        });
    }

    /**
     * 标准化数据格式
     * @param {Object} data - 原始数据
     * @returns {Object} 标准化后的数据
     */
    function normalizeDataFormat(data) {
        if (!data || typeof data !== 'object') return {};

        const normalized = {};
        
        // 复制所有字段，确保字段名一致性
        Object.keys(data).forEach(key => {
            const value = data[key];
            if (value !== null && value !== undefined && value !== '') {
                normalized[key] = value;
            }
        });

        return normalized;
    }

    /**
     * 设置字段值
     * @param {string} fieldName - 字段名
     * @param {*} value - 字段值
     */
    function setFieldValue(fieldName, value) {
        const element = serviceState.elements[fieldName];
        if (!element) return;

        if (element.type === 'checkbox') {
            element.checked = Boolean(value);
        } else if (element.tagName === 'SELECT') {
            element.value = value;
            // 如果选项不存在，触发选项更新
            if (element.selectedIndex === -1) {
                populateSelectOptions(element, value);
            }
        } else {
            element.value = value;
        }

        // 触发change事件
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }

    /**
     * 获取元素值
     * @param {HTMLElement} element - DOM元素
     * @returns {*} 元素值
     */
    function getElementValue(element) {
        if (!element) return null;

        if (element.type === 'checkbox') {
            return element.checked;
        } else if (element.type === 'number') {
            const value = parseFloat(element.value);
            return isNaN(value) ? null : value;
        } else {
            return element.value.trim() || null;
        }
    }

    /**
     * 填充表单数据
     * @param {Object} data - 要填充的数据
     */
    function fillFormFromData(data) {
        if (!data || typeof data !== 'object') {
            getLogger().log('无效的表单数据', 'warning');
            return;
        }

        try {
            // 标准化数据格式
            const normalizedData = normalizeDataFormat(data);
            
            // 处理字段映射和填充
            Object.entries(serviceState.fieldMappings).forEach(([apiField, formField]) => {
                if (normalizedData.hasOwnProperty(apiField) && serviceState.elements[formField]) {
                    const value = normalizedData[apiField];
                    
                    if (value !== null && value !== undefined && value !== '') {
                        setFieldValue(formField, value);
                    }
                }
            });

            // 特殊处理语言数组
            if (normalizedData.languages_id_array) {
                setLanguageSelection(normalizedData.languages_id_array);
            }

            // 触发表单更新事件
            triggerFormUpdate();
            getLogger().log('表单数据填充完成', 'success', {
                fieldsFilled: Object.keys(normalizedData).length
            });

        } catch (error) {
            getLogger().log('表单数据填充失败', 'error', { error: error.message });
        }
    }

    /**
     * 收集表单数据
     * @returns {Object} 表单数据
     */
    function collectFormData() {
        const formData = {};

        try {
            // 收集基础字段数据
            Object.entries(serviceState.fieldMappings).forEach(([apiField, formField]) => {
                const element = serviceState.elements[formField];
                if (element) {
                    let value = getElementValue(element);
                    if (value !== null && value !== undefined && value !== '') {
                        formData[apiField] = value;
                    }
                }
            });

            // 特殊处理语言数组 - 转换为API格式
            const selectedLanguages = getSelectedLanguages();
            if (selectedLanguages.length > 0) {
                // 转换为对象格式 {"0":"2","1":"4"}
                const languageObject = {};
                selectedLanguages.forEach((id, index) => {
                    languageObject[index.toString()] = id.toString();
                });
                formData.languages_id_array = languageObject;
            }

            // 应用backend user email绑定逻辑
            if (!formData.incharge_by_backend_user_id) {
                const appState = getAppState();
                const currentUser = appState?.get('auth.user');
                
                // 优先在系统数据中匹配邮箱 → ID
                if (currentUser && currentUser.email) {
                    const backendUsers = appState.get('systemData.backendUsers') || [];
                    const matched = backendUsers.find(u => (u.email || '').toLowerCase() === currentUser.email.toLowerCase());
                    if (matched) {
                        formData.incharge_by_backend_user_id = matched.id;
                        getLogger().log('根据登录邮箱自动绑定后台用户', 'info', {
                            email: currentUser.email,
                            backendUserId: matched.id,
                            backendUserName: matched.name
                        });
                    } else {
                        // 若无邮件匹配，使用第一个可用的后台用户
                        const firstUser = backendUsers.find(u => u.id);
                        if (firstUser) {
                            formData.incharge_by_backend_user_id = firstUser.id;
                            getLogger().log('使用默认后台用户', 'info', {
                                backendUserId: firstUser.id,
                                backendUserName: firstUser.name
                            });
                        }
                    }
                }
            }

            // 确保关键字段为字符串
            ['sub_category_id', 'car_type_id', 'incharge_by_backend_user_id'].forEach(field => {
                if (formData[field] !== undefined) {
                    formData[field] = String(formData[field]);
                }
            });

            getLogger().log('表单数据收集完成', 'info', {
                fieldsCollected: Object.keys(formData).length
            });

            return formData;

        } catch (error) {
            getLogger().log('表单数据收集失败', 'error', { error: error.message });
            return {};
        }
    }

    /**
     * 获取选中的语言
     * @returns {Array} 选中的语言ID数组
     */
    function getSelectedLanguages() {
        const selectedLanguages = [];
        
        if (serviceState.elements.languagesCheckboxes) {
            serviceState.elements.languagesCheckboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedLanguages.push(parseInt(checkbox.value));
                }
            });
        }

        return selectedLanguages;
    }

    /**
     * 设置语言选择
     * @param {Array|Object} languageIds - 语言ID数组或对象
     */
    function setLanguageSelection(languageIds) {
        if (!serviceState.elements.languagesCheckboxes) return;

        // 先清除所有选择
        serviceState.elements.languagesCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // 处理不同格式的语言ID
        let idsToSelect = [];
        if (Array.isArray(languageIds)) {
            idsToSelect = languageIds;
        } else if (typeof languageIds === 'object') {
            idsToSelect = Object.values(languageIds).map(id => parseInt(id));
        }

        // 设置选中状态
        idsToSelect.forEach(id => {
            const checkbox = document.querySelector(`input[name="languagesIdArray"][value="${id}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });

        getLogger().log('语言选择已设置', 'info', { selectedCount: idsToSelect.length });
    }

    /**
     * 验证字段
     * @param {string} fieldName - 字段名
     * @returns {boolean} 验证结果
     */
    function validateField(fieldName) {
        const element = serviceState.elements[fieldName];
        const rule = serviceState.validationRules[fieldName];
        
        if (!element || !rule) return true;

        const value = getElementValue(element);
        const errors = [];

        // 必填验证
        if (rule.required && (value === null || value === undefined || value === '')) {
            errors.push('此字段为必填项');
        }

        // 类型验证
        if (value && rule.type) {
            if (rule.type === 'email' && rule.pattern && !rule.pattern.test(value)) {
                errors.push('请输入有效的邮箱地址');
            } else if (rule.type === 'number' && isNaN(parseFloat(value))) {
                errors.push('请输入有效的数字');
            }
        }

        // 长度验证
        if (value && rule.minLength && value.length < rule.minLength) {
            errors.push(`最少需要${rule.minLength}个字符`);
        }

        // 显示或清除错误
        if (errors.length > 0) {
            showFieldError(fieldName, errors[0]);
            return false;
        } else {
            clearFieldError(fieldName);
            return true;
        }
    }

    /**
     * 显示字段错误
     * @param {string} fieldName - 字段名
     * @param {string} message - 错误消息
     */
    function showFieldError(fieldName, message) {
        const element = serviceState.elements[fieldName];
        if (!element) return;

        element.classList.add('error');
        
        // 移除现有错误消息
        clearFieldError(fieldName);
        
        // 添加新错误消息
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.style.cssText = 'color: #dc3545; font-size: 12px; margin-top: 4px;';
        errorElement.textContent = message;
        
        element.parentNode.appendChild(errorElement);
    }

    /**
     * 清除字段错误
     * @param {string} fieldName - 字段名
     */
    function clearFieldError(fieldName) {
        const element = serviceState.elements[fieldName];
        if (!element) return;

        element.classList.remove('error');
        
        const errorElement = element.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    }

    /**
     * 验证整个表单
     * @returns {boolean} 验证结果
     */
    function validateForm() {
        let isValid = true;
        
        Object.keys(serviceState.validationRules).forEach(fieldName => {
            if (!validateField(fieldName)) {
                isValid = false;
            }
        });

        return isValid;
    }

    /**
     * 重置表单
     */
    function resetForm() {
        if (serviceState.elements.form) {
            serviceState.elements.form.reset();
        }

        // 清除所有错误状态
        Object.keys(serviceState.validationRules).forEach(fieldName => {
            clearFieldError(fieldName);
        });

        // 清除语言选择
        if (serviceState.elements.languagesCheckboxes) {
            serviceState.elements.languagesCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        triggerFormUpdate();
        getLogger().log('表单已重置', 'info');
    }

    /**
     * 触发表单更新事件
     */
    function triggerFormUpdate() {
        const event = new CustomEvent('formUpdated', {
            detail: { formData: collectFormData() }
        });
        document.dispatchEvent(event);
    }

    /**
     * 填充OTA渠道选项
     */
    function populateOtaChannelOptions() {
        try {
            const appState = getAppState();
            if (!appState) return;

            const user = appState.get('auth.user');
            if (!user) return;

            const otaSelect = serviceState.elements.ota;
            if (!otaSelect) return;

            // 获取用户特定的OTA配置
            let otaConfig = null;
            if (window.OTA?.otaChannelMapping?.getConfig) {
                otaConfig = window.OTA.otaChannelMapping.getConfig(user.id) ||
                           window.OTA.otaChannelMapping.getConfig(user.email);
            }

            // 清空现有选项
            clearSelectOptions(otaSelect);

            if (otaConfig && otaConfig.options) {
                // 用户有专属配置，直接填充选项
                otaConfig.options.forEach(option => {
                    addSelectOption(otaSelect, option.value, option.text);
                });

                // 设置默认值
                if (otaConfig.default) {
                    otaSelect.value = otaConfig.default;
                }
            } else {
                // 没有专属配置，显示占位符和通用选项
                addDefaultOption(otaSelect, '请选择OTA渠道');

                // 添加通用选项
                const commonChannels = window.OTA?.otaChannelMapping?.commonChannels || [
                    { value: 'Ctrip', text: '携程' },
                    { value: 'Klook', text: 'Klook客路' },
                    { value: 'KKday', text: 'KKday' },
                    { value: 'Other', text: '其他' }
                ];

                commonChannels.forEach(channel => {
                    addSelectOption(otaSelect, channel.value, channel.text);
                });
            }

            getLogger().log('OTA渠道选项填充完成', 'success');

        } catch (error) {
            getLogger().log('填充OTA渠道选项失败', 'error', { error: error.message });
        }
    }

    /**
     * 填充语言选项
     */
    function populateLanguageOptions() {
        try {
            const appState = getAppState();
            if (!appState) {
                getLogger().log('应用状态不可用，跳过语言选项填充', 'warning');
                return;
            }

            const languages = appState.get('systemData.languages');
            if (!Array.isArray(languages) || languages.length === 0) {
                getLogger().log('语言数据不可用', 'warning');
                return;
            }

            // 更新语言复选框组
            const languageContainer = document.getElementById('languageSelectionContainer');
            if (languageContainer) {
                updateLanguageCheckboxes(languageContainer, languages);
            }

            getLogger().log('语言选项填充完成', 'success', { count: languages.length });

        } catch (error) {
            getLogger().log('填充语言选项失败', 'error', { error: error.message });
        }
    }

    /**
     * 清空选择框选项
     * @param {HTMLSelectElement} selectElement - 选择框元素
     */
    function clearSelectOptions(selectElement) {
        selectElement.innerHTML = '';
    }

    /**
     * 添加默认选项
     * @param {HTMLSelectElement} selectElement - 选择框元素
     * @param {string} text - 选项文本
     */
    function addDefaultOption(selectElement, text) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = text;
        option.disabled = true;
        option.selected = true;
        selectElement.appendChild(option);
    }

    /**
     * 添加选择选项
     * @param {HTMLSelectElement} selectElement - 选择框元素
     * @param {string|number} value - 选项值
     * @param {string} text - 选项文本
     */
    function addSelectOption(selectElement, value, text) {
        const option = document.createElement('option');
        option.value = value;
        option.textContent = text;
        selectElement.appendChild(option);
    }

    /**
     * 更新语言复选框
     * @param {HTMLElement} container - 容器元素
     * @param {Array} languages - 语言数据
     */
    function updateLanguageCheckboxes(container, languages) {
        container.innerHTML = '';

        languages.forEach(language => {
            const checkboxWrapper = document.createElement('div');
            checkboxWrapper.className = 'checkbox-wrapper';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.name = 'languagesIdArray';
            checkbox.value = language.id;
            checkbox.id = `language_${language.id}`;

            const label = document.createElement('label');
            label.htmlFor = `language_${language.id}`;
            label.textContent = language.name;

            checkboxWrapper.appendChild(checkbox);
            checkboxWrapper.appendChild(label);
            container.appendChild(checkboxWrapper);
        });

        // 重新缓存语言复选框
        serviceState.elements.languagesCheckboxes = container.querySelectorAll('input[name="languagesIdArray"]');
    }

    /**
     * 更新语言显示
     */
    function updateLanguageDisplay() {
        const selectedLanguages = getSelectedLanguages();
        const displayText = selectedLanguages.length > 0 
            ? `已选择 ${selectedLanguages.length} 种语言`
            : '请选择语言';
        
        // 更新显示元素（如果存在）
        const displayElement = document.querySelector('.language-selection-display');
        if (displayElement) {
            displayElement.textContent = displayText;
        }

        getLogger().log('语言显示已更新', 'info', { 
            selectedCount: selectedLanguages.length,
            selectedIds: selectedLanguages 
        });
    }

    /**
     * 填充子分类下拉菜单
     * @param {Array} categories - 子分类数据
     */
    function populateSubCategories(categories) {
        try {
            const element = serviceState.elements.subCategoryId;
            if (!element || !Array.isArray(categories)) {
                getLogger().log('子分类数据无效或元素不存在', 'warning');
                return;
            }

            clearSelectOptions(element);
            addDefaultOption(element, '请选择服务类型');
            
            categories.forEach(category => {
                if (category.id && category.name) {
                    addSelectOption(element, category.id, category.name);
                }
            });

            getLogger().log('子分类选项填充完成', 'success', { count: categories.length });

        } catch (error) {
            getLogger().log('填充子分类选项失败', 'error', { error: error.message });
        }
    }

    /**
     * 填充车型下拉菜单
     * @param {Array} carTypes - 车型数据
     */
    function populateCarTypes(carTypes) {
        try {
            const element = serviceState.elements.carTypeId;
            if (!element || !Array.isArray(carTypes)) {
                getLogger().log('车型数据无效或元素不存在', 'warning');
                return;
            }

            clearSelectOptions(element);
            addDefaultOption(element, '请选择车型');
            
            carTypes.forEach(carType => {
                if (carType.id && carType.name) {
                    addSelectOption(element, carType.id, carType.name);
                }
            });

            getLogger().log('车型选项填充完成', 'success', { count: carTypes.length });

        } catch (error) {
            getLogger().log('填充车型选项失败', 'error', { error: error.message });
        }
    }

    /**
     * 填充驾驶区域下拉菜单
     * @param {Array} regions - 驾驶区域数据
     */
    function populateDrivingRegions(regions) {
        try {
            const element = serviceState.elements.drivingRegionId;
            if (!element || !Array.isArray(regions)) {
                getLogger().log('驾驶区域数据无效或元素不存在', 'warning');
                return;
            }

            clearSelectOptions(element);
            addDefaultOption(element, '请选择驾驶区域');
            
            regions.forEach(region => {
                if (region.id && region.name) {
                    addSelectOption(element, region.id, region.name);
                }
            });

            getLogger().log('驾驶区域选项填充完成', 'success', { count: regions.length });

        } catch (error) {
            getLogger().log('填充驾驶区域选项失败', 'error', { error: error.message });
        }
    }

    /**
     * 填充后台用户下拉菜单
     * @param {Array} users - 后台用户数据
     */
    function populateBackendUsers(users) {
        try {
            const element = serviceState.elements.inchargeByBackendUserId;
            if (!element || !Array.isArray(users)) {
                getLogger().log('后台用户数据无效或元素不存在', 'warning');
                return;
            }

            clearSelectOptions(element);
            addDefaultOption(element, '请选择后台用户');
            
            users.forEach(user => {
                if (user.id && user.name) {
                    addSelectOption(element, user.id, user.name);
                }
            });

            getLogger().log('后台用户选项填充完成', 'success', { count: users.length });

        } catch (error) {
            getLogger().log('填充后台用户选项失败', 'error', { error: error.message });
        }
    }

    /**
     * 填充所有下拉菜单 - 统一入口方法
     */
    function populateDropdowns() {
        try {
            const appState = getAppState();
            if (!appState) {
                getLogger().log('应用状态不可用，跳过下拉菜单填充', 'warning');
                return;
            }

            // 获取系统数据
            const systemData = appState.get('systemData');
            if (!systemData) {
                getLogger().log('系统数据不可用，跳过下拉菜单填充', 'warning');
                return;
            }

            // 填充所有下拉菜单
            populateOtaChannelOptions();
            populateLanguageOptions();
            populateSubCategories(systemData.subCategories);
            populateCarTypes(systemData.carTypes);
            populateDrivingRegions(systemData.drivingRegions);
            populateBackendUsers(systemData.backendUsers);

            getLogger().log('所有下拉菜单填充完成', 'success');

        } catch (error) {
            getLogger().log('下拉菜单填充失败', 'error', { error: error.message });
        }
    }

    /**
     * 表单服务对象 - 微服务接口
     */
    const FormService = {
        /**
         * 初始化服务
         */
        init() {
            if (serviceState.initialized) {
                getLogger().log('表单服务已经初始化', 'warning');
                return Promise.resolve();
            }

            try {
                serviceState.fieldMappings = createFieldMappings();
                serviceState.validationRules = createValidationRules();
                cacheFormElements();
                
                // 设置基础验证
                Object.keys(serviceState.validationRules).forEach(fieldName => {
                    const element = serviceState.elements[fieldName];
                    if (element) {
                        element.addEventListener('blur', () => validateField(fieldName));
                        element.addEventListener('input', () => clearFieldError(fieldName));
                    }
                });

                // 设置语言复选框事件处理
                if (serviceState.elements.languagesCheckboxes) {
                    serviceState.elements.languagesCheckboxes.forEach(checkbox => {
                        checkbox.addEventListener('change', () => {
                            updateLanguageDisplay();
                        });
                    });
                }

                serviceState.initialized = true;
                getLogger().log('表单服务初始化完成', 'success');
                return Promise.resolve();
            } catch (error) {
                getLogger().log('表单服务初始化失败', 'error', { error: error.message });
                return Promise.reject(error);
            }
        },

        /**
         * 销毁服务
         */
        destroy() {
            // 移除事件监听器
            Object.keys(serviceState.validationRules).forEach(fieldName => {
                const element = serviceState.elements[fieldName];
                if (element) {
                    element.removeEventListener('blur', () => validateField(fieldName));
                    element.removeEventListener('input', () => clearFieldError(fieldName));
                }
            });

            serviceState.initialized = false;
            serviceState.elements = {};
            serviceState.fieldMappings = {};
            serviceState.validationRules = {};
            
            getLogger().log('表单服务已销毁', 'info');
            return Promise.resolve();
        },

        /**
         * 获取服务状态
         */
        getStatus() {
            return {
                name: 'FormService',
                initialized: serviceState.initialized,
                state: serviceState.initialized ? 'ready' : 'uninitialized',
                elementCount: Object.keys(serviceState.elements).length,
                fieldMappings: Object.keys(serviceState.fieldMappings).length,
                version: '1.0.0'
            };
        },

        // 公共API方法
        fillFormFromData,
        collectFormData,
        validateForm,
        validateField,
        resetForm,
        setLanguageSelection,
        getSelectedLanguages,
        showFieldError,
        clearFieldError,
        triggerFormUpdate,
        populateOtaChannelOptions,
        populateLanguageOptions,
        populateSubCategories,
        populateCarTypes,
        populateDrivingRegions,
        populateBackendUsers,
        populateDropdowns,
        updateLanguageDisplay
    };

    // 注册到全局命名空间
    window.OTA.services.formService = FormService;

    // 向后兼容性支持
    window.formService = FormService;

    // 日志记录
    getLogger().log('表单服务模块已加载', 'info');

})();
