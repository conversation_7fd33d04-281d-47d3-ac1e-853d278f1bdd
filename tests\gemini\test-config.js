/**
 * 文件: test-config.js
 * 路径: tests\gemini\test-config.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.OTA.Registry
 * - window.OTA.Registry.registerService
 * 
 * === 对外暴露 ===
 * - window.OTA
 * - window.OTA.testConfig
 * 
 * === 类声明 ===
 * - class TestConfig
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.929Z
 */

/**
 * @CONFIG 测试配置文件
 * 🏷️ 标签: @TEST_CONFIG @PROCESSOR_TEST_DATA
 * 📝 说明: 专用处理器测试的配置文件，包含测试数据、测试规则和验证标准
 * 🎯 目标: 为单元测试提供标准化的测试数据和配置管理
 */

(function() {
    'use strict';
    
    /**
     * 测试配置管理器
     * 管理所有处理器的测试数据和配置
     */
    class TestConfig {
        constructor() {
            this.testData = new Map();
            this.validationRules = new Map();
            this.performanceThresholds = new Map();
            
            this.initializeTestData();
            this.initializeValidationRules();
            this.initializePerformanceThresholds();
        }
        
        /**
         * 初始化测试数据
         * @private
         */
        initializeTestData() {
            // Fliggy飞猪测试数据
            this.testData.set('fliggy-processor', {
                platform: 'fliggy',
                validOrders: [
                    {
                        name: '标准飞猪订单',
                        text: '飞猪旅行订单确认\n订单号：FL123456789\n联系人：张三\n电话：13800138000\n出发地：上海浦东国际机场\n目的地：上海市中心酒店\n出发日期：2024年1月15日\n出发时间：10:30\n乘客人数：2人\n行李：2件',
                        expected: {
                            platform: 'fliggy',
                            order_reference: 'FL123456789',
                            customer_name: '张三',
                            phone: '13800138000',
                            pickup_location: '上海浦东国际机场',
                            destination: '上海市中心酒店',
                            pickup_date: '15-01-2024',
                            pickup_time: '10:30',
                            passenger_count: 2,
                            luggage_count: 2
                        }
                    },
                    {
                        name: '简化飞猪订单',
                        text: 'Fliggy Order FL987654321 Contact: 李四 Phone: 13900139000 From: 广州机场 To: 珠海酒店',
                        expected: {
                            platform: 'fliggy',
                            order_reference: 'FL987654321',
                            customer_name: '李四',
                            phone: '13900139000',
                            pickup_location: '广州机场',
                            destination: '珠海酒店'
                        }
                    }
                ],
                invalidOrders: [
                    '这不是一个有效的订单信息',
                    '',
                    'Random text without any order information',
                    '订单号缺失的内容 联系人：测试 电话：123456'
                ],
                edgeCases: [
                    {
                        name: '特殊字符订单',
                        text: '飞猪订单 FL@#$123 联系人：张三&李四 电话：+86-138-0013-8000',
                        shouldHandle: true
                    }
                ]
            });
            
            // KKday测试数据
            this.testData.set('kkday-processor', {
                platform: 'kkday',
                validOrders: [
                    {
                        name: '标准KKday订单',
                        text: 'KKday体验预订确认\n订单编号：KK987654321\n预订人：王五\n联系电话：13700137000\n接送地点：台北桃园国际机场\n目的地：台北101大楼\n体验日期：2024年1月20日\n集合时间：14:00\n参与人数：3人',
                        expected: {
                            platform: 'kkday',
                            order_reference: 'KK987654321',
                            customer_name: '王五',
                            phone: '13700137000',
                            pickup_location: '台北桃园国际机场',
                            destination: '台北101大楼',
                            pickup_date: '20-01-2024',
                            pickup_time: '14:00',
                            passenger_count: 3
                        }
                    }
                ],
                invalidOrders: [
                    '无效的KKday订单',
                    'Missing order number content'
                ]
            });
            
            // Klook测试数据
            this.testData.set('klook-processor', {
                platform: 'klook',
                validOrders: [
                    {
                        name: '标准Klook订单',
                        text: 'Klook客路体验确认\n确认号：KL456789123\n旅客姓名：赵六\n手机号码：13600136000\n集合地点：香港国际机场\n目的地：香港迪士尼乐园\n体验日期：2024年1月25日\n集合时间：09:00\n参与人数：4人',
                        expected: {
                            platform: 'klook',
                            order_reference: 'KL456789123',
                            customer_name: '赵六',
                            phone: '13600136000',
                            pickup_location: '香港国际机场',
                            destination: '香港迪士尼乐园',
                            pickup_date: '25-01-2024',
                            pickup_time: '09:00',
                            passenger_count: 4
                        }
                    }
                ],
                invalidOrders: [
                    '不完整的Klook信息',
                    'Incomplete Klook data'
                ]
            });
            
            // Ctrip携程测试数据
            this.testData.set('ctrip-processor', {
                platform: 'ctrip',
                validOrders: [
                    {
                        name: '标准携程订单',
                        text: '携程用车服务订单\n产品订单号：CT789123456\n联系人：孙七\n联系电话：13500135000\n上车地点：北京首都国际机场T3航站楼\n下车地点：北京王府井大饭店\n用车日期：2024年1月30日\n用车时间：16:30\n乘车人数：2人\n行李箱数：3件',
                        expected: {
                            platform: 'ctrip',
                            order_reference: 'CT789123456',
                            customer_name: '孙七',
                            phone: '13500135000',
                            pickup_location: '北京首都国际机场T3航站楼',
                            destination: '北京王府井大饭店',
                            pickup_date: '30-01-2024',
                            pickup_time: '16:30',
                            passenger_count: 2,
                            luggage_count: 3
                        }
                    }
                ],
                invalidOrders: [
                    '错误的携程订单格式',
                    'Wrong Ctrip format'
                ]
            });
            
            // Agoda测试数据
            this.testData.set('agoda-processor', {
                platform: 'agoda',
                validOrders: [
                    {
                        name: '标准Agoda订单',
                        text: 'Agoda Booking Confirmation\nBooking Reference: AG123789456\nGuest Name: John Smith\nPhone Number: +60123456789\nPickup Location: Kuala Lumpur International Airport\nDestination: Petronas Twin Towers Hotel\nCheck-in Date: 25 January 2024\nCheck-in Time: 18:00\nNumber of Guests: 2\nLuggage: 2 pieces',
                        expected: {
                            platform: 'agoda',
                            order_reference: 'AG123789456',
                            customer_name: 'John Smith',
                            phone: '+60123456789',
                            pickup_location: 'Kuala Lumpur International Airport',
                            destination: 'Petronas Twin Towers Hotel',
                            pickup_date: '25-01-2024',
                            pickup_time: '18:00',
                            passenger_count: 2,
                            luggage_count: 2
                        }
                    }
                ],
                invalidOrders: [
                    'Invalid Agoda booking',
                    'No valid booking data'
                ]
            });
            
            // Booking.com测试数据
            this.testData.set('booking-processor', {
                platform: 'booking',
                validOrders: [
                    {
                        name: '标准Booking.com订单',
                        text: 'Booking.com Reservation Confirmation\nConfirmation Number: BK987321654\nMain Guest: Jane Doe\nContact Number: +65987654321\nProperty Address: Marina Bay Sands Singapore\nDestination: Singapore Changi Airport\nCheck-in Date: 28 January 2024\nCheck-in Time: 11:00\nGuests: 2 Adults\nPIN: 1234',
                        expected: {
                            platform: 'booking',
                            order_reference: 'BK987321654',
                            customer_name: 'Jane Doe',
                            phone: '+65987654321',
                            pickup_location: 'Marina Bay Sands Singapore',
                            destination: 'Singapore Changi Airport',
                            pickup_date: '28-01-2024',
                            pickup_time: '11:00',
                            passenger_count: 2
                        }
                    }
                ],
                invalidOrders: [
                    'Not a valid Booking.com reservation',
                    'Empty booking content'
                ]
            });
        }
        
        /**
         * 初始化验证规则
         * @private
         */
        initializeValidationRules() {
            // 通用验证规则
            const commonRules = {
                order_reference: {
                    required: true,
                    minLength: 6,
                    maxLength: 20,
                    pattern: /^[A-Z0-9\-]+$/
                },
                customer_name: {
                    required: true,
                    minLength: 2,
                    maxLength: 50
                },
                phone: {
                    required: false,
                    pattern: /^[\+\-\d\s\(\)]+$/
                },
                pickup_location: {
                    required: true,
                    minLength: 3,
                    maxLength: 200
                },
                destination: {
                    required: false,
                    maxLength: 200
                },
                pickup_date: {
                    required: false,
                    pattern: /^\d{2}-\d{2}-\d{4}$/
                },
                pickup_time: {
                    required: false,
                    pattern: /^\d{2}:\d{2}$/
                },
                passenger_count: {
                    required: false,
                    type: 'number',
                    min: 1,
                    max: 20
                },
                luggage_count: {
                    required: false,
                    type: 'number',
                    min: 0,
                    max: 50
                }
            };
            
            // 为每个处理器设置验证规则
            this.validationRules.set('fliggy-processor', {
                ...commonRules,
                order_reference: {
                    ...commonRules.order_reference,
                    pattern: /^FL[A-Z0-9]+$/
                }
            });
            
            this.validationRules.set('kkday-processor', {
                ...commonRules,
                order_reference: {
                    ...commonRules.order_reference,
                    pattern: /^KK[A-Z0-9]+$/
                }
            });
            
            this.validationRules.set('klook-processor', {
                ...commonRules,
                order_reference: {
                    ...commonRules.order_reference,
                    pattern: /^KL[A-Z0-9]+$/
                }
            });
            
            this.validationRules.set('ctrip-processor', {
                ...commonRules,
                order_reference: {
                    ...commonRules.order_reference,
                    pattern: /^CT[A-Z0-9]+$/
                }
            });
            
            this.validationRules.set('agoda-processor', {
                ...commonRules,
                order_reference: {
                    ...commonRules.order_reference,
                    pattern: /^AG[A-Z0-9]+$/
                }
            });
            
            this.validationRules.set('booking-processor', {
                ...commonRules,
                order_reference: {
                    ...commonRules.order_reference,
                    pattern: /^BK[A-Z0-9]+$/
                }
            });
        }
        
        /**
         * 初始化性能阈值
         * @private
         */
        initializePerformanceThresholds() {
            const defaultThresholds = {
                referenceIdentification: 100, // 毫秒
                orderProcessing: 500, // 毫秒
                memoryUsage: 10 * 1024 * 1024, // 10MB
                maxConcurrency: 10
            };
            
            // 为每个处理器设置性能阈值
            for (const processorName of ['fliggy-processor', 'kkday-processor', 'klook-processor', 'ctrip-processor', 'agoda-processor', 'booking-processor']) {
                this.performanceThresholds.set(processorName, { ...defaultThresholds });
            }
        }
        
        /**
         * 获取测试数据
         * @param {string} processorName - 处理器名称
         * @returns {Object} 测试数据
         */
        getTestData(processorName) {
            return this.testData.get(processorName);
        }
        
        /**
         * 获取验证规则
         * @param {string} processorName - 处理器名称
         * @returns {Object} 验证规则
         */
        getValidationRules(processorName) {
            return this.validationRules.get(processorName);
        }
        
        /**
         * 获取性能阈值
         * @param {string} processorName - 处理器名称
         * @returns {Object} 性能阈值
         */
        getPerformanceThresholds(processorName) {
            return this.performanceThresholds.get(processorName);
        }
        
        /**
         * 获取所有支持的处理器
         * @returns {Array} 处理器名称列表
         */
        getSupportedProcessors() {
            return Array.from(this.testData.keys());
        }
        
        /**
         * 验证测试结果
         * @param {string} processorName - 处理器名称
         * @param {Object} result - 测试结果
         * @returns {Object} 验证结果
         */
        validateResult(processorName, result) {
            const rules = this.getValidationRules(processorName);
            if (!rules) {
                return { valid: false, errors: ['未找到验证规则'] };
            }
            
            const errors = [];
            
            for (const [field, rule] of Object.entries(rules)) {
                const value = result[field];
                
                // 检查必填字段
                if (rule.required && (value === undefined || value === null || value === '')) {
                    errors.push(`${field} 是必填字段`);
                    continue;
                }
                
                // 如果值为空且不是必填，跳过其他验证
                if (value === undefined || value === null || value === '') {
                    continue;
                }
                
                // 类型检查
                if (rule.type === 'number' && typeof value !== 'number') {
                    errors.push(`${field} 应该是数字类型`);
                    continue;
                }
                
                // 长度检查
                if (typeof value === 'string') {
                    if (rule.minLength && value.length < rule.minLength) {
                        errors.push(`${field} 长度不能少于 ${rule.minLength} 个字符`);
                    }
                    if (rule.maxLength && value.length > rule.maxLength) {
                        errors.push(`${field} 长度不能超过 ${rule.maxLength} 个字符`);
                    }
                }
                
                // 数值范围检查
                if (typeof value === 'number') {
                    if (rule.min !== undefined && value < rule.min) {
                        errors.push(`${field} 不能小于 ${rule.min}`);
                    }
                    if (rule.max !== undefined && value > rule.max) {
                        errors.push(`${field} 不能大于 ${rule.max}`);
                    }
                }
                
                // 正则表达式检查
                if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
                    errors.push(`${field} 格式不正确`);
                }
            }
            
            return {
                valid: errors.length === 0,
                errors
            };
        }
    }
    
    // 创建全局配置实例
    const testConfig = new TestConfig();
    
    // 注册到全局
    if (typeof window !== 'undefined') {
        window.OTA = window.OTA || {};
        window.OTA.testConfig = testConfig;
        
        // 注册到服务注册中心
        if (window.OTA.Registry) {
            window.OTA.Registry.registerService('test-config', testConfig, {
                dependencies: [],
                description: '测试配置管理器，提供测试数据和验证规则'
            });
        }
    }
    
    // Node.js环境支持
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = TestConfig;
    }
    
})();
