/**
 * 文件: component-lifecycle-manager.js
 * 路径: js\core\component-lifecycle-manager.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.addEventListener
 * - window.OTA.container
 * - document (DOM API)
 * - console (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.OTA
 * - window.OTA.ComponentLifecycleManager
 * - window.OTA.componentLifecycleManager
 * - window.ComponentLifecycleManager
 * - window.componentLifecycleManager
 * - window.OTA.container.register
 * 
 * === 类声明 ===
 * - class ComponentLifecycleManager
 * 
 * === 事件监听 ===
 * - beforeunload 事件
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.652Z
 */

/**
 * 组件生命周期管理器
 * 统一管理所有UI组件的生命周期，确保正确的初始化、更新和销毁
 * 解决组件内存泄漏和状态不一致问题
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 组件生命周期管理器类
     */
    class ComponentLifecycleManager {
        constructor() {
            this.components = new Map(); // 组件注册表
            this.componentStates = new Map(); // 组件状态表
            this.componentPool = new Map(); // 组件复用池
            this.memoryUsage = new Map(); // 内存使用跟踪
            this.initialized = false;
            this.logger = null; // 延迟获取logger避免循环依赖
            this.lifecycleHooks = {
                beforeCreate: [],
                afterCreate: [],
                beforeDestroy: [],
                afterDestroy: [],
                onStateChange: [],
                onMemoryWarning: []
            };
            
            // 内存管理配置
            this.memoryConfig = {
                maxPoolSize: 10, // 复用池最大大小
                maxIdleTime: 300000, // 5分钟无活动自动回收
                gcInterval: 60000, // 1分钟检查一次内存
                memoryThreshold: 50 * 1024 * 1024 // 50MB内存阈值
            };
            
            this.gcTimer = null;
        }

        /**
         * 初始化生命周期管理器
         */
        init() {
            if (this.initialized) {
                this.log('组件生命周期管理器已经初始化', 'warning');
                return;
            }

            this.setupPageUnloadHandler();
            this.startMemoryGC(); // 启动内存管理
            this.initialized = true;
            this.log('组件生命周期管理器已初始化', 'info', {
                memoryGCEnabled: true,
                maxPoolSize: this.memoryConfig.maxPoolSize,
                gcInterval: this.memoryConfig.gcInterval
            });
        }

        /**
         * 注册组件
         * @param {string} componentId - 组件ID
         * @param {Object} componentInstance - 组件实例
         * @param {Object} options - 选项
         */
        register(componentId, componentInstance, options = {}) {
            if (this.components.has(componentId)) {
                this.log(`组件 ${componentId} 已存在，将被替换`, 'warning');
                this.destroy(componentId); // 销毁旧实例
            }

            // 触发beforeCreate钩子
            this.triggerHook('beforeCreate', { componentId, instance: componentInstance, options });

            const registration = {
                instance: componentInstance,
                options,
                createdAt: Date.now(),
                lastUpdatedAt: Date.now(),
                state: 'created'
            };

            this.components.set(componentId, registration);
            this.componentStates.set(componentId, {
                isActive: false,
                isVisible: false,
                isDirty: false,
                lastInteractionAt: null
            });

            // 为组件添加生命周期方法（如果不存在）
            this.enhanceComponent(componentInstance, componentId);

            // 触发afterCreate钩子
            this.triggerHook('afterCreate', { componentId, instance: componentInstance });

            this.log(`已注册组件: ${componentId}`, 'info', { 
                type: options.type || 'unknown',
                hasDestroy: typeof componentInstance.destroy === 'function'
            });
        }

        /**
         * 增强组件实例
         * @param {Object} componentInstance - 组件实例
         * @param {string} componentId - 组件ID
         */
        enhanceComponent(componentInstance, componentId) {
            // 保存原始destroy方法
            const originalDestroy = componentInstance.destroy;

            // 增强destroy方法
            componentInstance.destroy = () => {
                this.log(`组件 ${componentId} 开始销毁`, 'info');
                
                // 更新状态
                this.updateComponentState(componentId, { isActive: false, isVisible: false });
                
                // 调用原始destroy方法
                if (typeof originalDestroy === 'function') {
                    originalDestroy.call(componentInstance);
                }
                
                // 从管理器中移除
                this.unregister(componentId);
            };

            // 添加状态更新方法
            componentInstance._updateLifecycleState = (state) => {
                this.updateComponentState(componentId, state);
            };

            // 添加健康检查方法
            componentInstance._performHealthCheck = () => {
                return this.performHealthCheck(componentId);
            };
        }

        /**
         * 注销组件
         * @param {string} componentId - 组件ID
         */
        unregister(componentId) {
            const registration = this.components.get(componentId);
            
            if (!registration) {
                this.log(`组件 ${componentId} 不存在，无法注销`, 'warning');
                return false;
            }

            // 触发beforeDestroy钩子
            this.triggerHook('beforeDestroy', { 
                componentId, 
                instance: registration.instance 
            });

            // 清理组件状态
            this.componentStates.delete(componentId);
            this.components.delete(componentId);

            // 触发afterDestroy钩子
            this.triggerHook('afterDestroy', { componentId });

            this.log(`已注销组件: ${componentId}`, 'info');
            return true;
        }

        /**
         * 销毁组件
         * @param {string} componentId - 组件ID
         */
        destroy(componentId) {
            const registration = this.components.get(componentId);
            
            if (!registration) {
                this.log(`组件 ${componentId} 不存在，无法销毁`, 'warning');
                return false;
            }

            const componentInstance = registration.instance;

            try {
                // 如果组件有destroy方法，调用它
                if (typeof componentInstance.destroy === 'function') {
                    componentInstance.destroy();
                } else {
                    // 否则直接注销
                    this.unregister(componentId);
                }
                
                return true;
            } catch (error) {
                this.log(`销毁组件 ${componentId} 时发生错误: ${error.message}`, 'error');
                
                // 强制注销
                this.unregister(componentId);
                return false;
            }
        }

        /**
         * 更新组件状态
         * @param {string} componentId - 组件ID
         * @param {Object} stateUpdate - 状态更新
         */
        updateComponentState(componentId, stateUpdate) {
            const currentState = this.componentStates.get(componentId);
            
            if (!currentState) {
                this.log(`组件 ${componentId} 状态不存在，无法更新`, 'warning');
                return;
            }

            const oldState = { ...currentState };
            const newState = { ...currentState, ...stateUpdate, lastUpdatedAt: Date.now() };
            
            this.componentStates.set(componentId, newState);

            // 更新组件注册信息
            const registration = this.components.get(componentId);
            if (registration) {
                registration.lastUpdatedAt = Date.now();
            }

            // 触发状态变更钩子
            this.triggerHook('onStateChange', {
                componentId,
                oldState,
                newState,
                stateUpdate
            });

            this.log(`组件 ${componentId} 状态已更新`, 'debug', { stateUpdate });
        }

        /**
         * 获取组件状态
         * @param {string} componentId - 组件ID
         * @returns {Object|null} 组件状态
         */
        getComponentState(componentId) {
            return this.componentStates.get(componentId) || null;
        }

        /**
         * 获取组件实例
         * @param {string} componentId - 组件ID
         * @returns {Object|null} 组件实例
         */
        getComponent(componentId) {
            const registration = this.components.get(componentId);
            return registration ? registration.instance : null;
        }

        /**
         * 获取所有组件
         * @returns {Array} 组件列表
         */
        getAllComponents() {
            const components = [];
            
            for (const [componentId, registration] of this.components.entries()) {
                const state = this.componentStates.get(componentId);
                
                components.push({
                    id: componentId,
                    type: registration.options.type || 'unknown',
                    createdAt: registration.createdAt,
                    lastUpdatedAt: registration.lastUpdatedAt,
                    state: registration.state,
                    isActive: state?.isActive || false,
                    isVisible: state?.isVisible || false
                });
            }
            
            return components;
        }

        /**
         * 执行健康检查
         * @param {string} componentId - 组件ID（可选，不提供则检查所有组件）
         * @returns {Object} 健康检查结果
         */
        performHealthCheck(componentId = null) {
            const results = {
                timestamp: Date.now(),
                totalComponents: this.components.size,
                healthyComponents: 0,
                issues: []
            };

            const componentsToCheck = componentId ? 
                [componentId] : 
                Array.from(this.components.keys());

            for (const id of componentsToCheck) {
                const registration = this.components.get(id);
                const state = this.componentStates.get(id);

                if (!registration || !state) {
                    results.issues.push({
                        componentId: id,
                        type: 'missing_data',
                        message: '组件注册或状态数据缺失'
                    });
                    continue;
                }

                let isHealthy = true;

                // 检查组件实例是否仍然有效
                const instance = registration.instance;
                if (!instance) {
                    results.issues.push({
                        componentId: id,
                        type: 'missing_instance',
                        message: '组件实例缺失'
                    });
                    isHealthy = false;
                }

                // 检查DOM元素是否仍然存在
                if (instance && instance.container) {
                    const container = typeof instance.container === 'string' ?
                        document.getElementById(instance.container) :
                        instance.container;

                    if (!container || !document.contains(container)) {
                        results.issues.push({
                            componentId: id,
                            type: 'dom_detached',
                            message: 'DOM容器已从文档中移除'
                        });
                        isHealthy = false;
                    }
                }

                // 检查组件是否长时间未更新
                const timeSinceUpdate = Date.now() - registration.lastUpdatedAt;
                if (timeSinceUpdate > 300000) { // 5分钟
                    results.issues.push({
                        componentId: id,
                        type: 'stale_component',
                        message: `组件超过${Math.round(timeSinceUpdate / 60000)}分钟未更新`
                    });
                }

                if (isHealthy) {
                    results.healthyComponents++;
                }
            }

            this.log(`健康检查完成: ${results.healthyComponents}/${results.totalComponents} 组件健康`, 
                results.issues.length > 0 ? 'warning' : 'info', 
                { issues: results.issues.length });

            return results;
        }

        /**
         * 添加生命周期钩子
         * @param {string} hookName - 钩子名称
         * @param {Function} callback - 回调函数
         */
        addHook(hookName, callback) {
            if (this.lifecycleHooks[hookName]) {
                this.lifecycleHooks[hookName].push(callback);
                this.log(`已添加 ${hookName} 钩子`, 'debug');
            } else {
                this.log(`未知的钩子名称: ${hookName}`, 'warning');
            }
        }

        /**
         * 触发生命周期钩子
         * @param {string} hookName - 钩子名称
         * @param {Object} data - 数据
         */
        triggerHook(hookName, data) {
            const hooks = this.lifecycleHooks[hookName];
            if (hooks && hooks.length > 0) {
                hooks.forEach(callback => {
                    try {
                        callback(data);
                    } catch (error) {
                        this.log(`执行 ${hookName} 钩子时发生错误: ${error.message}`, 'error');
                    }
                });
            }
        }

        /**
         * 设置页面卸载处理器
         */
        setupPageUnloadHandler() {
            window.addEventListener('beforeunload', () => {
                this.log('页面即将卸载，开始清理所有组件', 'info');
                this.destroyAll();
            });
        }

        /**
         * 销毁所有组件
         */
        destroyAll() {
            const componentIds = Array.from(this.components.keys());
            
            this.log(`开始销毁 ${componentIds.length} 个组件`, 'info');

            for (const componentId of componentIds) {
                try {
                    this.destroy(componentId);
                } catch (error) {
                    this.log(`销毁组件 ${componentId} 时发生错误: ${error.message}`, 'error');
                }
            }

            this.log('所有组件已销毁', 'info');
        }

        /**
         * 获取管理器状态
         * @returns {Object} 状态信息
         */
        getStatus() {
            return {
                initialized: this.initialized,
                totalComponents: this.components.size,
                activeComponents: Array.from(this.componentStates.values())
                    .filter(state => state.isActive).length,
                visibleComponents: Array.from(this.componentStates.values())
                    .filter(state => state.isVisible).length,
                components: this.getAllComponents()
            };
        }

        /**
         * 获取组件复用实例
         * @param {string} componentType - 组件类型
         * @returns {Object|null} 复用的组件实例
         */
        getFromPool(componentType) {
            const pool = this.componentPool.get(componentType);
            if (pool && pool.length > 0) {
                const instance = pool.pop();
                this.log(`从复用池获取组件: ${componentType}`, 'debug');
                return instance;
            }
            return null;
        }

        /**
         * 将组件放入复用池
         * @param {string} componentType - 组件类型
         * @param {Object} instance - 组件实例
         */
        returnToPool(componentType, instance) {
            if (!this.componentPool.has(componentType)) {
                this.componentPool.set(componentType, []);
            }

            const pool = this.componentPool.get(componentType);
            if (pool.length < this.memoryConfig.maxPoolSize) {
                // 重置组件状态
                if (typeof instance.reset === 'function') {
                    instance.reset();
                }
                
                instance._pooledAt = Date.now();
                pool.push(instance);
                this.log(`组件已放入复用池: ${componentType}`, 'debug');
            } else {
                // 池已满，直接销毁
                this.destroyInstance(instance);
                this.log(`复用池已满，销毁组件: ${componentType}`, 'debug');
            }
        }

        /**
         * 启动内存垃圾回收
         */
        startMemoryGC() {
            if (this.gcTimer) {
                clearInterval(this.gcTimer);
            }

            this.gcTimer = setInterval(() => {
                this.performMemoryGC();
            }, this.memoryConfig.gcInterval);

            this.log('内存垃圾回收已启动', 'info');
        }

        /**
         * 执行内存垃圾回收
         */
        performMemoryGC() {
            const now = Date.now();
            let reclaimedCount = 0;

            // 清理复用池中的过期组件
            for (const [componentType, pool] of this.componentPool.entries()) {
                const activePool = pool.filter(instance => {
                    const isExpired = now - instance._pooledAt > this.memoryConfig.maxIdleTime;
                    if (isExpired) {
                        this.destroyInstance(instance);
                        reclaimedCount++;
                        return false;
                    }
                    return true;
                });
                this.componentPool.set(componentType, activePool);
            }

            // 清理长时间未活动的组件
            for (const [componentId, state] of this.componentStates.entries()) {
                if (state.lastInteractionAt && 
                    now - state.lastInteractionAt > this.memoryConfig.maxIdleTime * 2) {
                    this.destroy(componentId);
                    reclaimedCount++;
                }
            }

            if (reclaimedCount > 0) {
                this.log(`内存回收完成，回收了 ${reclaimedCount} 个组件`, 'info');
            }

            // 检查内存使用情况
            this.checkMemoryUsage();
        }

        /**
         * 检查内存使用情况
         */
        checkMemoryUsage() {
            if (performance.memory) {
                const used = performance.memory.usedJSHeapSize;
                const total = performance.memory.totalJSHeapSize;
                const limit = performance.memory.jsHeapSizeLimit;

                this.memoryUsage.set('used', used);
                this.memoryUsage.set('total', total);
                this.memoryUsage.set('limit', limit);

                // 如果内存使用超过阈值，触发警告
                if (used > this.memoryConfig.memoryThreshold) {
                    this.triggerHook('onMemoryWarning', {
                        used,
                        total,
                        limit,
                        componentCount: this.components.size,
                        poolSize: Array.from(this.componentPool.values())
                            .reduce((sum, pool) => sum + pool.length, 0)
                    });

                    this.log('内存使用警告', 'warning', {
                        usedMB: (used / 1024 / 1024).toFixed(2),
                        totalMB: (total / 1024 / 1024).toFixed(2),
                        usage: ((used / total) * 100).toFixed(1) + '%'
                    });

                    // 强制执行更激进的回收策略
                    this.performAggressiveGC();
                }
            }
        }

        /**
         * 执行激进的垃圾回收
         */
        performAggressiveGC() {
            this.log('执行激进内存回收', 'warning');

            // 清空所有复用池
            for (const [componentType, pool] of this.componentPool.entries()) {
                pool.forEach(instance => this.destroyInstance(instance));
                pool.length = 0;
            }

            // 销毁非活动组件
            const inactiveComponents = [];
            for (const [componentId, state] of this.componentStates.entries()) {
                if (!state.isActive && !state.isVisible) {
                    inactiveComponents.push(componentId);
                }
            }

            inactiveComponents.forEach(componentId => {
                this.destroy(componentId);
            });

            this.log(`激进回收完成，回收了 ${inactiveComponents.length} 个非活动组件`, 'info');
        }

        /**
         * 销毁组件实例
         * @param {Object} instance - 组件实例
         */
        destroyInstance(instance) {
            try {
                if (typeof instance.destroy === 'function') {
                    instance.destroy();
                }
                
                // 清理引用
                Object.keys(instance).forEach(key => {
                    if (instance.hasOwnProperty(key)) {
                        delete instance[key];
                    }
                });
            } catch (error) {
                this.log('销毁组件实例时发生错误', 'error', error);
            }
        }

        /**
         * 获取内存统计信息
         * @returns {Object} 内存统计
         */
        getMemoryStats() {
            const poolStats = {};
            let totalPoolSize = 0;

            for (const [componentType, pool] of this.componentPool.entries()) {
                poolStats[componentType] = pool.length;
                totalPoolSize += pool.length;
            }

            return {
                componentCount: this.components.size,
                totalPoolSize,
                poolStats,
                memoryUsage: Object.fromEntries(this.memoryUsage),
                config: this.memoryConfig
            };
        }

        /**
         * 日志输出
         * @param {string} message - 消息
         * @param {string} level - 级别
         * @param {Object} data - 数据
         */
        log(message, level = 'info', data = null) {
            if (!this.logger) {
                try {
                    this.logger = getLogger ? getLogger() : null;
                } catch (e) {
                    // Logger可能还未初始化
                }
            }

            if (this.logger) {
                this.logger.log(`[LifecycleManager] ${message}`, level, data);
            } else {
                console.log(`[LifecycleManager] ${message}`, data);
            }
        }
    }

    // 创建全局实例
    const componentLifecycleManager = new ComponentLifecycleManager();

    // 导出到OTA命名空间
    window.OTA.ComponentLifecycleManager = ComponentLifecycleManager;
    window.OTA.componentLifecycleManager = componentLifecycleManager;

    // 向后兼容
    window.ComponentLifecycleManager = ComponentLifecycleManager;
    window.componentLifecycleManager = componentLifecycleManager;

    // 🔧 注册到依赖容器（如果可用）
    if (window.OTA && window.OTA.container && typeof window.OTA.container.register === 'function') {
        try {
            window.OTA.container.register('lifecycleManager', () => componentLifecycleManager, {
                singleton: true
            });
        } catch (error) {
            console.warn('[ComponentLifecycleManager] 注册到依赖容器失败:', error.message);
        }
    }

})();