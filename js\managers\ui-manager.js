/**
 * UI管理器模块 (优化版)
 * 负责UI协调、通知显示和界面状态管理
 * 保留核心功能，去除过度复杂的部分
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    /**
     * UI管理器类 (优化版)
     * 保留核心UI协调功能，简化实现逻辑
     */
    class UIManager {
        constructor() {
            this.initialized = false;
            this.notificationTimeout = null;
            this.managers = {
                form: null  // 子管理器支持
            };
        }

        /**
         * 初始化UI管理器
         */
        init() {
            if (this.initialized) {
                getLogger().log('UI管理器已经初始化', 'warning');
                return;
            }

            this.createNotificationContainer();
            this.setupFormManagerProxy();
            this.initialized = true;
            getLogger().log('UI管理器初始化完成', 'success');
        }

        /**
         * 创建通知容器 (简化版)
         */
        createNotificationContainer() {
            if (document.getElementById('notification-container')) {
                return;
            }

            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }

        /**
         * 设置表单管理器代理
         */
        setupFormManagerProxy() {
            this.managers.form = {
                populateOtaChannelOptions: () => {
                    this.populateOtaChannelOptions();
                }
            };
        }

        /**
         * 显示通知 (核心方法)
         */
        showAlert(message, type = 'info', duration = 3000) {
            if (!message) return;

            try {
                const container = document.getElementById('notification-container');
                if (!container) return;

                // 清除之前的通知
                this.clearNotification();

                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.style.cssText = `
                    background: ${this.getNotificationColor(type)};
                    color: white;
                    padding: 15px 20px;
                    border-radius: 6px;
                    margin-bottom: 10px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    font-size: 14px;
                    line-height: 1.4;
                    max-width: 100%;
                    word-wrap: break-word;
                    pointer-events: auto;
                    cursor: pointer;
                    transform: translateX(100%);
                    transition: transform 0.3s ease-out;
                `;
                
                notification.textContent = message;
                container.appendChild(notification);

                // 动画显示
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 10);

                // 点击关闭
                notification.onclick = () => {
                    this.hideNotification(notification);
                };

                // 自动关闭
                if (duration > 0) {
                    this.notificationTimeout = setTimeout(() => {
                        this.hideNotification(notification);
                    }, duration);
                }

                getLogger().log('通知已显示', 'info', { message, type });

            } catch (error) {
                console.error('显示通知失败:', error);
                // 降级到原生alert
                alert(message);
            }
        }

        /**
         * 获取通知背景色
         */
        getNotificationColor(type) {
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8',
                primary: '#007bff'
            };
            return colors[type] || colors.info;
        }

        /**
         * 隐藏通知
         */
        hideNotification(notification) {
            if (notification && notification.parentNode) {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }

        /**
         * 清除当前通知
         */
        clearNotification() {
            if (this.notificationTimeout) {
                clearTimeout(this.notificationTimeout);
                this.notificationTimeout = null;
            }

            const container = document.getElementById('notification-container');
            if (container) {
                container.innerHTML = '';
            }
        }

        /**
         * 显示错误消息 (便捷方法)
         */
        showError(message) {
            this.showAlert(message, 'error', 5000);
        }

        /**
         * 显示成功消息 (便捷方法)
         */
        showSuccess(message) {
            this.showAlert(message, 'success', 3000);
        }

        /**
         * 显示警告消息 (便捷方法)
         */
        showWarning(message) {
            this.showAlert(message, 'warning', 4000);
        }

        /**
         * 显示信息消息 (便捷方法)
         */
        showInfo(message) {
            this.showAlert(message, 'info', 3000);
        }

        /**
         * 更新登录UI状态 (关键集成方法)
         */
        updateLoginUI(isLoggedIn) {
            try {
                // 获取所有相关UI元素
                const workspace = document.getElementById('workspace');
                const loginPanel = document.getElementById('loginPanel');
                const userInfo = document.getElementById('userInfo');
                const historyBtn = document.getElementById('historyBtn');
                const logoutBtn = document.getElementById('logoutBtn');

                if (isLoggedIn) {
                    // 登录状态：显示工作区，隐藏登录面板，显示用户信息和按钮
                    if (workspace) workspace.style.display = 'block';
                    if (loginPanel) loginPanel.style.display = 'none';
                    if (userInfo) userInfo.style.display = 'block';
                    if (historyBtn) historyBtn.style.display = 'inline-block';
                    if (logoutBtn) logoutBtn.style.display = 'inline-block';

                    // 初始化工作区功能
                    this.initializeWorkspace();

                    getLogger().log('UI已切换到工作区模式', 'info');
                } else {
                    // 未登录状态：显示登录面板，隐藏工作区和用户相关按钮
                    if (workspace) workspace.style.display = 'none';
                    if (loginPanel) loginPanel.style.display = 'block';
                    if (userInfo) userInfo.style.display = 'none';
                    if (historyBtn) historyBtn.style.display = 'none';
                    if (logoutBtn) logoutBtn.style.display = 'none';

                    getLogger().log('UI已切换到登录模式', 'info');
                }

            } catch (error) {
                getLogger().log('更新登录UI失败', 'error', { error: error.message });
            }
        }

        /**
         * 初始化工作区 (简化版)
         */
        initializeWorkspace() {
            // 填充下拉菜单
            this.populateLanguageOptions();
            this.populateOtaChannelOptions();
            
            // 触发其他初始化
            document.dispatchEvent(new CustomEvent('workspaceInitialized'));
        }

        /**
         * 填充语言选项 (关键集成方法)
         */
        populateLanguageOptions() {
            try {
                const appState = window.OTA?.appState || window.appState;
                if (!appState) {
                    getLogger().log('应用状态不可用，跳过语言选项填充', 'warning');
                    return;
                }

                const languages = appState.get('systemData.languages');
                if (!Array.isArray(languages) || languages.length === 0) {
                    getLogger().log('语言数据不可用', 'warning');
                    return;
                }

                // 更新语言复选框组
                const languageContainer = document.getElementById('languageSelectionContainer');
                if (languageContainer) {
                    this.updateLanguageCheckboxes(languageContainer, languages);
                }

                // 更新其他语言选择UI
                this.updateLanguageDropdowns(languages);

                getLogger().log('语言选项填充完成', 'success', { count: languages.length });

            } catch (error) {
                getLogger().log('填充语言选项失败', 'error', { error: error.message });
            }
        }

        /**
         * 更新语言复选框
         */
        updateLanguageCheckboxes(container, languages) {
            // 清空现有选项
            container.innerHTML = '';

            languages.forEach(language => {
                if (language.id && language.name) {
                    const checkboxWrapper = document.createElement('div');
                    checkboxWrapper.className = 'language-checkbox-wrapper';
                    checkboxWrapper.innerHTML = `
                        <label class="language-checkbox-label">
                            <input type="checkbox" name="languagesIdArray" value="${language.id}">
                            <span class="language-name">${language.name}</span>
                        </label>
                    `;
                    container.appendChild(checkboxWrapper);
                }
            });
        }

        /**
         * 更新语言下拉菜单
         */
        updateLanguageDropdowns(languages) {
            const dropdowns = document.querySelectorAll('select[data-language-dropdown]');
            dropdowns.forEach(dropdown => {
                this.populateSelectOptions(dropdown, languages, '请选择语言');
            });
        }

        /**
         * 填充OTA渠道选项 (关键集成方法)
         */
        populateOtaChannelOptions() {
            try {
                const appState = window.OTA?.appState || window.appState;
                if (!appState) return;

                const userId = appState.get('auth.user.id');
                if (!userId) return;

                // 获取用户OTA配置
                const otaConfig = window.OTA?.getOtaConfigForUser?.(userId);
                if (!otaConfig) return;

                const otaSelect = document.getElementById('otaChannel');
                if (otaSelect) {
                    this.populateSelectOptions(otaSelect, 
                        otaConfig.options.map(option => ({ id: option, name: option })),
                        '请选择OTA渠道'
                    );

                    // 设置默认值
                    if (otaConfig.default) {
                        otaSelect.value = otaConfig.default;
                    }
                }

                getLogger().log('OTA渠道选项填充完成', 'success');

            } catch (error) {
                getLogger().log('填充OTA渠道选项失败', 'error', { error: error.message });
            }
        }

        /**
         * 填充选择框选项 (通用方法)
         */
        populateSelectOptions(selectElement, options, defaultText = '请选择') {
            if (!selectElement || !Array.isArray(options)) return;

            // 清空选项
            selectElement.innerHTML = '';

            // 添加默认选项
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = defaultText;
            selectElement.appendChild(defaultOption);

            // 添加数据选项
            options.forEach(option => {
                if (option.id && option.name) {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.id;
                    optionElement.textContent = option.name;
                    selectElement.appendChild(optionElement);
                }
            });
        }

        /**
         * 显示错误模态框 (简化版)
         */
        showErrorModal(error, context = '') {
            const message = typeof error === 'string' ? error : 
                           error?.message || '发生未知错误';
            
            const fullMessage = context ? `${context}: ${message}` : message;
            this.showError(fullMessage);
            
            // 记录到控制台用于调试
            console.error('Error Modal:', error, context);
        }

        /**
         * 更新UI (通用刷新方法)
         */
        updateUI() {
            try {
                // 检查登录状态并更新UI
                const appState = window.OTA?.appState || window.appState;
                if (appState) {
                    const isLoggedIn = appState.get('auth.isLoggedIn');
                    this.updateLoginUI(isLoggedIn);
                }

                // 触发UI更新事件
                document.dispatchEvent(new CustomEvent('uiUpdated'));
                
                getLogger().log('UI更新完成', 'info');

            } catch (error) {
                getLogger().log('UI更新失败', 'error', { error: error.message });
            }
        }

        /**
         * 显示加载状态 (简化版)
         */
        showLoading(message = '加载中...') {
            const existingLoader = document.getElementById('global-loader');
            if (existingLoader) return;

            const loader = document.createElement('div');
            loader.id = 'global-loader';
            loader.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255,255,255,0.9);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            `;
            
            loader.innerHTML = `
                <div style="
                    width: 40px;
                    height: 40px;
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid #007bff;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin-bottom: 15px;
                "></div>
                <div style="font-size: 14px; color: #666;">${message}</div>
            `;

            // 添加旋转动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
            document.body.appendChild(loader);
        }

        /**
         * 隐藏加载状态
         */
        hideLoading() {
            const loader = document.getElementById('global-loader');
            if (loader) {
                loader.remove();
            }
        }

        /**
         * 获取管理器状态
         */
        getManagerStatus() {
            return {
                initialized: this.initialized,
                hasNotificationContainer: !!document.getElementById('notification-container'),
                managersAvailable: Object.keys(this.managers).length
            };
        }

        /**
         * 销毁UI管理器
         */
        destroy() {
            this.clearNotification();
            this.hideLoading();
            
            const container = document.getElementById('notification-container');
            if (container) {
                container.remove();
            }

            this.initialized = false;
            getLogger().log('UI管理器已销毁', 'info');
        }
    }

    // 导出到全局命名空间
    window.OTA.managers.UIManager = UIManager;

    // 创建全局实例并注册到依赖容器
    if (window.OTA && window.OTA.container) {
        try {
            window.OTA.container.register('uiManager', () => {
                const manager = new UIManager();
                manager.init();
                return manager;
            }, { singleton: true });
            getLogger().log('UIManager已注册到依赖容器', 'info');
        } catch (error) {
            console.warn('UIManager注册到依赖容器失败:', error.message);
        }
    }

    // 向后兼容性暴露
    if (!window.uiManager && !window.OTA.uiManager) {
        const globalInstance = new UIManager();
        globalInstance.init();
        window.OTA.uiManager = globalInstance;
        window.uiManager = globalInstance;
    }

})();