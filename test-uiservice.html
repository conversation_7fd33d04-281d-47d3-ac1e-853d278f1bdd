<!DOCTYPE html>
<html>
<head>
    <title>Test UIService Registration</title>
</head>
<body>
    <h1>UIService Registration Test</h1>
    <div id="log"></div>

    <!-- Load required scripts in the correct order -->
    <!-- Core infrastructure -->
    <script src="js/core/api-key-manager.js"></script>
    <script src="js/services/logger.js"></script>
    <script src="js/utils/utils.js"></script>
    
    <!-- Configuration and containers -->
    <script src="js/config/services-config.js"></script>
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/unified-config-center.js"></script>
    
    <!-- Bootstrap -->
    <script src="js/bootstrap/application-bootstrap.js"></script>
    <script src="js/bootstrap/app-state.js"></script>
    
    <!-- Services -->
    <script src="js/services/ui-service.js"></script>
    
    <script>
        async function testUIService() {
            const log = document.getElementById('log');
            
            function logMessage(message) {
                log.innerHTML += '<div>' + message + '</div>';
                console.log(message);
            }
            
            try {
                logMessage('🧪 Starting UIService registration test...');
                
                // Check if ui-service.js loaded
                if (window.OTA && window.OTA.services && window.OTA.services.uiService) {
                    logMessage('✅ ui-service.js loaded successfully');
                } else {
                    logMessage('❌ ui-service.js not loaded or not registered to window.OTA.services.uiService');
                    return;
                }
                
                // Initialize bootstrap
                if (!window.OTA || !window.OTA.ApplicationBootstrap) {
                    logMessage('❌ ApplicationBootstrap not available');
                    return;
                }
                
                const bootstrap = new window.OTA.ApplicationBootstrap();
                logMessage('✅ Bootstrap created');
                
                // Start the application
                const result = await bootstrap.start();
                if (result.success) {
                    logMessage('✅ Bootstrap started successfully');
                } else {
                    logMessage('❌ Bootstrap failed: ' + result.error);
                    return;
                }
                
                // Test uiService retrieval
                try {
                    const uiService = window.OTA.getService('uiService');
                    if (uiService) {
                        logMessage('✅ uiService retrieved successfully via getService()');
                        
                        // Test uiService methods
                        if (typeof uiService.updateLoginUI === 'function') {
                            logMessage('✅ uiService.updateLoginUI method available');
                        } else {
                            logMessage('❌ uiService.updateLoginUI method not available');
                        }
                        
                        if (typeof uiService.getStatus === 'function') {
                            const status = uiService.getStatus();
                            logMessage('✅ uiService status: ' + JSON.stringify(status));
                        } else {
                            logMessage('❌ uiService.getStatus method not available');
                        }
                        
                    } else {
                        logMessage('❌ uiService not retrieved from getService()');
                    }
                } catch (error) {
                    logMessage('❌ Error getting uiService: ' + error.message);
                }
                
                // Test app state login UI update
                try {
                    if (window.OTA.appState && typeof window.OTA.appState.triggerLoginUIUpdate === 'function') {
                        logMessage('🧪 Testing AppState.triggerLoginUIUpdate...');
                        window.OTA.appState.triggerLoginUIUpdate(true);
                        logMessage('✅ AppState.triggerLoginUIUpdate executed without error');
                    } else {
                        logMessage('❌ AppState.triggerLoginUIUpdate not available');
                    }
                } catch (error) {
                    logMessage('❌ Error in AppState.triggerLoginUIUpdate: ' + error.message);
                }
                
                logMessage('🎉 Test completed!');
                
            } catch (error) {
                logMessage('❌ Test failed: ' + error.message);
            }
        }
        
        // Run test when page loads
        window.addEventListener('load', testUIService);
    </script>
</body>
</html>
