/**
 * 语言管理器服务 (优化版)
 * 负责语言数据管理、语言选择和多语言支持
 * 保留核心功能，去除过度复杂的缓存机制
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.services = window.OTA.services || {};

(function() {
    'use strict';

    /**
     * 语言管理器类 (优化版)
     * 保留核心语言管理功能，简化实现逻辑
     */
    class LanguageManager {
        constructor() {
            this.languages = this.initializeLanguages();
            this.selectedLanguages = [];
            this.currentLanguage = 'en';
            this.initialized = false;
        }

        /**
         * 初始化语言数据
         */
        initializeLanguages() {
            return {
                2: { id: 2, name: 'English (EN)', code: 'en', enabled: true },
                3: { id: 3, name: 'Malay (MY)', code: 'ms', enabled: true },
                4: { id: 4, name: 'Chinese (CN)', code: 'zh', enabled: true },
                5: { id: 5, name: 'Paging (PG)', code: 'pg', enabled: true },
                6: { id: 6, name: 'Charter (CHARTER)', code: 'charter', enabled: true },
                8: { id: 8, name: '携程司导 (IM)', code: 'im', enabled: true },
                9: { id: 9, name: 'PSV (PSV)', code: 'psv', enabled: true },
                10: { id: 10, name: 'EVP (EVP)', code: 'evp', enabled: true },
                11: { id: 11, name: 'Car Type Reverify (CAR)', code: 'car', enabled: true },
                12: { id: 12, name: 'Jetty (JETTY)', code: 'jetty', enabled: true },
                13: { id: 13, name: 'PhotoSkill Proof (PHOTO)', code: 'photo', enabled: true }
            };
        }

        /**
         * 初始化语言管理器
         */
        init() {
            if (this.initialized) {
                getLogger().log('语言管理器已经初始化', 'warning');
                return;
            }

            this.loadFromAppState();
            this.setDefaultLanguages();
            this.initialized = true;
            getLogger().log('语言管理器初始化完成', 'success');
        }

        /**
         * 从应用状态加载语言数据
         */
        loadFromAppState() {
            try {
                const appState = window.OTA?.appState || window.appState;
                if (appState) {
                    const systemLanguages = appState.get('systemData.languages');
                    if (Array.isArray(systemLanguages) && systemLanguages.length > 0) {
                        this.mergeLanguagesFromSystem(systemLanguages);
                        getLogger().log('从系统数据加载语言完成', 'info');
                    }
                }
            } catch (error) {
                getLogger().log('从应用状态加载语言数据失败', 'warning', { error: error.message });
            }
        }

        /**
         * 合并系统语言数据
         */
        mergeLanguagesFromSystem(systemLanguages) {
            systemLanguages.forEach(sysLang => {
                if (sysLang.id && sysLang.name) {
                    this.languages[sysLang.id] = {
                        id: sysLang.id,
                        name: sysLang.name,
                        code: sysLang.code || this.languages[sysLang.id]?.code || 'unknown',
                        enabled: sysLang.enabled !== false
                    };
                }
            });
        }

        /**
         * 设置默认语言选择
         */
        setDefaultLanguages() {
            this.selectedLanguages = [2, 3, 4]; // English, Malay, Chinese
        }

        /**
         * 获取所有语言 (同步)
         */
        getLanguagesSync(options = {}) {
            const { enabledOnly = true } = options;
            const languagesArray = Object.values(this.languages);
            
            return enabledOnly 
                ? languagesArray.filter(lang => lang.enabled)
                : languagesArray;
        }

        /**
         * 获取所有语言 (异步，兼容原接口)
         */
        async getLanguages(options = {}) {
            return Promise.resolve(this.getLanguagesSync(options));
        }

        /**
         * 获取可用语言列表
         */
        getAvailableLanguages() {
            return this.getLanguagesSync({ enabledOnly: true });
        }

        /**
         * 根据ID获取语言
         */
        getLanguage(id) {
            const languageId = parseInt(id);
            return this.languages[languageId] || null;
        }

        /**
         * 设置选中的语言
         */
        setSelectedLanguages(languageIds) {
            if (!Array.isArray(languageIds)) {
                getLogger().log('设置语言选择失败：参数必须是数组', 'error');
                return;
            }

            const validIds = languageIds
                .map(id => parseInt(id))
                .filter(id => !isNaN(id) && this.languages[id]);

            this.selectedLanguages = validIds;
            getLogger().log('语言选择已更新', 'info', { languages: validIds });
        }

        /**
         * 获取选中的语言
         */
        getSelectedLanguages() {
            return [...this.selectedLanguages];
        }

        /**
         * 获取选中语言的详细信息
         */
        getSelectedLanguageDetails() {
            return this.selectedLanguages.map(id => this.getLanguage(id)).filter(Boolean);
        }

        /**
         * 设置当前语言
         */
        setLanguage(languageCode) {
            this.currentLanguage = languageCode;
            getLogger().log('当前语言已设置', 'info', { language: languageCode });
        }

        /**
         * 获取当前语言
         */
        getCurrentLanguage() {
            return this.currentLanguage;
        }

        /**
         * 获取默认语言列表
         */
        getDefaultLanguages() {
            return [2, 3, 4]; // English, Malay, Chinese
        }

        /**
         * 验证语言ID数组
         */
        validateLanguageIds(languageIds) {
            if (!Array.isArray(languageIds)) {
                return { valid: false, error: '语言ID必须是数组' };
            }

            const invalidIds = languageIds.filter(id => {
                const languageId = parseInt(id);
                return isNaN(languageId) || !this.languages[languageId];
            });

            if (invalidIds.length > 0) {
                return { 
                    valid: false, 
                    error: `无效的语言ID: ${invalidIds.join(', ')}`,
                    invalidIds 
                };
            }

            return { valid: true };
        }

        /**
         * 格式化语言数据供API使用
         */
        formatForAPI(languageIds) {
            if (!Array.isArray(languageIds)) {
                return {};
            }

            const validIds = languageIds
                .map(id => parseInt(id))
                .filter(id => !isNaN(id) && this.languages[id]);

            return {
                languages_id_array: validIds,
                language_names: validIds.map(id => this.getLanguage(id)?.name).filter(Boolean),
                language_codes: validIds.map(id => this.getLanguage(id)?.code).filter(Boolean)
            };
        }

        /**
         * 基于文本内容自动检测需要的语言
         */
        detectLanguagesFromText(text, customerName = '') {
            const detectedLanguages = new Set([2, 3]); // 默认英语和马来语

            // 检测中文
            const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;
            if (chineseRegex.test(text + customerName)) {
                detectedLanguages.add(4); // 添加中文
            }

            // 可以根据需要添加更多语言检测逻辑
            return Array.from(detectedLanguages);
        }

        /**
         * 根据服务类型获取推荐语言
         */
        getLanguagesForServiceType(serviceTypeId) {
            // 根据服务类型推荐语言
            const recommendations = {
                1: [2, 3, 4], // 基础服务：英语、马来语、中文
                2: [2, 3, 4, 8], // 包车服务：添加携程司导
                3: [2, 3], // 简单服务：只需英语和马来语
                5: [5], // 分页服务：只需分页语言
                // 可以根据实际需求扩展
            };

            return recommendations[serviceTypeId] || this.getDefaultLanguages();
        }

        /**
         * 获取语言统计信息
         */
        getLanguageStats() {
            const total = Object.keys(this.languages).length;
            const enabled = this.getLanguagesSync({ enabledOnly: true }).length;
            const selected = this.selectedLanguages.length;

            return {
                total,
                enabled,
                selected,
                currentLanguage: this.currentLanguage,
                availableLanguages: this.getAvailableLanguages().map(lang => ({
                    id: lang.id,
                    name: lang.name,
                    code: lang.code
                }))
            };
        }

        /**
         * 重置语言设置
         */
        reset() {
            this.selectedLanguages = [];
            this.currentLanguage = 'en';
            this.setDefaultLanguages();
            getLogger().log('语言设置已重置', 'info');
        }

        /**
         * 检查语言管理器是否可用
         */
        isAvailable() {
            return this.initialized;
        }

        /**
         * 导出语言数据 (用于调试)
         */
        exportLanguageData() {
            return {
                languages: { ...this.languages },
                selectedLanguages: [...this.selectedLanguages],
                currentLanguage: this.currentLanguage,
                stats: this.getLanguageStats()
            };
        }

        /**
         * 更新语言状态
         */
        updateLanguageStatus(languageId, enabled) {
            const id = parseInt(languageId);
            if (this.languages[id]) {
                this.languages[id].enabled = Boolean(enabled);
                getLogger().log('语言状态已更新', 'info', { id, enabled });
            }
        }
    }

    // 导出到全局命名空间
    window.OTA.services.LanguageManager = LanguageManager;

    // 创建全局实例并注册到依赖容器
    if (window.OTA && window.OTA.container) {
        try {
            window.OTA.container.register('languageManager', () => {
                const manager = new LanguageManager();
                manager.init();
                return manager;
            }, { singleton: true });
            getLogger().log('LanguageManager已注册到依赖容器', 'info');
        } catch (error) {
            console.warn('LanguageManager注册到依赖容器失败:', error.message);
        }
    }

    // 向后兼容性暴露
    if (!window.languageManager && !window.OTA.languageManager) {
        const globalInstance = new LanguageManager();
        globalInstance.init();
        window.OTA.languageManager = globalInstance;
        window.languageManager = globalInstance;
    }

})();