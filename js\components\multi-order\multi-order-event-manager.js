/**
 * 文件: multi-order-event-manager.js
 * 路径: js\components\multi-order\multi-order-event-manager.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.getLogger
 * - document (DOM API)
 * - console (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.getMultiOrderEventManager
 * - window.MultiOrderEventManager
 * - window.OTA
 * - window.OTA
 * - window.OTA.multiOrder
 * - window.OTA.multiOrder
 * - window.OTA.multiOrder.EventManager
 * - window.OTA.multiOrder.getEventManager
 * 
 * === 类声明 ===
 * - class MultiOrderEventManager
 * 
 * === 函数声明 ===
 * - function createMultiOrderEventManager()
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.613Z
 */

/**
 * 🎯 多订单事件管理器
 * 负责处理多订单系统的事件绑定、事件处理和事件委托
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-20
 */

(function() {
    'use strict';

    /**
     * 多订单事件管理器类
     * 专门处理事件绑定、事件处理、键盘快捷键、拖拽事件等功能
     */
    class MultiOrderEventManager {
        /**
         * 构造函数
         * @param {Object} dependencies - 依赖注入对象
         * @param {Object} dependencies.logger - 日志服务
         * @param {Object} dependencies.config - 配置对象
         * @param {Object} dependencies.stateManager - 状态管理器
         * @param {Object} dependencies.uiManager - UI管理器
         * @param {Object} dependencies.quickEditManager - 快捷编辑管理器
         */
        constructor(dependencies = {}) {
            this.logger = dependencies.logger || this.getLogger();
            this.config = {
                // 事件管理配置
                debounceDelay: 300,
                dragThreshold: 5,
                keyboardShortcuts: {
                    'Escape': 'exitQuickEdit',
                    'Enter': 'confirmAction',
                    'Delete': 'deleteSelected',
                    'Ctrl+A': 'selectAll',
                    'Ctrl+D': 'deselectAll'
                },
                touchEvents: {
                    enabled: true,
                    swipeThreshold: 50,
                    tapDelay: 300
                },
                ...dependencies.config
            };

            // 依赖注入
            this.stateManager = dependencies.stateManager;
            this.uiManager = dependencies.uiManager;
            this.quickEditManager = dependencies.quickEditManager;

            // 事件管理状态
            this.boundEventHandlers = new Map();
            this.debounceTimers = new Map();
            this.activeEvents = new Set();
            this.eventHistory = [];

            // 拖拽状态
            this.dragState = {
                isDragging: false,
                startX: 0,
                startY: 0,
                currentX: 0,
                currentY: 0,
                target: null
            };

            // 触摸状态
            this.touchState = {
                startTime: 0,
                startX: 0,
                startY: 0,
                lastTap: 0
            };

            // 初始化
            this.init();
        }

        /**
         * 初始化事件管理器
         */
        init() {
            this.logger?.log('🎯 事件管理器初始化开始', 'info');
            
            try {
                // 绑定全局事件处理器
                this.bindGlobalEventHandlers();
                
                // 绑定键盘快捷键
                this.bindKeyboardShortcuts();
                
                // 绑定触摸事件（如果启用）
                if (this.config.touchEvents.enabled) {
                    this.bindTouchEvents();
                }
                
                this.logger?.log('✅ 事件管理器初始化完成', 'success');
            } catch (error) {
                this.logger?.logError('事件管理器初始化失败', error);
                throw error;
            }
        }

        /**
         * 绑定全局事件处理器
         */
        bindGlobalEventHandlers() {
            // 文档级别事件
            this.addEventHandler(document, 'click', this.handleDocumentClick.bind(this));
            this.addEventHandler(document, 'keydown', this.handleKeyDown.bind(this));
            this.addEventHandler(document, 'keyup', this.handleKeyUp.bind(this));
            
            // 窗口级别事件
            this.addEventHandler(window, 'resize', this.debounce(this.handleWindowResize.bind(this), this.config.debounceDelay));
            this.addEventHandler(window, 'beforeunload', this.handleBeforeUnload.bind(this));
            
            // 多订单面板事件
            this.bindPanelEvents();
        }

        /**
         * 绑定多订单面板事件
         */
        bindPanelEvents() {
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (!multiOrderPanel) return;

            // 面板内部事件委托
            this.addEventHandler(multiOrderPanel, 'click', this.handlePanelClick.bind(this));
            this.addEventHandler(multiOrderPanel, 'change', this.handlePanelChange.bind(this));
            this.addEventHandler(multiOrderPanel, 'input', this.handlePanelInput.bind(this));
            this.addEventHandler(multiOrderPanel, 'blur', this.handlePanelBlur.bind(this), true);
            this.addEventHandler(multiOrderPanel, 'focus', this.handlePanelFocus.bind(this), true);

            // 拖拽事件
            this.addEventHandler(multiOrderPanel, 'mousedown', this.handleMouseDown.bind(this));
            this.addEventHandler(document, 'mousemove', this.handleMouseMove.bind(this));
            this.addEventHandler(document, 'mouseup', this.handleMouseUp.bind(this));
        }

        /**
         * 绑定键盘快捷键
         */
        bindKeyboardShortcuts() {
            Object.entries(this.config.keyboardShortcuts).forEach(([key, action]) => {
                this.registerKeyboardShortcut(key, action);
            });
        }

        /**
         * 注册键盘快捷键
         * @param {string} key - 键盘组合键
         * @param {string} action - 动作名称
         */
        registerKeyboardShortcut(key, action) {
            const handler = (event) => {
                if (this.matchesKeyboardShortcut(event, key)) {
                    event.preventDefault();
                    this.executeKeyboardAction(action, event);
                }
            };

            this.addEventHandler(document, 'keydown', handler);
            this.logger?.log(`键盘快捷键已注册: ${key} -> ${action}`, 'info');
        }

        /**
         * 匹配键盘快捷键
         * @param {KeyboardEvent} event - 键盘事件
         * @param {string} shortcut - 快捷键组合
         * @returns {boolean} 是否匹配
         */
        matchesKeyboardShortcut(event, shortcut) {
            const parts = shortcut.split('+');
            const key = parts.pop();
            
            const modifiers = {
                'Ctrl': event.ctrlKey,
                'Alt': event.altKey,
                'Shift': event.shiftKey,
                'Meta': event.metaKey
            };

            // 检查修饰键
            for (const part of parts) {
                if (!modifiers[part]) return false;
            }

            // 检查主键
            return event.key === key || event.code === key;
        }

        /**
         * 执行键盘动作
         * @param {string} action - 动作名称
         * @param {KeyboardEvent} event - 键盘事件
         */
        executeKeyboardAction(action, event) {
            switch (action) {
                case 'exitQuickEdit':
                    this.handleExitQuickEdit(event);
                    break;
                case 'confirmAction':
                    this.handleConfirmAction(event);
                    break;
                case 'deleteSelected':
                    this.handleDeleteSelected(event);
                    break;
                case 'selectAll':
                    this.handleSelectAll(event);
                    break;
                case 'deselectAll':
                    this.handleDeselectAll(event);
                    break;
                default:
                    this.logger?.log(`未知的键盘动作: ${action}`, 'warn');
            }
        }

        /**
         * 绑定触摸事件
         */
        bindTouchEvents() {
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (!multiOrderPanel) return;

            this.addEventHandler(multiOrderPanel, 'touchstart', this.handleTouchStart.bind(this));
            this.addEventHandler(multiOrderPanel, 'touchmove', this.handleTouchMove.bind(this));
            this.addEventHandler(multiOrderPanel, 'touchend', this.handleTouchEnd.bind(this));
        }

        /**
         * 添加事件处理器
         * @param {Element} element - 目标元素
         * @param {string} event - 事件名称
         * @param {Function} handler - 事件处理函数
         * @param {boolean} useCapture - 是否使用捕获阶段
         */
        addEventHandler(element, event, handler, useCapture = false) {
            if (!element || !event || !handler) return;

            const key = `${element.constructor.name}-${event}-${handler.name}`;
            
            // 避免重复绑定
            if (this.boundEventHandlers.has(key)) {
                return;
            }

            element.addEventListener(event, handler, useCapture);
            this.boundEventHandlers.set(key, { element, event, handler, useCapture });
            this.activeEvents.add(event);

            this.logger?.log(`事件处理器已绑定: ${event}`, 'info');
        }

        /**
         * 移除事件处理器
         * @param {string} key - 事件处理器键
         */
        removeEventHandler(key) {
            const handlerInfo = this.boundEventHandlers.get(key);
            if (handlerInfo) {
                const { element, event, handler, useCapture } = handlerInfo;
                element.removeEventListener(event, handler, useCapture);
                this.boundEventHandlers.delete(key);
                this.logger?.log(`事件处理器已移除: ${event}`, 'info');
            }
        }

        /**
         * 防抖函数
         * @param {Function} func - 要防抖的函数
         * @param {number} delay - 延迟时间
         * @returns {Function} 防抖后的函数
         */
        debounce(func, delay) {
            return (...args) => {
                const key = func.name || 'anonymous';
                
                if (this.debounceTimers.has(key)) {
                    clearTimeout(this.debounceTimers.get(key));
                }
                
                const timer = setTimeout(() => {
                    func.apply(this, args);
                    this.debounceTimers.delete(key);
                }, delay);
                
                this.debounceTimers.set(key, timer);
            };
        }

        /**
         * 处理文档点击事件
         * @param {MouseEvent} event - 鼠标事件
         */
        handleDocumentClick(event) {
            // 记录事件
            this.recordEvent('documentClick', event);

            // 处理面板外点击
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (multiOrderPanel && !multiOrderPanel.contains(event.target)) {
                this.handleClickOutsidePanel(event);
            }

            // 处理订单选择
            if (event.target.classList.contains('order-checkbox')) {
                this.handleOrderCheckboxClick(event);
            }

            // 处理快捷编辑按钮
            if (event.target.classList.contains('quick-edit-btn')) {
                this.handleQuickEditButtonClick(event);
            }
        }

        /**
         * 处理面板点击事件
         * @param {MouseEvent} event - 鼠标事件
         */
        handlePanelClick(event) {
            this.recordEvent('panelClick', event);

            // 阻止事件冒泡到文档级别
            event.stopPropagation();

            // 处理特定元素点击
            const target = event.target;

            if (target.classList.contains('order-item')) {
                this.handleOrderItemClick(event);
            } else if (target.classList.contains('batch-action-btn')) {
                this.handleBatchActionClick(event);
            } else if (target.classList.contains('close-panel-btn')) {
                this.handleClosePanelClick(event);
            }
        }

        /**
         * 处理面板变更事件
         * @param {Event} event - 变更事件
         */
        handlePanelChange(event) {
            this.recordEvent('panelChange', event);

            const target = event.target;

            if (target.classList.contains('order-checkbox')) {
                this.handleOrderCheckboxChange(event);
            } else if (target.classList.contains('batch-select')) {
                this.handleBatchSelectChange(event);
            }
        }

        /**
         * 处理面板输入事件
         * @param {InputEvent} event - 输入事件
         */
        handlePanelInput(event) {
            this.recordEvent('panelInput', event);

            // 防抖处理输入事件
            this.debounce(() => {
                this.handleDebouncedInput(event);
            }, this.config.debounceDelay)();
        }

        /**
         * 处理键盘按下事件
         * @param {KeyboardEvent} event - 键盘事件
         */
        handleKeyDown(event) {
            this.recordEvent('keyDown', event);

            // 处理快捷键（已在绑定时处理）
            // 这里处理其他键盘事件
            if (event.key === 'Tab') {
                this.handleTabNavigation(event);
            }
        }

        /**
         * 处理键盘释放事件
         * @param {KeyboardEvent} event - 键盘事件
         */
        handleKeyUp(event) {
            this.recordEvent('keyUp', event);
        }

        /**
         * 处理窗口大小变化事件
         * @param {Event} event - 窗口事件
         */
        handleWindowResize(event) {
            this.recordEvent('windowResize', event);

            // 通知UI管理器调整布局
            if (this.uiManager && this.uiManager.handleResize) {
                this.uiManager.handleResize();
            }
        }

        /**
         * 处理页面卸载前事件
         * @param {BeforeUnloadEvent} event - 卸载事件
         */
        handleBeforeUnload(event) {
            this.recordEvent('beforeUnload', event);

            // 检查是否有未保存的更改
            if (this.stateManager && this.stateManager.getStateValue('batchProgress.isRunning')) {
                event.preventDefault();
                event.returnValue = '批量处理正在进行中，确定要离开吗？';
                return event.returnValue;
            }
        }

        /**
         * 处理鼠标按下事件（拖拽开始）
         * @param {MouseEvent} event - 鼠标事件
         */
        handleMouseDown(event) {
            if (event.target.classList.contains('draggable')) {
                this.dragState.isDragging = true;
                this.dragState.startX = event.clientX;
                this.dragState.startY = event.clientY;
                this.dragState.target = event.target;

                event.preventDefault();
                this.recordEvent('dragStart', event);
            }
        }

        /**
         * 处理鼠标移动事件（拖拽中）
         * @param {MouseEvent} event - 鼠标事件
         */
        handleMouseMove(event) {
            if (this.dragState.isDragging) {
                this.dragState.currentX = event.clientX;
                this.dragState.currentY = event.clientY;

                const deltaX = this.dragState.currentX - this.dragState.startX;
                const deltaY = this.dragState.currentY - this.dragState.startY;

                // 检查是否超过拖拽阈值
                if (Math.abs(deltaX) > this.config.dragThreshold || Math.abs(deltaY) > this.config.dragThreshold) {
                    this.handleDragMove(event, deltaX, deltaY);
                }
            }
        }

        /**
         * 处理鼠标释放事件（拖拽结束）
         * @param {MouseEvent} event - 鼠标事件
         */
        handleMouseUp(event) {
            if (this.dragState.isDragging) {
                this.handleDragEnd(event);
                this.resetDragState();
                this.recordEvent('dragEnd', event);
            }
        }

        /**
         * 处理触摸开始事件
         * @param {TouchEvent} event - 触摸事件
         */
        handleTouchStart(event) {
            const touch = event.touches[0];
            this.touchState.startTime = Date.now();
            this.touchState.startX = touch.clientX;
            this.touchState.startY = touch.clientY;

            this.recordEvent('touchStart', event);
        }

        /**
         * 处理触摸移动事件
         * @param {TouchEvent} event - 触摸事件
         */
        handleTouchMove(event) {
            this.recordEvent('touchMove', event);

            // 处理滑动手势
            const touch = event.touches[0];
            const deltaX = touch.clientX - this.touchState.startX;
            const deltaY = touch.clientY - this.touchState.startY;

            if (Math.abs(deltaX) > this.config.touchEvents.swipeThreshold) {
                this.handleSwipeGesture(deltaX > 0 ? 'right' : 'left', event);
            }
        }

        /**
         * 处理触摸结束事件
         * @param {TouchEvent} event - 触摸事件
         */
        handleTouchEnd(event) {
            const duration = Date.now() - this.touchState.startTime;

            // 检测双击
            if (duration < this.config.touchEvents.tapDelay) {
                const now = Date.now();
                if (now - this.touchState.lastTap < this.config.touchEvents.tapDelay) {
                    this.handleDoubleTap(event);
                }
                this.touchState.lastTap = now;
            }

            this.recordEvent('touchEnd', event);
        }

        /**
         * 记录事件到历史记录
         * @param {string} eventType - 事件类型
         * @param {Event} event - 事件对象
         */
        recordEvent(eventType, event) {
            this.eventHistory.push({
                type: eventType,
                timestamp: Date.now(),
                target: event.target?.tagName || 'unknown',
                detail: {
                    clientX: event.clientX,
                    clientY: event.clientY,
                    key: event.key,
                    button: event.button
                }
            });

            // 限制历史记录大小
            if (this.eventHistory.length > 100) {
                this.eventHistory = this.eventHistory.slice(-50);
            }
        }

        /**
         * 重置拖拽状态
         */
        resetDragState() {
            this.dragState = {
                isDragging: false,
                startX: 0,
                startY: 0,
                currentX: 0,
                currentY: 0,
                target: null
            };
        }

        /**
         * 清理所有事件处理器
         */
        cleanup() {
            // 移除所有绑定的事件处理器
            this.boundEventHandlers.forEach((handlerInfo, key) => {
                this.removeEventHandler(key);
            });

            // 清理防抖定时器
            this.debounceTimers.forEach(timer => clearTimeout(timer));
            this.debounceTimers.clear();

            // 清理状态
            this.activeEvents.clear();
            this.eventHistory = [];
            this.resetDragState();

            this.logger?.log('🎯 事件管理器已清理', 'info');
        }

        /**
         * 销毁事件管理器
         */
        destroy() {
            this.cleanup();
            this.logger?.log('🎯 事件管理器已销毁', 'info');
        }

        /**
         * 处理面板失焦事件
         * @param {FocusEvent} event - 失焦事件
         */
        handlePanelBlur(event) {
            this.logger?.log('🔍 面板失焦', 'debug', { 
                target: event.target?.tagName,
                id: event.target?.id 
            });
            
            // 保存当前编辑状态
            if (event.target.matches('input, textarea, select')) {
                this.saveFieldState(event.target);
            }
            
            // 更新焦点状态
            if (this.stateManager && typeof this.stateManager.updateFocusState === 'function') {
                this.stateManager.updateFocusState(event.target, false);
            }
        }

        /**
         * 处理面板获焦事件
         * @param {FocusEvent} event - 获焦事件
         */
        handlePanelFocus(event) {
            this.logger?.log('🎯 面板获焦', 'debug', { 
                target: event.target?.tagName,
                id: event.target?.id 
            });
            
            // 恢复编辑状态
            if (event.target.matches('input, textarea, select')) {
                this.restoreFieldState(event.target);
            }
            
            // 更新焦点状态
            if (this.stateManager && typeof this.stateManager.updateFocusState === 'function') {
                this.stateManager.updateFocusState(event.target, true);
            }
            
            // 自动选择文本（对于文本输入框）
            if (event.target.matches('input[type="text"], input[type="number"], textarea')) {
                setTimeout(() => {
                    event.target.select?.();
                }, 10);
            }
        }

        /**
         * 保存字段状态
         * @param {HTMLElement} field - 表单字段
         */
        saveFieldState(field) {
            if (field && field.id) {
                const state = {
                    value: field.value,
                    selectionStart: field.selectionStart,
                    selectionEnd: field.selectionEnd,
                    timestamp: Date.now()
                };
                
                // 存储到会话存储
                try {
                    sessionStorage.setItem(`field_state_${field.id}`, JSON.stringify(state));
                } catch (error) {
                    this.logger?.log('字段状态保存失败', 'warning', error);
                }
            }
        }

        /**
         * 恢复字段状态
         * @param {HTMLElement} field - 表单字段
         */
        restoreFieldState(field) {
            if (field && field.id) {
                try {
                    const stateStr = sessionStorage.getItem(`field_state_${field.id}`);
                    if (stateStr) {
                        const state = JSON.parse(stateStr);
                        
                        // 5分钟内的状态才恢复
                        if (Date.now() - state.timestamp < 5 * 60 * 1000) {
                            if (field.value !== state.value) {
                                field.value = state.value;
                            }
                            
                            // 恢复光标位置
                            if (typeof field.setSelectionRange === 'function') {
                                field.setSelectionRange(state.selectionStart, state.selectionEnd);
                            }
                        }
                    }
                } catch (error) {
                    this.logger?.log('字段状态恢复失败', 'warning', error);
                }
            }
        }

        /**
         * 获取事件统计信息
         * @returns {Object} 事件统计
         */
        getEventStats() {
            return {
                boundHandlers: this.boundEventHandlers.size,
                activeEvents: this.activeEvents.size,
                eventHistory: this.eventHistory.length,
                debounceTimers: this.debounceTimers.size,
                isDragging: this.dragState.isDragging
            };
        }

        /**
         * 处理面板外点击事件
         * @param {MouseEvent} event - 鼠标事件
         */
        handleClickOutsidePanel(event) {
            try {
                this.recordEvent('clickOutsidePanel', event);
                
                // 检查是否有未保存的更改
                if (this.stateManager && this.stateManager.getStateValue('editSession.hasUnsavedChanges')) {
                    const confirmClose = confirm('有未保存的更改，确定要关闭面板吗？');
                    if (!confirmClose) {
                        return;
                    }
                }
                
                // 隐藏多订单面板
                if (this.uiManager && typeof this.uiManager.hideMultiOrderPanel === 'function') {
                    this.uiManager.hideMultiOrderPanel();
                }
                
                // 清理编辑状态
                if (this.stateManager && typeof this.stateManager.clearEditSession === 'function') {
                    this.stateManager.clearEditSession();
                }
                
                this.logger?.log('📱 面板已通过外部点击关闭', 'info');
                
            } catch (error) {
                this.logger?.logError('处理面板外点击事件失败', error);
            }
        }

        /**
         * 处理订单复选框点击事件
         * @param {MouseEvent} event - 鼠标事件
         */
        handleOrderCheckboxClick(event) {
            try {
                this.recordEvent('orderCheckboxClick', event);
                
                const checkbox = event.target;
                const orderId = checkbox.dataset.orderId || checkbox.value;
                const isChecked = checkbox.checked;
                
                if (!orderId) {
                    this.logger?.logError('订单复选框缺少orderId数据');
                    return;
                }
                
                // 更新选择状态
                if (this.stateManager && typeof this.stateManager.updateOrderSelection === 'function') {
                    this.stateManager.updateOrderSelection(orderId, isChecked);
                }
                
                // 更新UI状态
                if (this.uiManager && typeof this.uiManager.updateSelectionUI === 'function') {
                    this.uiManager.updateSelectionUI();
                }
                
                this.logger?.log(`📋 订单${orderId}选择状态: ${isChecked}`, 'info');
                
            } catch (error) {
                this.logger?.logError('处理订单复选框点击失败', error);
            }
        }

        /**
         * 处理快捷编辑按钮点击事件
         * @param {MouseEvent} event - 鼠标事件
         */
        handleQuickEditButtonClick(event) {
            try {
                this.recordEvent('quickEditButtonClick', event);
                
                const button = event.target;
                const orderId = button.dataset.orderId;
                const editType = button.dataset.editType || 'general';
                
                if (!orderId) {
                    this.logger?.logError('快捷编辑按钮缺少orderId数据');
                    return;
                }
                
                // 启动快捷编辑模式
                if (this.quickEditManager && typeof this.quickEditManager.startQuickEdit === 'function') {
                    this.quickEditManager.startQuickEdit(orderId, editType);
                } else if (this.uiManager && typeof this.uiManager.startQuickEdit === 'function') {
                    this.uiManager.startQuickEdit(orderId, editType);
                }
                
                // 更新状态
                if (this.stateManager && typeof this.stateManager.setActiveEdit === 'function') {
                    this.stateManager.setActiveEdit(orderId, editType);
                }
                
                this.logger?.log(`✏️ 开始快捷编辑订单${orderId} (${editType})`, 'info');
                
            } catch (error) {
                this.logger?.logError('处理快捷编辑按钮点击失败', error);
            }
        }

        /**
         * 处理关闭面板按钮点击事件
         * @param {MouseEvent} event - 鼠标事件
         */
        handleClosePanelClick(event) {
            try {
                this.recordEvent('closePanelClick', event);
                
                // 检查是否有未保存的更改
                if (this.stateManager && this.stateManager.getStateValue('editSession.hasUnsavedChanges')) {
                    const confirmClose = confirm('有未保存的更改，确定要关闭面板吗？');
                    if (!confirmClose) {
                        return;
                    }
                }
                
                // 隐藏面板
                if (this.uiManager && typeof this.uiManager.hideMultiOrderPanel === 'function') {
                    this.uiManager.hideMultiOrderPanel();
                }
                
                // 清理状态
                if (this.stateManager && typeof this.stateManager.clearEditSession === 'function') {
                    this.stateManager.clearEditSession();
                }
                
                this.logger?.log('❌ 面板已通过关闭按钮关闭', 'info');
                
            } catch (error) {
                this.logger?.logError('处理关闭面板按钮点击失败', error);
            }
        }

        /**
         * 处理订单项点击事件
         * @param {MouseEvent} event - 鼠标事件
         */
        handleOrderItemClick(event) {
            try {
                this.recordEvent('orderItemClick', event);
                
                const orderItem = event.target.closest('.order-item');
                if (!orderItem) return;
                
                const orderId = orderItem.dataset.orderId;
                if (!orderId) {
                    this.logger?.logError('订单项缺少orderId数据');
                    return;
                }
                
                // 高亮选中的订单项
                document.querySelectorAll('.order-item').forEach(item => {
                    item.classList.remove('active');
                });
                orderItem.classList.add('active');
                
                // 更新活动订单状态
                if (this.stateManager && typeof this.stateManager.setActiveOrder === 'function') {
                    this.stateManager.setActiveOrder(orderId);
                }
                
                // 显示订单详情
                if (this.uiManager && typeof this.uiManager.showOrderDetails === 'function') {
                    this.uiManager.showOrderDetails(orderId);
                }
                
                this.logger?.log(`📄 选中订单${orderId}`, 'info');
                
            } catch (error) {
                this.logger?.logError('处理订单项点击失败', error);
            }
        }

        /**
         * 处理批量操作按钮点击事件
         * @param {MouseEvent} event - 鼠标事件
         */
        handleBatchActionClick(event) {
            try {
                this.recordEvent('batchActionClick', event);
                
                const button = event.target;
                const action = button.dataset.action;
                
                if (!action) {
                    this.logger?.logError('批量操作按钮缺少action数据');
                    return;
                }
                
                // 获取选中的订单
                const selectedOrders = this.stateManager ? 
                    this.stateManager.getStateValue('selectedOrders') || [] : [];
                
                if (selectedOrders.length === 0) {
                    alert('请先选择要操作的订单');
                    return;
                }
                
                // 确认操作
                const confirmMessage = `确定要对${selectedOrders.length}个订单执行"${action}"操作吗？`;
                if (!confirm(confirmMessage)) {
                    return;
                }
                
                // 执行批量操作
                if (this.uiManager && typeof this.uiManager.executeBatchAction === 'function') {
                    this.uiManager.executeBatchAction(action, selectedOrders);
                }
                
                this.logger?.log(`🔄 执行批量操作: ${action} (${selectedOrders.length}个订单)`, 'info');
                
            } catch (error) {
                this.logger?.logError('处理批量操作按钮点击失败', error);
            }
        }

        /**
         * 处理订单复选框状态变化事件
         * @param {ChangeEvent} event - 变化事件
         */
        handleOrderCheckboxChange(event) {
            try {
                this.recordEvent('orderCheckboxChange', event);
                
                const checkbox = event.target;
                const orderId = checkbox.dataset.orderId || checkbox.value;
                const isChecked = checkbox.checked;
                
                // 更新选择状态（与点击事件类似，但专注于状态变化）
                if (this.stateManager && typeof this.stateManager.updateOrderSelection === 'function') {
                    this.stateManager.updateOrderSelection(orderId, isChecked);
                }
                
                // 触发批量操作UI更新
                if (this.uiManager && typeof this.uiManager.updateBatchActionUI === 'function') {
                    const selectedCount = this.stateManager ? 
                        (this.stateManager.getStateValue('selectedOrders') || []).length : 0;
                    this.uiManager.updateBatchActionUI(selectedCount);
                }
                
                this.logger?.log(`🔄 订单${orderId}选择状态变化: ${isChecked}`, 'debug');
                
            } catch (error) {
                this.logger?.logError('处理订单复选框状态变化失败', error);
            }
        }

        /**
         * 处理批量选择状态变化事件
         * @param {ChangeEvent} event - 变化事件
         */
        handleBatchSelectChange(event) {
            try {
                this.recordEvent('batchSelectChange', event);
                
                const selectElement = event.target;
                const action = selectElement.value;
                
                if (!action) return;
                
                // 执行批量选择操作
                switch (action) {
                    case 'all':
                        this.selectAllOrders();
                        break;
                    case 'none':
                        this.deselectAllOrders();
                        break;
                    case 'invert':
                        this.invertOrderSelection();
                        break;
                    default:
                        this.logger?.logError(`未知的批量选择操作: ${action}`);
                }
                
                // 重置选择框
                setTimeout(() => {
                    selectElement.value = '';
                }, 100);
                
                this.logger?.log(`📋 执行批量选择操作: ${action}`, 'info');
                
            } catch (error) {
                this.logger?.logError('处理批量选择状态变化失败', error);
            }
        }

        /**
         * 处理防抖输入事件
         * @param {InputEvent} event - 输入事件
         */
        handleDebouncedInput(event) {
            try {
                this.recordEvent('debouncedInput', event);
                
                const input = event.target;
                const value = input.value;
                const inputType = input.dataset.inputType || 'search';
                
                // 清除之前的防抖计时器
                if (this.debounceTimer) {
                    clearTimeout(this.debounceTimer);
                }
                
                // 设置新的防抖计时器
                this.debounceTimer = setTimeout(() => {
                    this.processInput(inputType, value, input);
                }, this.config.debounceDelay);
                
                this.logger?.log(`⌨️ 防抖输入: ${inputType} = "${value}"`, 'debug');
                
            } catch (error) {
                this.logger?.logError('处理防抖输入事件失败', error);
            }
        }

        /**
         * 处理标签页导航事件
         * @param {MouseEvent} event - 鼠标事件
         */
        handleTabNavigation(event) {
            try {
                this.recordEvent('tabNavigation', event);
                
                const tab = event.target.closest('.tab-item');
                if (!tab) return;
                
                const targetTab = tab.dataset.tab;
                if (!targetTab) {
                    this.logger?.logError('标签页缺少tab数据');
                    return;
                }
                
                // 更新活动标签页
                document.querySelectorAll('.tab-item').forEach(t => {
                    t.classList.remove('active');
                });
                tab.classList.add('active');
                
                // 显示对应内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.style.display = content.dataset.tab === targetTab ? 'block' : 'none';
                });
                
                // 更新状态
                if (this.stateManager && typeof this.stateManager.setActiveTab === 'function') {
                    this.stateManager.setActiveTab(targetTab);
                }
                
                this.logger?.log(`📑 切换到标签页: ${targetTab}`, 'info');
                
            } catch (error) {
                this.logger?.logError('处理标签页导航失败', error);
            }
        }

        /**
         * 选择所有订单
         */
        selectAllOrders() {
            try {
                const checkboxes = document.querySelectorAll('.order-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = true;
                    const orderId = checkbox.dataset.orderId || checkbox.value;
                    if (orderId && this.stateManager && typeof this.stateManager.updateOrderSelection === 'function') {
                        this.stateManager.updateOrderSelection(orderId, true);
                    }
                });
                
                // 更新UI
                if (this.uiManager && typeof this.uiManager.updateSelectionUI === 'function') {
                    this.uiManager.updateSelectionUI();
                }
                
                this.logger?.log('✅ 已选择所有订单', 'info');
                
            } catch (error) {
                this.logger?.logError('选择所有订单失败', error);
            }
        }

        /**
         * 取消选择所有订单
         */
        deselectAllOrders() {
            try {
                const checkboxes = document.querySelectorAll('.order-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                    const orderId = checkbox.dataset.orderId || checkbox.value;
                    if (orderId && this.stateManager && typeof this.stateManager.updateOrderSelection === 'function') {
                        this.stateManager.updateOrderSelection(orderId, false);
                    }
                });
                
                // 更新UI
                if (this.uiManager && typeof this.uiManager.updateSelectionUI === 'function') {
                    this.uiManager.updateSelectionUI();
                }
                
                this.logger?.log('❌ 已取消选择所有订单', 'info');
                
            } catch (error) {
                this.logger?.logError('取消选择所有订单失败', error);
            }
        }

        /**
         * 反转订单选择
         */
        invertOrderSelection() {
            try {
                const checkboxes = document.querySelectorAll('.order-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = !checkbox.checked;
                    const orderId = checkbox.dataset.orderId || checkbox.value;
                    if (orderId && this.stateManager && typeof this.stateManager.updateOrderSelection === 'function') {
                        this.stateManager.updateOrderSelection(orderId, checkbox.checked);
                    }
                });
                
                // 更新UI
                if (this.uiManager && typeof this.uiManager.updateSelectionUI === 'function') {
                    this.uiManager.updateSelectionUI();
                }
                
                this.logger?.log('🔄 已反转订单选择', 'info');
                
            } catch (error) {
                this.logger?.logError('反转订单选择失败', error);
            }
        }

        /**
         * 处理输入内容
         * @param {string} inputType - 输入类型
         * @param {string} value - 输入值
         * @param {HTMLElement} input - 输入元素
         */
        processInput(inputType, value, input) {
            try {
                switch (inputType) {
                    case 'search':
                        this.performSearch(value);
                        break;
                    case 'filter':
                        this.performFilter(value, input.dataset.filterField);
                        break;
                    case 'edit':
                        this.performInlineEdit(value, input.dataset.orderId, input.dataset.field);
                        break;
                    default:
                        this.logger?.log(`未处理的输入类型: ${inputType}`, 'warning');
                }
            } catch (error) {
                this.logger?.logError('处理输入内容失败', error);
            }
        }

        /**
         * 执行搜索
         * @param {string} searchTerm - 搜索词
         */
        performSearch(searchTerm) {
            if (this.uiManager && typeof this.uiManager.performSearch === 'function') {
                this.uiManager.performSearch(searchTerm);
            }
            this.logger?.log(`🔍 执行搜索: "${searchTerm}"`, 'info');
        }

        /**
         * 执行过滤
         * @param {string} filterValue - 过滤值
         * @param {string} filterField - 过滤字段
         */
        performFilter(filterValue, filterField) {
            if (this.uiManager && typeof this.uiManager.performFilter === 'function') {
                this.uiManager.performFilter(filterField, filterValue);
            }
            this.logger?.log(`🔽 执行过滤: ${filterField} = "${filterValue}"`, 'info');
        }

        /**
         * 执行内联编辑
         * @param {string} value - 编辑值
         * @param {string} orderId - 订单ID
         * @param {string} field - 字段名
         */
        performInlineEdit(value, orderId, field) {
            if (this.uiManager && typeof this.uiManager.performInlineEdit === 'function') {
                this.uiManager.performInlineEdit(orderId, field, value);
            }
            this.logger?.log(`✏️ 内联编辑: 订单${orderId}.${field} = "${value}"`, 'info');
        }

        /**
         * 处理退出快捷编辑（ESC键）
         */
        handleExitQuickEdit() {
            try {
                this.recordEvent('exitQuickEdit', { key: 'Escape' });
                
                // 退出快捷编辑模式
                if (this.quickEditManager && typeof this.quickEditManager.exitQuickEdit === 'function') {
                    this.quickEditManager.exitQuickEdit();
                } else if (this.uiManager && typeof this.uiManager.exitQuickEdit === 'function') {
                    this.uiManager.exitQuickEdit();
                }
                
                // 清理编辑状态
                if (this.stateManager && typeof this.stateManager.clearActiveEdit === 'function') {
                    this.stateManager.clearActiveEdit();
                }
                
                // 移除编辑相关的CSS类
                document.querySelectorAll('.quick-editing').forEach(element => {
                    element.classList.remove('quick-editing');
                });
                
                this.logger?.log('⎋ 已退出快捷编辑模式', 'info');
                
            } catch (error) {
                this.logger?.logError('处理退出快捷编辑失败', error);
            }
        }

        /**
         * 处理确认操作（Enter键）
         */
        handleConfirmAction() {
            try {
                this.recordEvent('confirmAction', { key: 'Enter' });
                
                // 获取当前活动的编辑元素
                const activeEditElement = document.querySelector('.quick-editing input:focus, .quick-editing textarea:focus');
                
                if (activeEditElement) {
                    // 确认编辑
                    this.confirmEdit(activeEditElement);
                } else {
                    // 执行默认确认操作
                    const activeDialog = document.querySelector('.modal.active, .dialog.active');
                    if (activeDialog) {
                        const confirmButton = activeDialog.querySelector('.btn-confirm, .btn-primary');
                        if (confirmButton && !confirmButton.disabled) {
                            confirmButton.click();
                        }
                    }
                }
                
                this.logger?.log('✅ 已执行确认操作', 'info');
                
            } catch (error) {
                this.logger?.logError('处理确认操作失败', error);
            }
        }

        /**
         * 处理删除选中项（Delete键）
         */
        handleDeleteSelected() {
            try {
                this.recordEvent('deleteSelected', { key: 'Delete' });
                
                // 获取选中的订单
                const selectedOrders = this.stateManager ? 
                    this.stateManager.getStateValue('selectedOrders') || [] : [];
                
                if (selectedOrders.length === 0) {
                    this.logger?.log('没有选中的订单可删除', 'warning');
                    return;
                }
                
                // 确认删除
                const confirmMessage = `确定要删除${selectedOrders.length}个选中的订单吗？此操作不可撤销。`;
                if (!confirm(confirmMessage)) {
                    return;
                }
                
                // 执行删除操作
                if (this.uiManager && typeof this.uiManager.deleteSelectedOrders === 'function') {
                    this.uiManager.deleteSelectedOrders(selectedOrders);
                }
                
                this.logger?.log(`🗑️ 已删除${selectedOrders.length}个订单`, 'info');
                
            } catch (error) {
                this.logger?.logError('处理删除选中项失败', error);
            }
        }

        /**
         * 处理全选（Ctrl+A）
         */
        handleSelectAll() {
            try {
                this.recordEvent('selectAll', { key: 'Ctrl+A' });
                
                // 防止浏览器默认的全选行为
                event?.preventDefault();
                
                this.selectAllOrders();
                
                this.logger?.log('🔘 已通过快捷键全选订单', 'info');
                
            } catch (error) {
                this.logger?.logError('处理全选快捷键失败', error);
            }
        }

        /**
         * 处理取消全选（Ctrl+D）
         */
        handleDeselectAll() {
            try {
                this.recordEvent('deselectAll', { key: 'Ctrl+D' });
                
                // 防止浏览器默认行为
                event?.preventDefault();
                
                this.deselectAllOrders();
                
                this.logger?.log('⭕ 已通过快捷键取消全选', 'info');
                
            } catch (error) {
                this.logger?.logError('处理取消全选快捷键失败', error);
            }
        }

        /**
         * 确认编辑
         * @param {HTMLElement} editElement - 编辑元素
         */
        confirmEdit(editElement) {
            try {
                const orderId = editElement.dataset.orderId;
                const field = editElement.dataset.field;
                const value = editElement.value;
                
                if (!orderId || !field) {
                    this.logger?.logError('编辑元素缺少必要的数据属性');
                    return;
                }
                
                // 提交编辑
                if (this.uiManager && typeof this.uiManager.confirmEdit === 'function') {
                    this.uiManager.confirmEdit(orderId, field, value);
                }
                
                // 退出编辑模式
                editElement.closest('.quick-editing')?.classList.remove('quick-editing');
                
                this.logger?.log(`✏️ 已确认编辑: 订单${orderId}.${field} = "${value}"`, 'info');
                
            } catch (error) {
                this.logger?.logError('确认编辑失败', error);
            }
        }

        /**
         * 处理拖拽移动事件
         * @param {MouseEvent} event - 鼠标事件
         * @param {number} deltaX - X方向偏移
         * @param {number} deltaY - Y方向偏移
         */
        handleDragMove(event, deltaX, deltaY) {
            try {
                this.recordEvent('dragMove', event);
                
                if (!this.dragState.target) return;
                
                const target = this.dragState.target;
                
                // 应用拖拽变换
                if (target.style) {
                    target.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
                    target.style.zIndex = '1000';
                    target.classList.add('dragging');
                }
                
                // 检测拖拽目标
                const elementBelow = document.elementFromPoint(event.clientX, event.clientY);
                const dropTarget = elementBelow?.closest('.drop-zone');
                
                // 更新拖拽状态
                this.updateDragIndicators(dropTarget);
                
                this.logger?.log(`🔄 拖拽移动: (${deltaX}, ${deltaY})`, 'debug');
                
            } catch (error) {
                this.logger?.logError('处理拖拽移动失败', error);
            }
        }

        /**
         * 处理拖拽结束事件
         * @param {MouseEvent} event - 鼠标事件
         */
        handleDragEnd(event) {
            try {
                this.recordEvent('dragEnd', event);
                
                if (!this.dragState.target) return;
                
                const target = this.dragState.target;
                
                // 检测放置目标
                const elementBelow = document.elementFromPoint(event.clientX, event.clientY);
                const dropTarget = elementBelow?.closest('.drop-zone');
                
                if (dropTarget && dropTarget !== target.closest('.drop-zone')) {
                    // 执行拖放操作
                    this.executeDrop(target, dropTarget);
                } else {
                    // 恢复原位置
                    this.restoreDragTarget(target);
                }
                
                // 清理拖拽状态
                this.cleanupDragState(target);
                
                this.logger?.log('🎯 拖拽结束', 'info');
                
            } catch (error) {
                this.logger?.logError('处理拖拽结束失败', error);
            }
        }

        /**
         * 处理滑动手势
         * @param {string} direction - 滑动方向: left, right, up, down
         * @param {TouchEvent} event - 触摸事件
         */
        handleSwipeGesture(direction, event) {
            try {
                this.recordEvent('swipeGesture', { direction, event });
                
                const target = event.target.closest('.order-item, .multi-order-panel');
                
                if (!target) return;
                
                switch (direction) {
                    case 'left':
                        this.handleSwipeLeft(target);
                        break;
                    case 'right':
                        this.handleSwipeRight(target);
                        break;
                    case 'up':
                        this.handleSwipeUp(target);
                        break;
                    case 'down':
                        this.handleSwipeDown(target);
                        break;
                }
                
                this.logger?.log(`👆 滑动手势: ${direction}`, 'info');
                
            } catch (error) {
                this.logger?.logError('处理滑动手势失败', error);
            }
        }

        /**
         * 处理双击手势
         * @param {TouchEvent} event - 触摸事件
         */
        handleDoubleTap(event) {
            try {
                this.recordEvent('doubleTap', event);
                
                const target = event.target.closest('.order-item');
                if (!target) return;
                
                const orderId = target.dataset.orderId;
                if (!orderId) {
                    this.logger?.logError('双击目标缺少orderId数据');
                    return;
                }
                
                // 启动快捷编辑
                if (this.quickEditManager && typeof this.quickEditManager.startQuickEdit === 'function') {
                    this.quickEditManager.startQuickEdit(orderId, 'general');
                } else if (this.uiManager && typeof this.uiManager.startQuickEdit === 'function') {
                    this.uiManager.startQuickEdit(orderId, 'general');
                }
                
                this.logger?.log(`👆👆 双击启动编辑: 订单${orderId}`, 'info');
                
            } catch (error) {
                this.logger?.logError('处理双击手势失败', error);
            }
        }

        /**
         * 更新拖拽指示器
         * @param {HTMLElement} dropTarget - 放置目标
         */
        updateDragIndicators(dropTarget) {
            try {
                // 清除之前的指示器
                document.querySelectorAll('.drop-zone').forEach(zone => {
                    zone.classList.remove('drag-over');
                });
                
                // 添加当前指示器
                if (dropTarget) {
                    dropTarget.classList.add('drag-over');
                }
                
            } catch (error) {
                this.logger?.logError('更新拖拽指示器失败', error);
            }
        }

        /**
         * 执行拖放操作
         * @param {HTMLElement} dragTarget - 拖拽目标
         * @param {HTMLElement} dropTarget - 放置目标
         */
        executeDrop(dragTarget, dropTarget) {
            try {
                const sourceOrderId = dragTarget.dataset.orderId;
                const targetZone = dropTarget.dataset.dropZone;
                
                if (!sourceOrderId || !targetZone) {
                    this.logger?.logError('拖放操作缺少必要数据');
                    return;
                }
                
                // 执行拖放逻辑
                if (this.uiManager && typeof this.uiManager.executeDrop === 'function') {
                    this.uiManager.executeDrop(sourceOrderId, targetZone);
                }
                
                this.logger?.log(`📦 执行拖放: 订单${sourceOrderId} → ${targetZone}`, 'info');
                
            } catch (error) {
                this.logger?.logError('执行拖放操作失败', error);
            }
        }

        /**
         * 恢复拖拽目标位置
         * @param {HTMLElement} target - 拖拽目标
         */
        restoreDragTarget(target) {
            try {
                if (target.style) {
                    target.style.transform = '';
                    target.style.zIndex = '';
                }
                
            } catch (error) {
                this.logger?.logError('恢复拖拽目标位置失败', error);
            }
        }

        /**
         * 清理拖拽状态
         * @param {HTMLElement} target - 拖拽目标
         */
        cleanupDragState(target) {
            try {
                // 清理CSS类
                target.classList.remove('dragging');
                document.querySelectorAll('.drop-zone').forEach(zone => {
                    zone.classList.remove('drag-over');
                });
                
                // 重置拖拽状态
                this.resetDragState();
                
            } catch (error) {
                this.logger?.logError('清理拖拽状态失败', error);
            }
        }

        /**
         * 处理左滑
         * @param {HTMLElement} target - 目标元素
         */
        handleSwipeLeft(target) {
            if (target.classList.contains('order-item')) {
                // 显示订单操作菜单
                this.showOrderActions(target);
            } else if (target.classList.contains('multi-order-panel')) {
                // 切换到下一个标签页
                this.switchToNextTab();
            }
        }

        /**
         * 处理右滑
         * @param {HTMLElement} target - 目标元素
         */
        handleSwipeRight(target) {
            if (target.classList.contains('order-item')) {
                // 选择/取消选择订单
                this.toggleOrderSelection(target);
            } else if (target.classList.contains('multi-order-panel')) {
                // 切换到上一个标签页
                this.switchToPreviousTab();
            }
        }

        /**
         * 处理上滑
         * @param {HTMLElement} target - 目标元素
         */
        handleSwipeUp(target) {
            if (target.classList.contains('multi-order-panel')) {
                // 展开面板
                this.expandPanel();
            }
        }

        /**
         * 处理下滑
         * @param {HTMLElement} target - 目标元素
         */
        handleSwipeDown(target) {
            if (target.classList.contains('multi-order-panel')) {
                // 收缩面板
                this.collapsePanel();
            }
        }

        /**
         * 显示订单操作菜单
         * @param {HTMLElement} orderItem - 订单项元素
         */
        showOrderActions(orderItem) {
            if (this.uiManager && typeof this.uiManager.showOrderActions === 'function') {
                const orderId = orderItem.dataset.orderId;
                this.uiManager.showOrderActions(orderId, orderItem);
            }
        }

        /**
         * 切换订单选择状态
         * @param {HTMLElement} orderItem - 订单项元素
         */
        toggleOrderSelection(orderItem) {
            const checkbox = orderItem.querySelector('.order-checkbox');
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                checkbox.dispatchEvent(new Event('change'));
            }
        }

        /**
         * 切换到下一个标签页
         */
        switchToNextTab() {
            const tabs = document.querySelectorAll('.tab-item');
            const activeTab = document.querySelector('.tab-item.active');
            if (tabs.length > 0 && activeTab) {
                const currentIndex = Array.from(tabs).indexOf(activeTab);
                const nextIndex = (currentIndex + 1) % tabs.length;
                tabs[nextIndex].click();
            }
        }

        /**
         * 切换到上一个标签页
         */
        switchToPreviousTab() {
            const tabs = document.querySelectorAll('.tab-item');
            const activeTab = document.querySelector('.tab-item.active');
            if (tabs.length > 0 && activeTab) {
                const currentIndex = Array.from(tabs).indexOf(activeTab);
                const prevIndex = (currentIndex - 1 + tabs.length) % tabs.length;
                tabs[prevIndex].click();
            }
        }

        /**
         * 展开面板
         */
        expandPanel() {
            if (this.uiManager && typeof this.uiManager.expandPanel === 'function') {
                this.uiManager.expandPanel();
            }
        }

        /**
         * 收缩面板
         */
        collapsePanel() {
            if (this.uiManager && typeof this.uiManager.collapsePanel === 'function') {
                this.uiManager.collapsePanel();
            }
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务对象
         */
        getLogger() {
            return window.getLogger?.() || {
                log: console.log.bind(console),
                logError: console.error.bind(console)
            };
        }
    }

    /**
     * 创建事件管理器实例的工厂函数
     * @param {Object} dependencies - 依赖注入对象
     * @returns {MultiOrderEventManager} 事件管理器实例
     */
    function createMultiOrderEventManager(dependencies = {}) {
        return new MultiOrderEventManager(dependencies);
    }

    // 导出到全局作用域
    window.getMultiOrderEventManager = createMultiOrderEventManager;
    window.MultiOrderEventManager = MultiOrderEventManager;

    // 确保OTA命名空间存在
    if (typeof window.OTA === 'undefined') {
        window.OTA = {};
    }
    if (typeof window.OTA.multiOrder === 'undefined') {
        window.OTA.multiOrder = {};
    }

    // 注册到OTA命名空间
    window.OTA.multiOrder.EventManager = MultiOrderEventManager;
    window.OTA.multiOrder.getEventManager = createMultiOrderEventManager;

})();
