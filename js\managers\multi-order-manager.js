/**
 * 文件: multi-order-manager.js
 * 路径: js\managers\multi-order-manager.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.getMultiOrderUtils
 * - window.getMultiOrderCleanupManager
 * - window.getMultiOrderValidationManager
 * - window.getMultiOrderStateManager
 * - window.getMultiOrderEventManager
 * - window.getMultiOrderBatchManager
 * - window.getMultiOrderChromeMCP
 * - window.getMultiOrderQuickEditManager
 * - window.MultiOrderDetector
 * - window.MultiOrderRenderer
 * - window.MultiOrderProcessor
 * - window.MultiOrderTransformer
 * - window.OTA.utils.formatPrice
 * - window.OTA.utils.formatPhoneDisplay
 * - window.OTA.getService
 * - window.OTA.getService
 * - window.getGeminiService
 * - window.getApiService
 * - window.getService
 * - window.getAppState
 * - window.OTA.multiOrderManager.toggleOrderSelection
 * - window.OTA.multiOrderManager.quickEditOrder
 * - window.getPagingServiceManager
 * - window.getPagingServiceManager
 * - window.getPagingServiceManager
 * - window.getPagingServiceManager
 * - window.getAPIService
 * - window.OTA.getService
 * - window.OTA.getService
 * - window.OTA.getService
 * - window.OTA.getService
 * - window.OTA.getService
 * - window.OTA.multiOrderManager.editOrderFields
 * - window.OTA.multiOrderManager.createSingleOrder
 * - window.OTA.multiOrderManager.editAddress
 * - window.OTA.multiOrderManager.editAddress
 * - window.OTA.getService
 * - window.OTA.getService
 * - window.OTA.getService
 * - window.getUiManager
 * - window.getUIManager
 * - window.orderHistoryManager
 * - window.OTA.getService
 * - window.OTA.getService
 * - window.location.href
 * - window.getUIManager
 * - window.getUIManager
 * - window.OTA.getService
 * - window.OTA.getService
 * - window.getUIManager
 * - window.OTA.multiOrderManager.editField
 * - window.OTA.multiOrderManager.editField
 * - window.OTA.multiOrderManager.editField
 * - window.OTA.multiOrderManager.editField
 * - window.OTA.multiOrderManager.editField
 * - window.OTA.multiOrderManager.editField
 * - window.OTA.multiOrderManager.editField
 * - window.OTA.multiOrderManager.editField
 * - window.OTA.multiOrderManager.handleQuickEditBlur
 * - window.OTA.multiOrderManager.handleQuickEditInput
 * - window.OTA.multiOrderManager.handleQuickEditKeypress
 * - window.OTA.multiOrderManager.generateOtaReference
 * - window.OTA.multiOrderManager.exitQuickEdit
 * - window.OTA.multiOrderManager.exitQuickEdit
 * - window.OTA.multiOrderManager.saveQuickEdit
 * - window.OTA.multiOrderManager.exitQuickEdit
 * - window.OTA.otaChannelMapping.getConfig
 * - window.OTA.otaChannelMapping.getConfig
 * - window.OTA.otaChannelMapping.commonChannels
 * - window.OTA.multiOrderManager.toggleLanguageDropdown
 * - window.OTA.multiOrderManager.updateLanguageSelection
 * - window.innerWidth
 * - window.innerHeight
 * - window.innerWidth
 * - window.innerHeight
 * - window.addEventListener
 * - window.uiCorrectionManager
 * - window.getI18nManager
 * - window.getI18nManager
 * - window.OTA.i18nManager
 * - window.i18nManager
 * - window.OTA.Registry
 * - window.OTA.container
 * - window.OTA.container.register
 * - document (DOM API)
 * - console (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.MultiOrderManager
 * - window.getMultiOrderManager
 * - window.OTA
 * - window.OTA.MultiOrderManager
 * - window.OTA.getMultiOrderManager
 * 
 * === 类声明 ===
 * - class MultiOrderManager
 * 
 * === 函数声明 ===
 * - function getMultiOrderManager()
 * - function registerMultiOrderManager()
 * - function get()
 * - function get()
 * 
 * === 事件监听 ===
 * - change 事件
 * - click 事件
 * - blur 事件
 * - keypress 事件
 * - click 事件
 * - click 事件
 * - click 事件
 * - click 事件
 * - click 事件
 * - click 事件
 * - click 事件
 * - click 事件
 * - blur 事件
 * - keydown 事件
 * - focus 事件
 * - click 事件
 * - keydown 事件
 * - mousedown 事件
 * - mousemove 事件
 * - mouseup 事件
 * - multiOrderDetected 事件
 * - appStateChanged 事件
 * - beforeunload 事件
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.808Z
 */

/**
 * @OTA_MANAGER 多订单处理管理器 - 重构版 v5.0 (模块化完成)
 * 🏷️ 标签: @OTA_MULTI_ORDER_MANAGER
 * 📝 说明: 核心协调器，管理多订单检测、UI渲染、批量处理和数据转换模块
 * ⚠️ 警告: 已重构为模块化架构，请勿重复开发
 *
 * 🎯 重构完成状态:
 * ✅ 工具函数模块化 (multi-order-utils.js)
 * ✅ 清理管理器模块化 (multi-order-cleanup-manager.js)
 * ✅ 验证管理器模块化 (multi-order-validation-manager.js)
 * ✅ UI管理器模块化 (multi-order-ui-manager.js + multi-order-quick-edit-manager.js)
 * ✅ 状态管理器模块化 (multi-order-state-manager.js)
 * ✅ 事件管理器模块化 (multi-order-event-manager.js)
 * ✅ 批量操作管理器模块化 (multi-order-batch-manager.js)
 * ✅ Chrome MCP集成器模块化 (multi-order-chrome-mcp.js)
 * ✅ 主文件精简优化 (从5060行减少到4407行，减少约13%)
 *
 * 🏗️ 架构特点: 模块化架构、委托模式、统一服务定位、降级方案、字段映射保护
 * <AUTHOR>
 * @version 5.0.0-modular-complete
 */

// 防止重复加载
if (window.OTA && window.OTA.MultiOrderManager) {
    // 移除调试日志：重构版多订单管理器已存在
} else {

/**
 * 多订单管理器核心类
 * 作为协调器管理各个子模块
 */
class MultiOrderManager {
    constructor(dependencies = {}) {
        // 🏗️ 模块化配置
        this.config = {
            minInputLength: 50,
            debounceDelay: 1200,
            maxOrdersPerBatch: 5,
            batchDelay: 800,
            confidenceThreshold: 0.7,
            ...dependencies.config
        };

        // 🏗️ 简化状态管理
        this.state = {
            isMultiOrderMode: false,
            currentSegments: [],
            selectedOrders: new Set(),
            processedOrders: new Map(),
            parsedOrders: [],
            multiOrderResult: null,
            batchProgress: {
                total: 0,
                completed: 0,
                failed: 0,
                isRunning: false,
                startTime: null,
                processingOrder: null
            }
        };

        // 🏗️ 事件管理
        this.eventListeners = new Map();
        this.debounceTimer = null;
        this.boundEventHandlers = new Map();

        // 🏗️ 服务依赖 - 延迟初始化避免循环依赖
        this._logger = null;

        // 🗑️ 已移除重复的ID映射表 - 减法重构优化
        // ID映射已统一到 js/ota-channel-mapping.js 和系统数据中
        // 使用统一配置：window.OTA.otaChannelMapping 和 appState.systemData
        this.idMappings = {
            // 🗑️ 后端用户映射已统一到系统数据 - 减法重构优化
            backendUsers: {},
            // 🗑️ 服务类型映射已统一到系统数据 - 减法重构优化
            subCategories: [],
            languages: [
                { id: 2, name: 'English' },
                { id: 4, name: 'Chinese' },
                { id: 3, name: 'Malay' }
            ]
        };

        // 🏗️ 初始化模块
        this.initializeModules(dependencies);
        this.init();
        this.setupGeneralCleanup();
        this.setupCleanupMechanism();
    }

    /**
     * 初始化各个功能模块
     * @param {Object} dependencies - 依赖注入对象
     */
    initializeModules(dependencies = {}) {
        try {
            // 初始化工具函数模块
            this.utils = dependencies.utils || window.getMultiOrderUtils?.() || this.createFallbackUtils();

            // 初始化清理管理器
            this.cleanupManager = dependencies.cleanupManager || window.getMultiOrderCleanupManager?.({
                logger: this.logger,
                config: this.config
            }) || this.createFallbackCleanupManager();

            // 初始化验证管理器
            this.validationManager = dependencies.validationManager || window.getMultiOrderValidationManager?.({
                logger: this.logger,
                config: this.config
            }) || this.createFallbackValidationManager();

            // 延迟初始化UI交互管理器避免循环依赖
            this._uiManager = null;
            this._dependencies = dependencies;

            // 初始化状态管理器 - 延迟获取appState避免循环依赖
            this.stateManager = dependencies.stateManager || window.getMultiOrderStateManager?.({
                logger: this.getLogger(),
                config: this.config,
                appState: null // 延迟初始化
            }) || this.createFallbackStateManager();

            // 初始化事件管理器
            this.eventManager = dependencies.eventManager || window.getMultiOrderEventManager?.({
                logger: this.logger,
                config: this.config,
                stateManager: this.stateManager,
                uiManager: this.uiManager,
                quickEditManager: null // 将在快捷编辑管理器初始化后设置
            }) || this.createFallbackEventManager();

            // 初始化批量操作管理器 - 延迟获取apiService避免循环依赖
            this.batchManager = dependencies.batchManager || window.getMultiOrderBatchManager?.({
                logger: this.getLogger(),
                config: this.config,
                stateManager: this.stateManager,
                validationManager: this.validationManager,
                apiService: null // 延迟初始化
            }) || this.createFallbackBatchManager();

            // 初始化Chrome MCP集成器
            this.chromeMCP = dependencies.chromeMCP || window.getMultiOrderChromeMCP?.({
                logger: this.logger,
                config: this.config,
                stateManager: this.stateManager,
                validationManager: this.validationManager
            }) || this.createFallbackChromeMCP();

            // 初始化快捷编辑管理器
            this.quickEditManager = dependencies.quickEditManager || window.getMultiOrderQuickEditManager?.({
                logger: this.logger,
                config: this.config,
                state: this.stateManager.getState(),
                utils: this.utils,
                validationManager: this.validationManager,
                cleanupManager: this.cleanupManager
            }) || this.createFallbackQuickEditManager();

            // 更新事件管理器的快捷编辑管理器引用
            if (this.eventManager && this.eventManager.setQuickEditManager) {
                this.eventManager.setQuickEditManager(this.quickEditManager);
            }

            // 初始化检测服务
            this.detector = dependencies.detector || new (window.OTA?.MultiOrderDetector || window.MultiOrderDetector)(this.config);

            // 初始化UI渲染器
            this.renderer = dependencies.renderer || new (window.OTA?.MultiOrderRenderer || window.MultiOrderRenderer)(this.config);

            // 初始化批量处理器
            this.processor = dependencies.processor || new (window.OTA?.MultiOrderProcessor || window.MultiOrderProcessor)(this.config);

            // 初始化数据转换器（如果可用）
            const TransformerClass = window.OTA?.MultiOrderTransformer || window.MultiOrderTransformer;
            this.transformer = dependencies.transformer || (TransformerClass ? new TransformerClass(this.config) : null);

            this.logger?.log('✅ 所有模块初始化完成', 'success', {
                utils: !!this.utils,
                cleanupManager: !!this.cleanupManager,
                detector: !!this.detector,
                renderer: !!this.renderer,
                processor: !!this.processor,
                transformer: !!this.transformer
            });

        } catch (error) {
            this.logger?.logError('模块初始化失败', error);
            // 提供降级方案
            this.initializeFallbackMethods();
        }
    }

    /**
     * 创建工具函数降级方案
     * @returns {Object} 工具函数对象
     */
    createFallbackUtils() {
        return {
            formatPrice: (price, currency = 'MYR') => {
                // 优先使用统一工具函数
                if (window.OTA?.utils?.formatPrice) {
                    return window.OTA.utils.formatPrice(price, currency);
                }
                return `${currency} ${price || '0.00'}`;
            },
            formatPhone: (phone) => {
                // 优先使用统一工具函数
                if (window.OTA?.utils?.formatPhoneDisplay) {
                    return window.OTA.utils.formatPhoneDisplay(phone);
                }
                return phone || '未提供';
            },
            getServiceTypeName: (id) => `服务${id}`,
            getCarTypeName: (id) => `车型${id}`,
            getLanguageName: () => '英文',
            getOtaChannelName: (code) => code || '未知渠道',
            getOtaReferenceNumber: (order) => order?.otaReferenceNumber || '无参考号',
            analyzeTimePointsAndFlights: () => ({ timePoints: [], hasFlightFeatures: false, isMultiTimePoint: false }),
            debugOrderFieldMapping: () => {}
        };
    }

    /**
     * 创建清理管理器降级方案
     * @returns {Object} 清理管理器对象
     */
    createFallbackCleanupManager() {
        return {
            clearAllSelections: (state) => {
                const checkboxes = document.querySelectorAll('.order-checkbox');
                checkboxes.forEach(checkbox => checkbox.checked = false);
                if (state?.selectedOrders) state.selectedOrders.clear();
            },
            clearCache: (state) => {
                if (state) {
                    if (state.processedOrders) state.processedOrders.clear();
                    if (state.selectedOrders) state.selectedOrders.clear();
                }
            },
            cleanupQuickEditPanels: () => {
                document.querySelectorAll('.quick-edit-overlay').forEach(el => el.remove());
            },
            cleanupBatchProcessing: () => {},
            cleanupDomReferences: () => {},
            cleanup: () => {},
            exitFieldEdit: () => {},
            exitQuickEdit: () => {},
            destroy: () => {}
        };
    }

    /**
     * 创建验证管理器降级方案
     * @returns {Object} 验证管理器对象
     */
    createFallbackValidationManager() {
        return {
            validateField: (index, fieldName, value) => {
                console.log(`降级验证: 订单${index} 字段${fieldName} 值${value}`);
            },
            validateAndEnhanceOtaReference: (index, state) => {
                console.log(`降级OTA参考号验证: 订单${index}`);
            },
            validateAndFormatOrderFields: (order) => {
                console.log('降级订单字段验证');
            },
            checkCommonFieldDisplayIssues: (orders) => {
                return { missingRequiredFields: [], invalidEmailFormats: [], invalidPhoneFormats: [] };
            },
            getSelectedOrderIndexes: () => {
                const checkboxes = document.querySelectorAll('.order-checkbox:checked');
                return Array.from(checkboxes).map(checkbox => {
                    const orderItem = checkbox.closest('.order-card');
                    return parseInt(orderItem.getAttribute('data-order-index'));
                }).filter(index => !isNaN(index));
            },
            validateSelectedOrders: () => {
                return { isValid: true, message: '降级模式：跳过验证', errors: [] };
            },
            clearValidationCache: () => {},
            destroy: () => {}
        };
    }

    /**
     * 创建UI管理器降级方案
     * @returns {Object} UI管理器对象
     */
    createFallbackUIManager() {
        return {
            showMultiOrderPanel: (orders) => {
                console.warn('UI管理器不可用，使用降级方案显示面板');
                const multiOrderPanel = document.getElementById('multiOrderPanel');
                if (multiOrderPanel) {
                    multiOrderPanel.classList.remove('hidden');
                    multiOrderPanel.style.display = 'block';
                }
            },
            hideMultiOrderPanel: () => {
                console.warn('UI管理器不可用，使用降级方案隐藏面板');
                const multiOrderPanel = document.getElementById('multiOrderPanel');
                if (multiOrderPanel) {
                    multiOrderPanel.classList.add('hidden');
                    multiOrderPanel.style.display = 'none';
                }
            },
            updatePanelContent: () => console.warn('UI管理器不可用：updatePanelContent'),
            toggleOrderSelection: () => console.warn('UI管理器不可用：toggleOrderSelection'),
            toggleLanguageDropdown: () => console.warn('UI管理器不可用：toggleLanguageDropdown'),
            initPanelEvents: () => console.warn('UI管理器不可用：initPanelEvents'),
            ensurePanelVisible: () => console.warn('UI管理器不可用：ensurePanelVisible'),
            addPanelDragFeature: () => console.warn('UI管理器不可用：addPanelDragFeature'),
            updateSelectedCount: () => console.warn('UI管理器不可用：updateSelectedCount'),
            selectAllOrders: () => console.warn('UI管理器不可用：selectAllOrders'),
            cleanupQuickEditPanels: () => {
                document.querySelectorAll('.quick-edit-overlay').forEach(el => el.remove());
            },
            destroy: () => {}
        };
    }

    /**
     * 创建状态管理器降级方案
     * @returns {Object} 状态管理器对象
     */
    createFallbackStateManager() {
        return {
            getState: () => this.state,
            getStateValue: (path) => {
                const keys = path.split('.');
                let value = this.state;
                for (const key of keys) {
                    if (value && typeof value === 'object' && key in value) {
                        value = value[key];
                    } else {
                        return undefined;
                    }
                }
                return value;
            },
            setStateValue: (path, value) => {
                console.warn(`状态管理器不可用：setStateValue(${path}, ${value})`);
            },
            resetMultiOrderState: () => {
                this.state.isMultiOrderMode = false;
                this.state.currentSegments = [];
                this.state.selectedOrders.clear();
                this.state.processedOrders.clear();
                this.state.parsedOrders = [];
            },
            setMultiOrderMode: (enabled, segments = [], orders = []) => {
                this.state.isMultiOrderMode = enabled;
                this.state.currentSegments = segments;
                this.state.parsedOrders = orders;
                this.state.selectedOrders.clear();
                this.state.processedOrders.clear();
                if (enabled && orders.length > 0) {
                    orders.forEach((_, index) => {
                        this.state.selectedOrders.add(index);
                    });
                }
            },
            updateOrderSelection: (index, selected) => {
                if (selected) {
                    this.state.selectedOrders.add(index);
                } else {
                    this.state.selectedOrders.delete(index);
                }
            },
            getSelectedOrders: () => {
                if (!this.state.parsedOrders) return [];
                return Array.from(this.state.selectedOrders)
                    .map(index => this.state.parsedOrders[index])
                    .filter(Boolean);
            },
            getSelectedOrderIndexes: () => Array.from(this.state.selectedOrders),
            updateOrderData: (index, orderData) => {
                if (this.state.parsedOrders[index]) {
                    this.state.parsedOrders[index] = { ...this.state.parsedOrders[index], ...orderData };
                }
            },
            updateOrderField: (index, fieldName, value) => {
                if (this.state.parsedOrders[index]) {
                    this.state.parsedOrders[index][fieldName] = value;
                }
            },
            setProcessedOrder: (index, processedData) => {
                this.state.processedOrders.set(index, processedData);
            },
            getProcessedOrder: (index) => this.state.processedOrders.get(index) || null,
            initBatchProgress: (total) => {
                this.state.batchProgress = {
                    total, completed: 0, failed: 0, isRunning: true,
                    startTime: Date.now(), processingOrder: null
                };
            },
            updateBatchProgress: (progress) => Object.assign(this.state.batchProgress, progress),
            completeBatchProgress: () => {
                this.state.batchProgress.isRunning = false;
                this.state.batchProgress.processingOrder = null;
            },
            getBatchProgress: () => ({ ...this.state.batchProgress }),
            addErrorLog: (errorDetails) => {
                if (!this.state.errorLog) this.state.errorLog = [];
                this.state.errorLog.push(errorDetails);
            },
            getErrorLog: () => [...(this.state.errorLog || [])],
            clearErrorLog: () => { this.state.errorLog = []; },
            addStateListener: () => console.warn('状态管理器不可用：addStateListener'),
            removeStateListener: () => console.warn('状态管理器不可用：removeStateListener'),
            validateState: () => ({ valid: true, issues: [] }),
            saveStateToAppState: () => console.warn('状态管理器不可用：saveStateToAppState'),
            restoreStateFromAppState: () => console.warn('状态管理器不可用：restoreStateFromAppState'),
            getStateStats: () => ({
                totalOrders: this.state.parsedOrders.length,
                selectedOrders: this.state.selectedOrders.size,
                processedOrders: this.state.processedOrders.size,
                isMultiOrderMode: this.state.isMultiOrderMode
            }),
            resetAllState: () => this.resetMultiOrderState(),
            destroy: () => {}
        };
    }

    /**
     * 创建事件管理器降级方案
     * @returns {Object} 事件管理器对象
     */
    createFallbackEventManager() {
        return {
            init: () => console.warn('事件管理器不可用：init'),
            bindGlobalEventHandlers: () => console.warn('事件管理器不可用：bindGlobalEventHandlers'),
            bindPanelEvents: () => console.warn('事件管理器不可用：bindPanelEvents'),
            bindKeyboardShortcuts: () => console.warn('事件管理器不可用：bindKeyboardShortcuts'),
            bindTouchEvents: () => console.warn('事件管理器不可用：bindTouchEvents'),
            addEventHandler: () => console.warn('事件管理器不可用：addEventHandler'),
            removeEventHandler: () => console.warn('事件管理器不可用：removeEventHandler'),
            debounce: (func, delay) => func, // 简单返回原函数
            handleDocumentClick: () => {},
            handlePanelClick: () => {},
            handlePanelChange: () => {},
            handlePanelInput: () => {},
            handleKeyDown: () => {},
            handleKeyUp: () => {},
            handleWindowResize: () => {},
            handleBeforeUnload: () => {},
            handleMouseDown: () => {},
            handleMouseMove: () => {},
            handleMouseUp: () => {},
            handleTouchStart: () => {},
            handleTouchMove: () => {},
            handleTouchEnd: () => {},
            recordEvent: () => {},
            resetDragState: () => {},
            cleanup: () => {},
            destroy: () => {},
            getEventStats: () => ({
                boundHandlers: 0,
                activeEvents: 0,
                eventHistory: 0,
                debounceTimers: 0,
                isDragging: false
            }),
            setQuickEditManager: () => console.warn('事件管理器不可用：setQuickEditManager')
        };
    }

    /**
     * 创建批量操作管理器降级方案
     * @returns {Object} 批量操作管理器对象
     */
    createFallbackBatchManager() {
        return {
            init: () => console.warn('批量操作管理器不可用：init'),
            batchCreateOrders: async (orders) => {
                console.warn('批量操作管理器不可用：batchCreateOrders');
                return { successful: [], failed: orders.map((order, index) => ({ order, index, error: '批量操作管理器不可用' })) };
            },
            batchValidateOrders: async (orders) => {
                console.warn('批量操作管理器不可用：batchValidateOrders');
                return { hasErrors: false, hasWarnings: false, errors: [], warnings: [], totalOrders: orders.length, validOrders: orders.length };
            },
            batchApplySettings: (orders, settings) => {
                console.warn('批量操作管理器不可用：batchApplySettings');
                return { updatedCount: 0, errors: [], success: false };
            },
            getBatchStats: () => ({
                isRunning: false,
                currentBatchSize: 0,
                queueSize: 0,
                completedCount: 0,
                failedCount: 0,
                retryQueueSize: 0,
                activeConcurrency: 0,
                maxConcurrency: 0,
                averageProcessingTime: 0,
                estimatedTimeRemaining: 0,
                successRate: 0
            }),
            stopBatchOperation: () => console.warn('批量操作管理器不可用：stopBatchOperation'),
            cleanup: () => console.warn('批量操作管理器不可用：cleanup'),
            destroy: () => console.warn('批量操作管理器不可用：destroy')
        };
    }

    /**
     * 创建Chrome MCP集成器降级方案
     * @returns {Object} Chrome MCP集成器对象
     */
    createFallbackChromeMCP() {
        return {
            init: () => console.warn('Chrome MCP集成器不可用：init'),
            getChromeMCP: () => ({
                navigate: async () => { throw new Error('Chrome MCP不可用'); },
                screenshot: async () => { throw new Error('Chrome MCP不可用'); },
                getContent: async () => { throw new Error('Chrome MCP不可用'); },
                click: async () => { throw new Error('Chrome MCP不可用'); },
                fill: async () => { throw new Error('Chrome MCP不可用'); },
                getInteractiveElements: async () => { throw new Error('Chrome MCP不可用'); },
                injectScript: async () => { throw new Error('Chrome MCP不可用'); },
                sendCommand: async () => { throw new Error('Chrome MCP不可用'); },
                networkRequest: async () => { throw new Error('Chrome MCP不可用'); }
            }),
            testChromeMCPIntegration: async () => ({
                passed: [],
                failed: [{ test: 'all', error: 'Chrome MCP集成器不可用' }],
                successRate: 0
            }),
            isChromeMCPAvailable: () => false,
            cleanup: () => console.warn('Chrome MCP集成器不可用：cleanup'),
            destroy: () => console.warn('Chrome MCP集成器不可用：destroy')
        };
    }

    /**
     * 创建快捷编辑管理器降级方案
     * @returns {Object} 快捷编辑管理器对象
     */
    createFallbackQuickEditManager() {
        return {
            quickEditOrder: (index) => console.warn(`快捷编辑管理器不可用：quickEditOrder(${index})`),
            createQuickEditPanel: (index) => console.warn(`快捷编辑管理器不可用：createQuickEditPanel(${index})`),
            exitQuickEdit: (index) => {
                console.warn(`快捷编辑管理器不可用，使用降级方案：exitQuickEdit(${index})`);
                const orderItem = document.querySelector(`.order-card[data-order-index="${index}"]`);
                if (orderItem) {
                    orderItem.classList.remove('editing');
                }
                const overlay = document.querySelector(`.quick-edit-overlay[data-order-index="${index}"]`);
                if (overlay) {
                    overlay.remove();
                }
            },
            saveQuickEdit: (index) => console.warn(`快捷编辑管理器不可用：saveQuickEdit(${index})`),
            updateOrderField: (index, fieldName, value) => {
                console.warn(`快捷编辑管理器不可用：updateOrderField(${index}, ${fieldName}, ${value})`);
            },
            cleanupQuickEditPanels: () => {
                document.querySelectorAll('.quick-edit-overlay').forEach(el => el.remove());
            },
            handleQuickEditBlur: () => console.warn('快捷编辑管理器不可用：handleQuickEditBlur'),
            handleQuickEditInput: () => console.warn('快捷编辑管理器不可用：handleQuickEditInput'),
            handleQuickEditKeypress: () => console.warn('快捷编辑管理器不可用：handleQuickEditKeypress'),
            handleQuickEditEscape: () => console.warn('快捷编辑管理器不可用：handleQuickEditEscape'),
            destroy: () => {}
        };
    }

    /**
     * 提供降级方案（当模块加载失败时）
     */
    initializeFallbackMethods() {
        this.logger?.log('⚠️ 启用降级方案', 'warn');

        // 基本的检测功能
        if (!this.detector) {
            this.detector = {
                detectMultiOrder: async () => ({ isMultiOrder: false, orderCount: 1, orders: [] })
            };
        }

        // 基本的渲染功能
        if (!this.renderer) {
            this.renderer = {
                showMultiOrderPanel: () => console.warn('UI渲染器不可用'),
                hideMultiOrderPanel: () => console.warn('UI渲染器不可用')
            };
        }

        // 基本的处理功能
        if (!this.processor) {
            this.processor = {
                processOrder: async () => ({ success: false, error: { message: '处理器不可用' } }),
                createSingleOrder: async () => ({ success: false, error: { message: '处理器不可用' } })
            };
        }
    }

    /**
     * 获取日志服务 - 延迟初始化避免循环依赖
     * @returns {Object} 日志服务实例
     */
    getLogger() {
        if (!this._logger) {
            try {
                this._logger = window.OTA.getService('logger');
            } catch (error) {
                // 降级到控制台
                this._logger = {
                    log: console.log.bind(console),
                    logError: console.error.bind(console)
                };
            }
        }
        return this._logger;
    }

    /**
     * 延迟获取UIManager避免循环依赖
     * @returns {Object} UI管理器实例
     */
    get uiManager() {
        if (!this._uiManager) {
            // 优先使用依赖注入
            if (this._dependencies?.uiManager) {
                this._uiManager = this._dependencies.uiManager;
            } else {
                // 使用统一服务获取
                try {
                    this._uiManager = window.OTA.getService('uiManager');
                } catch (error) {
                    // 最后降级到创建备用管理器
                    this._uiManager = this.createFallbackUIManager();
                }
            }
        }
        return this._uiManager;
    }

    /**
     * 获取logger属性 - 向后兼容
     * @returns {Object} 日志记录器实例
     */
    get logger() {
        return this.getLogger();
    }

    /**
     * 获取服务实例的统一方法
     * @param {string} serviceName - 服务名称
     * @returns {Object} 服务实例
     */
    getService(serviceName) {
        const serviceMap = {
            'gemini': () => window.getGeminiService?.(),
            'api': () => window.getApiService?.() || window.getService?.('apiService'),
            'logger': () => this.getLogger(),
            'appState': () => window.getAppState?.()
        };

        const serviceGetter = serviceMap[serviceName];
        return serviceGetter ? serviceGetter() : null;
    }

    /**
     * 获取Gemini配置
     * @returns {Object} Gemini配置对象
     */
    getGeminiConfig() {
        return {
            confidenceThreshold: this.config.confidenceThreshold,
            maxRetries: 3,
            timeout: 30000
        };
    }

    /**
     * 创建单个订单（委托给批量处理器模块）
     * @param {number} index - 订单索引
     * @returns {Promise<Object>} 创建结果
     */
    async createSingleOrder(index) {
        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            this.logger?.logError('订单索引无效或订单数据不存在', { index, ordersLength: this.state.parsedOrders?.length });
            return { success: false, error: { message: '订单索引无效' } };
        }

        const orderData = this.state.parsedOrders[index];

        // 🏗️ 委托给批量处理器模块
        if (this.processor && typeof this.processor.createSingleOrder === 'function') {
            return await this.processor.createSingleOrder(orderData, index);
        }

        // 降级方案
        this.logger?.logError('批量处理器不可用', { index });
        return { success: false, error: { message: '批量处理器不可用' } };
    }

    /**
     * 批量创建选中的订单（委托给批量处理器模块）
     * @returns {Promise<Object>} 批量创建结果
     */
    async createSelectedOrders() {
        const selectedOrders = this.getSelectedOrders();

        if (selectedOrders.length === 0) {
            this.logger?.log('没有选中的订单', 'warn');
            return { success: false, reason: '没有选中的订单' };
        }

        // 🏗️ 委托给批量处理器模块
        if (this.processor && typeof this.processor.createSelectedOrders === 'function') {
            return await this.processor.createSelectedOrders(selectedOrders);
        }

        // 降级方案
        this.logger?.logError('批量处理器不可用');
        return { success: false, error: { message: '批量处理器不可用' } };
    }

    /**
     * 获取选中的订单（委托给状态管理器）
     * @returns {Array} 选中的订单数组
     */
    getSelectedOrders() {
        return this.stateManager.getSelectedOrders();
    }

    /**
     * 切换订单选择状态（委托给状态管理器和UI管理器）
     * @param {number} index - 订单索引
     */
    toggleOrderSelection(index) {
        // 🏗️ 委托给状态管理器更新选择状态
        const currentlySelected = this.stateManager.getStateValue('selectedOrders').has(index);
        this.stateManager.updateOrderSelection(index, !currentlySelected);

        // 🏗️ 委托给UI管理器处理界面更新
        this.uiManager.toggleOrderSelection(index);
    }

    /**
     * 设置通用清理机制（委托给清理管理器）
     */
    setupGeneralCleanup() {
        // 清理机制现在由清理管理器处理
        this.logger?.log('通用清理机制设置完成（委托给清理管理器）', 'info');
    }

    /**
     * 初始化面板事件
     */
    initPanelEvents() {
        // 面板事件现在由UI渲染器模块处理
        this.logger?.log('面板事件初始化完成（委托给UI渲染器）', 'info');
    }

    /**
     * 设置输入事件监听器（已禁用 - 统一使用实时分析管理器）
     */
    setupInputListener() {
        this.logger?.log('⚠️ 多订单管理器输入监听器已禁用（使用统一事件流程）', 'info');
    }

    /**
     * 更新选中订单数量显示
     */
    updateSelectedCount() {
        const checkboxes = document.querySelectorAll('.order-checkbox:checked');

        // 更新footer中的计数器（保持兼容性）
        const countElement = document.getElementById('selectedOrderCount');
        if (countElement) {
            countElement.textContent = `已选择 ${checkboxes.length} 个订单`;
        }
    }

    /**
     * 更新订单统计信息
     * @param {number} orderCount - 订单数量
     */
    updateOrderStats(orderCount) {
        const countElement = document.getElementById('multiOrderCount');
        if (countElement) {
            countElement.textContent = `${orderCount} 个订单`;
        }
    }

    /**
     * 初始化管理器
     */
    init() {
        this.logger?.log('多订单管理器v4.0正在初始化...', 'info');

        this.setupInputListener();
        this.initPanelEvents();
        this.initBatchControls(); // 初始化批量操作功能

        this.logger?.log('多订单管理器v4.0初始化完成', 'success');
    }


    /**
     * 处理多订单检测事件（统一入口版本）
     * @param {object} multiOrderResult - Gemini返回的完整多订单检测结果
     * @param {string} orderText - 订单文本
     */
    handleMultiOrderDetectionUnified(multiOrderResult, orderText) {
        console.group('🔍 多订单数据流追踪 - 第6步：handleMultiOrderDetectionUnified');
        this.logger?.log(`🔄 处理多订单检测事件: ${multiOrderResult?.orderCount || 0}个订单`, 'info');

        // 在console中显示详细的处理过程
        console.log('参数检查:');
        console.log('  multiOrderResult:', multiOrderResult);
        console.log('  orderText长度:', orderText?.length);
        console.log('  orderCount:', multiOrderResult?.orderCount);
        console.log('  orders数组:', multiOrderResult?.orders);
        console.log('  orders长度:', multiOrderResult?.orders?.length);
        console.log('  isMultiOrder:', multiOrderResult?.isMultiOrder);
        console.groupEnd();

        try {
            // 验证输入参数
            if (!multiOrderResult || typeof multiOrderResult !== 'object') {
                console.error('❌ 多订单检测结果格式无效');
                throw new Error('多订单检测结果格式无效');
            }

            // 🎯 核心逻辑：根据orderCount决定处理方式
            console.log('🎯 检查条件:', {
                'orderCount > 1': multiOrderResult.orderCount > 1,
                'orders存在': !!multiOrderResult.orders,
                'orders.length > 1': multiOrderResult.orders && multiOrderResult.orders.length > 1
            });
            
            if (multiOrderResult.orderCount > 1 && multiOrderResult.orders && multiOrderResult.orders.length > 1) {
                console.log('✅ 进入多订单模式处理逻辑');
                
                // 多订单模式：直接显示多订单面板
                this.logger?.log(`✅ 确认多订单模式，显示${multiOrderResult.orders.length}个订单的面板`, 'success');
                console.log(`✅ 确认多订单模式，显示${multiOrderResult.orders.length}个订单的面板`);
                
                // 保存多订单状态
                this.state.isMultiOrderMode = true;
                this.state.parsedOrders = multiOrderResult.orders;
                this.state.multiOrderResult = multiOrderResult;
                console.log('💾 多订单状态已保存');
                
                console.group('🔍 多订单数据流追踪 - 第7步：面板显示准备');
                const panel = document.getElementById('multiOrderPanel');
                console.log('面板DOM检查:', {
                    panel: panel,
                    panelExists: !!panel,
                    panelId: panel?.id,
                    panelClasses: panel?.className,
                    panelDisplay: panel?.style.display,
                    panelOffsetHeight: panel?.offsetHeight
                });
                console.groupEnd();
                
                try {
                    // 🏗️ 使用UI渲染器模块显示多订单面板
                    console.group('🔍 多订单数据流追踪 - 第8步：调用renderer.showMultiOrderPanel');
                    console.log('传入orders数据:', multiOrderResult.orders);
                    console.log('orders数量:', multiOrderResult.orders?.length);
                    this.renderer.showMultiOrderPanel(multiOrderResult.orders);
                    console.log('✅ renderer.showMultiOrderPanel调用完成');
                    console.groupEnd();

                    this.logger?.log('🎉 多订单面板显示命令已执行', 'success');
                } catch (showPanelError) {
                    console.error('❌ renderer.showMultiOrderPanel执行失败:', showPanelError);
                    this.logger?.logError('renderer.showMultiOrderPanel执行失败', showPanelError);
                }

            } else if (multiOrderResult.orderCount === 1) {
                // 单订单模式：隐藏多订单面板（如果之前显示过）
                this.logger?.log('✅ 确认单订单模式，隐藏多订单面板', 'info');
                this.renderer.hideMultiOrderPanel();

            } else {
                // 无有效订单：隐藏面板并记录
                this.logger?.log('⚠️ 无有效订单，隐藏多订单面板', 'warning');
                this.renderer.hideMultiOrderPanel();
            }
            
        } catch (error) {
            this.logger?.logError('处理多订单检测事件失败（统一入口）', error);
            this.renderer.hideMultiOrderPanel();
        }
    }

    /**
     * 处理多订单检测事件（旧版本，保留向后兼容）
     * @param {object} data - 订单数据
     * @param {string} orderText - 订单文本
     */
    handleMultiOrderDetection(data, orderText) {
        this.logger?.log('🔄 处理多订单检测事件', 'info', {
            dataType: Array.isArray(data) ? 'array' : typeof data,
            orderLength: orderText?.length || 0
        });

        try {
            // 处理不同格式的数据
            let processedData = data;

            // 如果是对象，尝试转换为数组
            if (data && typeof data === 'object' && !Array.isArray(data)) {
                // 检查是否有 orders 属性
                if (data.orders && Array.isArray(data.orders)) {
                    processedData = data.orders;
                }
                // 检查是否有 segments 属性
                else if (data.segments && Array.isArray(data.segments)) {
                    processedData = data.segments;
                }
                // 检查是否有 items 属性
                else if (data.items && Array.isArray(data.items)) {
                    processedData = data.items;
                }
                // 如果对象有多个键值对，可能每个键都是一个订单
                else {
                    const keys = Object.keys(data);
                    if (keys.length > 1) {
                        processedData = keys.map(key => ({
                            id: key,
                            ...data[key]
                        }));
                    }
                }
            }

            // 🏗️ 使用模块化架构处理多订单逻辑
            if (Array.isArray(processedData) && processedData.length > 1) {
                this.logger?.log(`检测到${processedData.length}个订单，显示多订单面板`, 'info');
                this.renderer.showMultiOrderPanel(processedData);
            } else if (Array.isArray(processedData) && processedData.length === 1) {
                this.logger?.log('检测到单个订单，进行智能分析', 'info');
                this.analyzeInputForMultiOrder(orderText);
            } else {
                this.logger?.log('无有效数据或无法识别格式，进行智能分析', 'info');
                // 检查是否包含多个时间点或地点，使用智能分析
                this.analyzeInputForMultiOrder(orderText);
            }
        } catch (error) {
            this.logger?.logError('处理多订单检测事件失败', error);
        }
    }

    /**
     * 处理订单状态变化
     * @param {object} orderData - 订单数据
     */
    handleOrderStateChange(orderData) {
        this.logger?.log('🔄 处理订单状态变化', 'info', orderData);

        try {
            // 如果有当前订单数据，可以进行额外的处理
            if (orderData) {
                // 这里可以添加订单状态变化的处理逻辑
                // 例如：更新UI显示、保存到历史记录等
                this.logger?.log('订单状态已更新', 'success');
            }
        } catch (error) {
            this.logger?.logError('处理订单状态变化失败', error);
        }
    }



    /**
     * 分析输入内容是否为多订单（重构版本，使用检测服务模块）
     * @param {string} text - 输入文本
     * @param {Object} options - 检测选项
     */
    async analyzeInputForMultiOrder(text, options = {}) {
        const { forceDetection = false } = options;

        // 基本验证（强制检测时跳过长度限制）
        if (!forceDetection && (!text || text.trim().length < this.config.minInputLength)) {
            this.renderer.hideMultiOrderPanel();
            return;
        }

        this.logger?.log(`🔍 开始分析输入内容，长度: ${text.length}字符`, 'info');

        try {
            // 🏗️ 使用检测服务模块进行多订单检测
            const detectionResult = await this.detector.detectMultiOrder(text, {
                forceDetection,
                source: 'manual'
            });

            this.logger?.log(`🔍 检测结果: ${detectionResult.isMultiOrder ? '多订单' : '单订单'}`, 'info', {
                orderCount: detectionResult.orderCount,
                confidence: detectionResult.confidence
            });

            if (detectionResult.isMultiOrder && detectionResult.orders && detectionResult.orders.length > 1) {
                this.logger?.log(`✅ 检测到多订单: ${detectionResult.orders.length}个完整订单`, 'success');

                // 🏗️ 使用UI渲染器模块显示多订单面板
                this.logger?.log('📋 准备显示多订单面板...', 'info');
                this.renderer.showMultiOrderPanel(detectionResult.orders);
                this.logger?.log('✅ 多订单面板显示完成', 'success');
            } else {
                this.logger?.log('📋 检测为单订单，隐藏多订单面板', 'info', {
                    reason: !detectionResult.isMultiOrder ? '非多订单' :
                           !detectionResult.orders ? '缺少orders数组' :
                           detectionResult.orders.length <= 1 ? '订单数量不足' : '未知原因'
                });
                this.renderer.hideMultiOrderPanel();
            }
        } catch (error) {
            this.logger?.logError('多订单分析失败', error);
            // 异常情况下隐藏面板
            this.renderer.hideMultiOrderPanel();
        }
    }

    // 🏗️ 检测方法已移动到 MultiOrderDetector 模块
    // 保留委托方法以维持向后兼容性

    /**
     * 传统多订单检测（委托给检测服务模块）
     * @param {string} text - 输入文本
     * @returns {object} 检测结果
     */
    detectMultiOrderTraditional(text) {
        if (this.detector && typeof this.detector.detectMultiOrderTraditional === 'function') {
            return this.detector.detectMultiOrderTraditional(text);
        }

        // 降级方案
        return { isMultiOrder: false, confidence: 0, reason: '检测服务不可用' };
    }

    /**
     * AI多订单检测（委托给检测服务模块）
     * @param {string} text - 输入文本
     * @returns {Promise<object>} AI检测结果
     */
    async detectMultiOrderAI(text) {
        if (this.detector && typeof this.detector.detectMultiOrderAI === 'function') {
            return await this.detector.detectMultiOrderAI(text);
        }

        // 降级方案
        return {
            isMultiOrder: false,
            confidence: 0,
            orderCount: 1,
            reason: '检测服务不可用'
        };
    }
    /**
     * 智能分割订单文本（委托给检测服务模块）
     * @param {string} text - 输入文本
     * @returns {Promise<Array>} 分割后的订单对象数组
     */
    async smartSplitOrderText(text) {
        if (this.detector && typeof this.detector.smartSplitOrderText === 'function') {
            const result = await this.detector.smartSplitOrderText(text);
            // 更新当前分段状态
            this.state.currentSegments = result || [text];
            return result;
        }

        // 降级方案
        return [text || ''];
    }

    // 🏗️ UI方法已移动到 MultiOrderRenderer 模块
    // 保留委托方法以维持向后兼容性

    /**
     * 显示多订单面板（委托给状态管理器和UI管理器）
     * @param {Array} orders - 完整解析的订单对象数组
     */
    showMultiOrderPanel(orders) {
        // 🏗️ 委托给状态管理器设置多订单模式
        this.stateManager.setMultiOrderMode(true, this.stateManager.getStateValue('currentSegments') || [], orders);

        // 🏗️ 委托给UI管理器显示面板
        this.uiManager.showMultiOrderPanel(orders);
    }

    /**
     * 隐藏多订单面板（委托给UI管理器）
     */
    hideMultiOrderPanel() {
        // 🏗️ 委托给UI管理器
        this.uiManager.hideMultiOrderPanel();
    }

    /**
     * 更新面板内容（显示完整解析的订单对象）- 增强版，包含批量控制面板和Paging服务检测
     * @param {Array} orders - 完整解析的订单对象数组
     */
    updatePanelContent(orders) {
        console.log('🔧 updatePanelContent开始执行', { orders: orders, ordersLength: orders?.length });
        
        const orderList = document.querySelector('#multiOrderPanel .multi-order-list');
        console.log('🔍 orderList容器检查:', {
            orderList: orderList,
            exists: !!orderList,
            className: orderList?.className
        });
        
        if (!orderList) {
            console.error('❌ 多订单列表容器不存在');
            getLogger()?.log('多订单列表容器不存在', 'warn');
            return;
        }

        try {
            console.log('🔧 开始处理Paging服务...');
            // 应用Paging服务自动识别和处理
            const processedOrders = this.processPagingServiceForOrders(orders);
            console.log('✅ Paging服务处理完成', { processedOrdersLength: processedOrders?.length });

            // 🔍 分析数据流转过程
            this.analyzeDataFlowTransformation(orders, processedOrders);

            // 🔧 应用一致性数据处理（新增）
            console.log('🔧 开始应用一致性数据处理...');
            const finalProcessedOrders = this.applyConsistentDataProcessing(processedOrders);
            console.log('✅ 一致性数据处理完成', { finalOrdersLength: finalProcessedOrders?.length });

            // 🔍 生成综合问题排查报告（使用最终处理后的数据）
            const diagnosticReport = this.generateFieldDisplayDiagnosticReport(orders, finalProcessedOrders);

            // 如果发现严重问题，在控制台显示警告
            if (diagnosticReport.summary.criticalIssues > 0) {
                console.warn(`⚠️ 发现 ${diagnosticReport.summary.criticalIssues} 个严重的字段显示问题`);
                console.warn('💡 建议查看控制台中的详细排查报告');
            } else {
                console.log('✅ 字段显示问题检查通过，未发现严重问题');
            }

            // 更新状态中的订单数据（使用最终处理后的数据）
            this.state.parsedOrders = finalProcessedOrders;
            console.log('🔧 开始生成订单项HTML...');
            
            // 生成订单项HTML - 显示结构化字段（使用最终处理后的数据）
            const orderItemsHTML = finalProcessedOrders.map((order, index) => {
            const orderId = `order-${index}`;
            
            // 生成订单摘要，传递正确的索引
            const summary = this.generateOrderSummary(order, index);
            
            // 检查是否为Paging订单
            const isPagingOrder = order.is_paging_order || order.carTypeId === 34;
            const pagingBadge = isPagingOrder ? '<span class="paging-badge">🏷️ 举牌</span>' : '';
            
            return `
                <div class="order-card ${isPagingOrder ? 'paging-order' : ''}" data-order-index="${index}" 
                     onclick="window.OTA.multiOrderManager.toggleOrderSelection(${index})" 
                     style="cursor: pointer;">
                    <div class="order-card-header">
                        <div class="order-selector">
                            <input type="checkbox" id="${orderId}" checked class="order-checkbox">
                            <div class="order-title">
                                <span class="order-number">订单 ${index + 1}</span>
                                ${pagingBadge}
                            </div>
                        </div>
                        <div class="order-status">
                            <span class="status-badge status-parsed">已解析</span>
                        </div>
                    </div>
                    <div class="order-card-body" onclick="window.OTA.multiOrderManager.quickEditOrder(${index}); event.stopPropagation();">
                        ${summary}
                    </div>
                </div>
            `;
            }).join('');
            console.log('✅ 订单项HTML生成完成', { orderItemsCount: processedOrders.length });

            
            console.log('🔧 更新orderList.innerHTML...');
            orderList.innerHTML = orderItemsHTML;
            console.log('✅ orderList.innerHTML更新完成');

            // 绑定选择框事件
            console.log('🔧 绑定选择框事件...');
            const checkboxes = orderList.querySelectorAll('.order-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', (e) => {
                    e.stopPropagation(); // 防止触发卡片点击
                    const card = checkbox.closest('.order-card');
                    if (card) {
                        card.classList.toggle('selected', checkbox.checked);
                    }
                    this.updateSelectedCount();
                });
            });
            console.log('✅ 选择框事件绑定完成');


            // 更新统计信息
            console.log('🔧 更新统计信息...');
            this.updateOrderStats(processedOrders.length);
            console.log('✅ 统计信息更新完成');
            
            // 显示Paging服务统计信息
            console.log('🔧 更新Paging服务统计信息...');
            this.updatePagingServiceStats(processedOrders);
            console.log('✅ Paging服务统计信息更新完成');

            // 集成学习系统UI更正功能
            console.log('🔧 集成学习系统UI...');
            this.integrateLearningSystemUI(processedOrders);
            console.log('✅ 学习系统UI集成完成');
            
            // 初始化选择计数
            this.updateSelectedCount();
            
            console.log('🎉 updatePanelContent执行完全成功');
            
        } catch (error) {
            console.error('❌ updatePanelContent执行过程中出错:', error);
            this.logger?.logError('updatePanelContent执行失败', error);
            throw error;
        }
    }

    /**
     * 处理订单的Paging服务识别和配置
     * @param {Array} orders - 原始订单数组
     * @returns {Array} 处理后的订单数组（可能包含新生成的Paging订单）
     */
    processPagingServiceForOrders(orders) {
        
        // 获取Paging服务管理器
        const pagingServiceManager = window.getPagingServiceManager ? window.getPagingServiceManager() : null;
        if (!pagingServiceManager) {
            this.logger?.log('⚠️ Paging服务管理器不可用，跳过Paging服务处理', 'warn');
            return this.processChineseLanguageDetection(orders);
        }

        try {
            // 检测所有订单的原始文本中是否包含Paging关键词
            const allOrdersText = orders.map(order => order.rawText || '').join(' ');
            const needsPagingService = pagingServiceManager.detectPagingService(allOrdersText);
            
            if (!needsPagingService) {
                this.logger?.log('📋 未检测到Paging服务关键词', 'info');
                return this.processChineseLanguageDetection(orders);
            }

            this.logger?.log('🏷️ 检测到Paging服务关键词，开始处理...', 'info');

            const processedOrders = [];
            let pagingOrdersGenerated = 0;

            orders.forEach(order => {
                // 标记原订单需要Paging服务
                const updatedOrder = {
                    ...order,
                    needsPagingService: true,
                    meetAndGreet: true
                };
                processedOrders.push(updatedOrder);

                // 为接机订单生成独立的Paging订单
                if (order.subCategoryId === 2 || order.sub_category_id === 2) {
                    try {
                        const pagingOrder = pagingServiceManager.generatePagingOrder(order);
                        
                        // 转换为前端格式
                        const frontendPagingOrder = this.convertPagingOrderToFrontendFormat(pagingOrder);
                        processedOrders.push(frontendPagingOrder);
                        pagingOrdersGenerated++;
                        
                        this.logger?.log(`✅ 为订单 "${order.customerName || 'Unknown'}" 生成Paging订单`, 'success');
                    } catch (error) {
                        this.logger?.logError(`生成Paging订单失败`, error);
                    }
                }
            });

            if (pagingOrdersGenerated > 0) {
                this.logger?.log(`🎯 Paging服务处理完成，共生成 ${pagingOrdersGenerated} 个Paging订单`, 'success');
            }

            return this.processChineseLanguageDetection(processedOrders);

        } catch (error) {
            this.logger?.logError('Paging服务处理失败', error);
            return this.processChineseLanguageDetection(orders);
        }
    }

    /**
     * 将Paging订单转换为前端格式
     * @param {Object} pagingOrder - 后端格式的Paging订单
     * @returns {Object} 前端格式的Paging订单
     */
    convertPagingOrderToFrontendFormat(pagingOrder) {
        return {
            // 基础字段
            rawText: `举牌服务 - ${pagingOrder.customer_name}`,
            customerName: pagingOrder.customer_name,
            customerContact: pagingOrder.customer_contact,
            customerEmail: pagingOrder.customer_email,
            pickup: pagingOrder.pickup,
            dropoff: pagingOrder.dropoff || '举牌服务点',
            pickupDate: pagingOrder.pickup_date,
            pickupTime: pagingOrder.pickup_time,
            
            // 服务配置 - 使用camelCase格式以匹配前端
            passengerCount: pagingOrder.passenger_count || 0,
            luggageCount: pagingOrder.luggage_count || 0,
            flightInfo: pagingOrder.flight_info,
            otaReferenceNumber: pagingOrder.ota_reference_number,
            otaPrice: pagingOrder.price || 0,
            currency: pagingOrder.currency || 'MYR',
            
            // ID字段
            carTypeId: pagingOrder.car_type_id || 34, // Ticket
            subCategoryId: pagingOrder.sub_category_id || 2, // 接机
            drivingRegionId: pagingOrder.driving_region_id || 9, // Paging
            languagesIdArray: pagingOrder.languages_id_array || [5], // Paging
            
            // 特殊需求
            extraRequirement: pagingOrder.extra_requirement,
            babyChair: false,
            tourGuide: false,
            meetAndGreet: true,
            needsPagingService: true,
            
            // 标识字段
            isPagingOrder: true,
            relatedMainOrder: pagingOrder.related_main_order,
            
            // 时间信息
            arrivalTime: pagingOrder.arrival_time,
            departureTime: pagingOrder.departure_time,
            flightType: pagingOrder.flight_type
        };
    }

    /**
     * 更新Paging服务统计信息显示
     * @param {Array} orders - 订单数组
     */
    updatePagingServiceStats(orders) {
        const pagingServiceManager = window.getPagingServiceManager ? window.getPagingServiceManager() : null;
        
        if (!pagingServiceManager) return;

        try {
            const stats = pagingServiceManager.getPagingServiceStats(orders);
            const summary = pagingServiceManager.createPagingServiceSummary(stats);
            
            // 在多订单面板中显示Paging服务统计
            const statsContainer = document.querySelector('.order-stats');
            if (statsContainer && stats.hasPagingService) {
                const pagingStats = document.createElement('span');
                pagingStats.className = 'paging-stats';
                pagingStats.innerHTML = `🏷️ ${summary}`;
                statsContainer.appendChild(pagingStats);
            }
            
            this.logger?.log(`📊 Paging服务统计: ${summary}`, 'info');
        } catch (error) {
            this.logger?.logError('更新Paging服务统计失败', error);
        }
    }

    /**
     * 处理中文语言自动检测
     * 使用统一的中文检测器确保与单订单模组逻辑同步
     * @param {Array} orders - 订单数组
     * @returns {Array} 处理后的订单数组
     */
    processChineseLanguageDetection(orders) {
        try {
            // 使用统一的扩展中文检测正则表达式
            const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;
            let chineseCount = 0;

            const processedOrders = orders.map(order => {
                const text = `${order.customer_name || ''} ${order.pickup_location || ''} ${order.dropoff_location || ''} ${order.extra_requirement || ''}`;

                // 简化的语言选择策略：单一语言选择
                const hasChinese = chineseRegex.test(text);
                if (hasChinese) chineseCount++;

                order.languages_id_array = hasChinese ? [4] : [2]; // 中文[4] 或 英文[2]
                return order;
            });

            // 简化的统计信息记录
            const total = orders.length;
            const englishCount = total - chineseCount;
            this.logger?.log(
                `📊 语言检测: ${total}个订单 - 中文${chineseCount}个(${Math.round(chineseCount/total*100)}%), 英文${englishCount}个(${Math.round(englishCount/total*100)}%)`,
                'info'
            );

            return processedOrders;

        } catch (error) {
            this.logger?.logError('语言自动检测失败', error);
            return orders;
        }
    }



    /**
     * 全选所有订单
     */
    selectAllOrders() {
        const checkboxes = document.querySelectorAll('.order-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
            const card = checkbox.closest('.order-card');
            if (card) {
                card.classList.add('selected');
            }
        });
        this.updateSelectedCount();
    }

    /**
     * 清除所有选择
     */
    clearAllSelections() {
        this.cleanupManager.clearAllSelections(this.state);
    }


    /**
     * 切换语言下拉菜单
     */
    toggleLanguageDropdown() {
        const content = document.getElementById('languageDropdownContent');
        const arrow = document.querySelector('#batchLanguageDropdown .dropdown-arrow');
        if (content) {
            const isVisible = content.style.display === 'block';
            content.style.display = isVisible ? 'none' : 'block';
            if (arrow) {
                arrow.textContent = isVisible ? '▼' : '▲';
            }
        }
    }

    /**
     * 更新语言选择显示
     */
    updateLanguageSelection() {
        const checkboxes = document.querySelectorAll('#languageDropdownContent input[type="checkbox"]:checked');
        const selectedText = document.getElementById('languageSelectedText');
        
        if (selectedText) {
            if (checkboxes.length === 0) {
                selectedText.textContent = '选择语言...';
            } else if (checkboxes.length === 1) {
                const label = checkboxes[0].closest('label').querySelector('.language-name').textContent;
                selectedText.textContent = label;
            } else {
                selectedText.textContent = `已选择 ${checkboxes.length} 种语言`;
            }
        }
    }






    /**
     * 筛选订单
     * @param {string} filterValue - 筛选值 (all, completed, pending, warning, error)
     */
    filterOrders(filterValue) {
        const orderCards = document.querySelectorAll('.order-card');
        
        orderCards.forEach(card => {
            const statusIcon = card.querySelector('.status-icon');
            let shouldShow = true;
            
            if (filterValue !== 'all' && statusIcon) {
                const hasStatus = statusIcon.classList.contains(`status-${filterValue}`);
                shouldShow = hasStatus;
            }
            
            card.style.display = shouldShow ? 'block' : 'none';
        });
        
        this.logger?.log(`筛选订单: ${filterValue}`, 'info');
    }

    /**
     * 排序订单
     * @param {string} sortValue - 排序值 (created-desc, created-asc, amount-desc, amount-asc, name-asc)
     */
    sortOrders(sortValue) {
        const orderContainer = document.getElementById('multiOrderList');
        if (!orderContainer) return;
        
        const orderCards = Array.from(orderContainer.querySelectorAll('.order-card'));
        
        orderCards.sort((a, b) => {
            const indexA = parseInt(a.dataset.orderIndex);
            const indexB = parseInt(b.dataset.orderIndex);
            const orderA = this.state.parsedOrders[indexA];
            const orderB = this.state.parsedOrders[indexB];
            
            switch (sortValue) {
                case 'created-asc':
                    return indexA - indexB;
                case 'created-desc':
                    return indexB - indexA;
                case 'amount-desc':
                    const priceA = parseFloat(orderA?.otaPrice || 0);
                    const priceB = parseFloat(orderB?.otaPrice || 0);
                    return priceB - priceA;
                case 'amount-asc':
                    const priceA2 = parseFloat(orderA?.otaPrice || 0);
                    const priceB2 = parseFloat(orderB?.otaPrice || 0);
                    return priceA2 - priceB2;
                case 'name-asc':
                    const nameA = orderA?.customerName || '';
                    const nameB = orderB?.customerName || '';
                    return nameA.localeCompare(nameB);
                default:
                    return 0;
            }
        });
        
        // 重新排列DOM元素
        orderCards.forEach(card => orderContainer.appendChild(card));
        
        this.logger?.log(`排序订单: ${sortValue}`, 'info');
    }

    /**
     * 创建单个订单
     * @param {number} index - 订单索引
     */
    async createSingleOrder(index) {
        if (!this.state.parsedOrders[index]) {
            this.logger?.logError('订单不存在', new Error(`订单索引 ${index} 不存在`));
            return;
        }
        
        try {
            // 获取API服务
            const apiService = getService('apiService') || window.getAPIService?.();
            if (!apiService) {
                throw new Error('API服务不可用');
            }
            
            // 创建订单
            const order = this.state.parsedOrders[index];
            const result = await apiService.createOrder(order);
            
            if (result.success) {
                this.logger?.log(`订单 ${index + 1} 创建成功`, 'success');
                
                // 更新UI状态
                const card = document.querySelector(`.order-card[data-order-index="${index}"]`);
                if (card) {
                    const statusIcon = card.querySelector('.status-icon');
                    if (statusIcon) {
                        statusIcon.innerHTML = '✅已创建';
                        statusIcon.className = 'status-icon status-complete';
                    }
                }
                
                // 显示成功消息
                const uiManager = window.OTA.getService('uiManager');
                uiManager?.showSuccess(`订单 ${index + 1} 创建成功！`);
            }
        } catch (error) {
            this.logger?.logError(`创建订单 ${index + 1} 失败`, error);
            
            // 显示错误消息
            const uiManager = window.OTA.getService('uiManager');
            uiManager?.showError(`创建订单失败: ${error.message}`);
        }
    }

    /**
     * 编辑指定订单
     * @param {number} index - 订单索引
     */
    editOrder(index) {
        this.logger?.log(`编辑订单 ${index + 1}`, 'info');

        try {
            if (index < 0 || index >= this.state.currentSegments.length) {
                throw new Error(`订单索引 ${index} 超出范围`);
            }

            const orderInput = document.getElementById('orderInput');
            if (!orderInput) {
                throw new Error('订单输入框不存在');
            }

            // 将选中的订单片段填充到主输入框
            orderInput.value = this.state.currentSegments[index].trim();
            
            // 隐藏多订单面板
            this.hideMultiOrderPanel();
            
            // 显示返回多订单模式按钮
            const returnBtn = document.getElementById('returnToMultiOrder');
            if (returnBtn) {
                returnBtn.classList.remove('hidden');
                this.logger?.log('显示返回多订单模式按钮', 'info');
            }
            
            // 滚动到输入框并聚焦
            orderInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
            orderInput.focus();
            
            // 触发输入事件以启动实时分析（🔧 修复：添加标记防止递归）
            const event = new Event('input', { bubbles: true });
            event._programmaticTrigger = true;
            orderInput.dispatchEvent(event);

            this.logger?.log(`订单 ${index + 1} 已载入编辑器`, 'success');

        } catch (error) {
            this.logger?.logError(`编辑订单失败: ${error.message}`, error);
            const uiManager = window.OTA.getService('uiManager');
            uiManager?.showError(`编辑订单失败: ${error.message}`);
        }
    }

    /**
     * 🔧 处理指定订单（AI解析 - 增强异步错误处理链）
     * @param {number} index - 订单索引
     */
    async processOrder(index) {
        this.logger?.log(`开始处理订单 ${index + 1}`, 'info');

        try {
            if (index < 0 || index >= this.state.currentSegments.length) {
                throw new Error(`订单索引 ${index} 超出范围`);
            }

            const orderText = this.state.currentSegments[index].trim();
            if (!orderText) {
                throw new Error(`订单 ${index + 1} 文本为空`);
            }
            
            // 更新UI状态
            this.updateOrderProcessingStatus(index, 'processing');

            // 调用Gemini AI分析（增加重试机制）
            const geminiService = this.getService('gemini');
            if (!geminiService || !geminiService.isAvailable()) {
                throw new Error('Gemini AI服务不可用');
            }

            let result = null;
            let retryCount = 0;
            const maxRetries = 3;
            
            while (retryCount < maxRetries) {
                try {
                    result = await geminiService.parseOrder(orderText);
                    break; // 成功则退出重试循环
                } catch (apiError) {
                    retryCount++;
                    this.logger?.logError(`订单 ${index + 1} API调用失败（尝试 ${retryCount}/${maxRetries}）`, apiError);
                    
                    if (retryCount >= maxRetries) {
                        throw new Error(`API调用失败，已重试 ${maxRetries} 次: ${apiError.message}`);
                    }
                    
                    // 指数退避延迟
                    const delay = Math.pow(2, retryCount) * 1000; // 2, 4, 8秒
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
            
            if (!result) {
                throw new Error('无法获取解析结果');
            }
            
            // 验证结果完整性
            if (!result.success) {
                throw new Error(result.message || '订单解析失败');
            }
            
            if (!result.data) {
                throw new Error('解析结果缺少数据');
            }
            
            // 验证关键字段
            const requiredFields = ['customer_name', 'pickup_location', 'pickup_date'];
            const missingFields = requiredFields.filter(field => !result.data[field]);
            
            if (missingFields.length > 0) {
                this.logger?.logError(`订单 ${index + 1} 缺少关键字段`, { missingFields });
                // 不中断处理，而是标记为警告状态
            }

            // 应用备用字段映射
            this.applyAlternativeFieldMapping(result.data);
            const standardizedData = result.data;
            
            // 存储解析结果
            this.state.processedOrders.set(index, {
                rawText: orderText,
                parsedData: standardizedData,
                confidence: result.confidence || 0,
                timestamp: Date.now(),
                retryCount: retryCount,
                status: 'success'
            });

            // 更新应用状态
            const appState = this.getService('appState');
            if (appState) {
                try {
                    appState.setCurrentOrder({
                        rawText: orderText,
                        parsedData: standardizedData,
                        confidence: result.confidence || 0,
                        source: 'multi-order-single',
                        orderIndex: index,
                        retryCount: retryCount
                    });
                } catch (stateError) {
                    this.logger?.logError(`更新应用状态失败: ${stateError.message}`, stateError);
                }
            }

            // 更新UI
            this.updateOrderProcessingStatus(index, 'success');
            this.showOrderDetails(index, standardizedData);

            this.logger?.log(`订单 ${index + 1} 处理完成`, 'success', {
                confidence: result.confidence,
                retryCount: retryCount,
                dataKeys: Object.keys(standardizedData || {}),
                missingFields: missingFields || []
            });

        } catch (error) {
            this.logger?.logError(`处理订单 ${index + 1} 失败: ${error.message}`, error);
            
            // 增强错误处理：分类错误类型
            const errorType = this.classifyErrorType(error);
            
            // 存储失败状态
            this.state.processedOrders.set(index, {
                rawText: this.state.currentSegments[index]?.trim() || '',
                parsedData: null,
                confidence: 0,
                timestamp: Date.now(),
                status: 'error',
                error: {
                    message: error.message,
                    type: errorType,
                    stack: error.stack
                }
            });
            
            this.updateOrderProcessingStatus(index, 'error', error.message);
            
            // 只在严重错误时显示用户提示，避免过度打扰
            if (errorType !== 'validation') {
                const uiManager = window.OTA.getService('uiManager');
                uiManager?.showError(`处理订单 ${index + 1} 失败: ${error.message}`);
            }
            
            // 记录详细错误信息
            this.logErrorDetails(error, index);
        }
    }

    /**
     * 🔧 分类错误类型
     * @param {Error} error - 错误对象
     * @returns {string} 错误类型
     */
    classifyErrorType(error) {
        const message = error.message.toLowerCase();
        
        if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {
            return 'network';
        } else if (message.includes('api') || message.includes('service')) {
            return 'api';
        } else if (message.includes('validation') || message.includes('required') || message.includes('invalid')) {
            return 'validation';
        } else if (message.includes('range') || message.includes('index')) {
            return 'range';
        } else {
            return 'unknown';
        }
    }

    /**
     * 预览订单详情
     * @param {number} index - 订单索引
     */
    previewOrder(index) {
        const processedOrder = this.state.processedOrders.get(index);
        if (!processedOrder) {
            const uiManager = window.OTA.getService('uiManager');
            uiManager?.showError('订单未解析，请先点击解析按钮');
            return;
        }

        const detailsElement = document.querySelector(`.order-item[data-order-index="${index}"] .order-details`);
        if (detailsElement) {
            detailsElement.style.display = detailsElement.style.display === 'none' ? 'block' : 'none';
        }
    }

    /**
     * 更新订单处理状态
     * @param {number} index - 订单索引
     * @param {string} status - 状态：processing, success, error
     * @param {string} message - 额外信息
     */
    updateOrderProcessingStatus(index, status, message = '') {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const statusBadge = orderItem.querySelector('.status-badge');
        const processBtn = orderItem.querySelector('.process-order-btn');
        const previewBtn = orderItem.querySelector('.preview-order-btn');

        if (statusBadge) {
            statusBadge.className = 'status-badge';
            switch (status) {
                case 'processing':
                    statusBadge.classList.add('status-processing');
                    statusBadge.textContent = '解析中...';
                    break;
                case 'success':
                    statusBadge.classList.add('status-success');
                    statusBadge.textContent = '已解析';
                    break;
                case 'error':
                    statusBadge.classList.add('status-error');
                    statusBadge.textContent = '解析失败';
                    break;
                default:
                    statusBadge.classList.add('status-pending');
                    statusBadge.textContent = '待处理';
            }
        }

        if (processBtn) {
            switch (status) {
                case 'processing':
                    processBtn.disabled = true;
                    processBtn.textContent = '🔄 解析中...';
                    break;
                case 'success':
                    processBtn.disabled = false;
                    processBtn.textContent = '✅ 重新解析';
                    processBtn.classList.remove('btn-primary');
                    processBtn.classList.add('btn-outline');
                    break;
                case 'error':
                    processBtn.disabled = false;
                    processBtn.textContent = '🔄 重试';
                    processBtn.classList.remove('btn-primary');
                    processBtn.classList.add('btn-warning');
                    break;
                default:
                    processBtn.disabled = false;
                    processBtn.textContent = '🤖 解析';
            }
        }

        if (previewBtn && status === 'success') {
            previewBtn.style.display = 'inline-block';
        }

        if (message && status === 'error') {
            const orderContent = orderItem.querySelector('.order-content');
            if (orderContent) {
                const errorDiv = orderContent.querySelector('.error-message') || document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.innerHTML = `<small style="color: #dc3545;">错误: ${message}</small>`;
                if (!orderContent.contains(errorDiv)) {
                    orderContent.appendChild(errorDiv);
                }
            }
        }
    }

    /**
     * 显示订单详情
     * @param {number} index - 订单索引
     * @param {object} orderData - 解析后的订单数据
     */
    showOrderDetails(index, orderData) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const detailsElement = orderItem.querySelector('.order-details');
        if (!detailsElement) return;

        // 生成详情HTML
        const detailsHTML = `
            <div class="order-fields">
                <div class="field-group">
                    <div class="field">
                        <label>客户姓名:</label>
                        <span class="editable-field" data-field="customer_name">${orderData.customer_name || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>联系电话:</label>
                        <span class="editable-field" data-field="customer_contact">${orderData.customer_contact || 'N/A'}</span>
                    </div>
                </div>
                <div class="field-group">
                    <div class="field">
                        <label>上车地点:</label>
                        <span class="editable-field" data-field="pickup">${orderData.pickup || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>下车地点:</label>
                        <span class="editable-field" data-field="dropoff">${orderData.dropoff || 'N/A'}</span>
                    </div>
                </div>
                <div class="field-group">
                    <div class="field">
                        <label>日期:</label>
                        <span class="editable-field" data-field="pickup_date">${orderData.pickup_date || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>时间:</label>
                        <span class="editable-field" data-field="pickup_time">${orderData.pickup_time || 'N/A'}</span>
                    </div>
                </div>
                <div class="field-group">
                    <div class="field">
                        <label>乘客数:</label>
                        <span class="editable-field" data-field="passenger_count">${orderData.passenger_count || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>服务类型:</label>
                        <span>${this.utils.getServiceTypeName(orderData.sub_category_id)}</span>
                    </div>
                </div>
                ${orderData.flight_info ? `
                <div class="field-group">
                    <div class="field">
                        <label>航班号:</label>
                        <span>${orderData.flight_info}</span>
                    </div>
                    <div class="field">
                        <label>航班时间:</label>
                        <span>${orderData.arrival_time || orderData.departure_time || 'N/A'}</span>
                    </div>
                </div>
                ` : ''}
            </div>
            <div class="order-actions-inline">
                <button type="button" class="btn btn-sm btn-outline" onclick="window.OTA.multiOrderManager.editOrderFields(${index})">
                    ✏️ 编辑字段
                </button>
                <button type="button" class="btn btn-sm btn-success" onclick="window.OTA.multiOrderManager.createSingleOrder(${index})">
                    🚀 创建此订单
                </button>
            </div>
        `;

        detailsElement.innerHTML = detailsHTML;
    }

    /**
     * Helper methods for compact layout design
     */
    getOrderStatusIcon(order) {
        const hasRequired = order.customerName && order.customerContact && order.pickup && order.pickupDate;
        if (hasRequired) {
            return '<span class="status-icon status-complete">✅就绪</span>';
        } else {
            return '<span class="status-icon status-error">❌缺信息</span>';
        }
    }

    formatRoute(order, index = 0) {
        const pickup = order.pickup || order.pickupLocation || '未指定';
        const dropoff = order.dropoff || order.dropoffLocation || '未指定';
        // 简化版地址编辑：添加editable类和基础点击事件
        return `
            <div class="route-display">
                <div class="pickup-address editable" onclick="window.OTA.multiOrderManager.editAddress(${index}, 'pickup', event)">
                    <span class="address-label">上车:</span>
                    <span class="address-text">${pickup}</span>
                </div>
                <div class="dropoff-address editable" onclick="window.OTA.multiOrderManager.editAddress(${index}, 'dropoff', event)">
                    <span class="address-label">下车:</span>
                    <span class="address-text">${dropoff}</span>
                </div>
            </div>
        `;
    }

    getVehicleType(order) {
        const typeMap = {
            1: '舒适5座',
            2: '豪华轿车',
            3: 'MPV7座',
            4: '豪华车'
        };
        return typeMap[order.carTypeId] || '舒适5座';
    }

    /**
     * 简化版地址编辑功能
     * @param {number} index - 订单索引
     * @param {string} type - 地址类型 ('pickup' 或 'dropoff')
     * @param {Event} event - 点击事件
     */
    editAddress(index, type, event) {
        event.stopPropagation();

        const addressElement = event.currentTarget;
        const textElement = addressElement.querySelector('.address-text');
        const currentValue = textElement.textContent;

        // 简单的prompt编辑
        const newValue = prompt(`编辑${type === 'pickup' ? '上车' : '下车'}地址:`, currentValue);

        if (newValue && newValue !== currentValue) {
            // 更新显示
            textElement.textContent = newValue;

            // 更新数据
            if (this.state.parsedOrders[index]) {
                if (type === 'pickup') {
                    this.state.parsedOrders[index].pickup = newValue;
                } else {
                    this.state.parsedOrders[index].dropoff = newValue;
                }
            }

            this.logger?.log(`地址已更新: 订单${index + 1} ${type} = "${newValue}"`, 'info');
        }
    }











    /**
     * 初始化批量操作功能（简化版）
     */
    initBatchControls() {
        const applyBtn = document.getElementById('applyBatchBtn');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => this.applyBatchSettings());
        }
    }

    /**
     * 应用批量设置（委托给批量操作管理器）
     */
    applyBatchSettings() {
        const languageSelect = document.getElementById('batchLanguageSelect');
        const otaSelect = document.getElementById('batchOtaSelect');
        const carTypeSelect = document.getElementById('batchCarTypeSelect');
        const backendUserSelect = document.getElementById('batchBackendUserSelect');

        // 收集要应用的设置
        const settings = {};
        if (languageSelect?.value) {
            settings.language = languageSelect.value;
        }
        if (otaSelect?.value) {
            settings.ota = otaSelect.value;
        }
        if (carTypeSelect?.value) {
            settings.carType = carTypeSelect.value;
        }
        if (backendUserSelect?.value) {
            settings.backendUser = backendUserSelect.value;
        }

        if (Object.keys(settings).length === 0) {
            alert('请选择要批量设置的项目');
            return;
        }

        // 🏗️ 获取选中的订单（委托给状态管理器）
        const selectedOrders = this.stateManager.getSelectedOrders();
        if (selectedOrders.length === 0) {
            alert('请先选择要批量设置的订单');
            return;
        }

        // 🏗️ 委托给批量操作管理器处理
        const result = this.batchManager.batchApplySettings(selectedOrders, settings);

        if (result.success) {
            this.logger?.log(`批量设置应用成功，更新了 ${result.updatedCount} 个字段`, 'success');
            alert(`批量设置应用成功，更新了 ${result.updatedCount} 个字段`);

            // 🏗️ 委托给UI管理器更新显示
            this.uiManager.updatePanelContent(this.stateManager.getState().parsedOrders);
        } else {
            this.logger?.logError('批量设置应用失败', result.errors);
            alert(`批量设置应用失败: ${result.errors.length} 个错误`);
        }
    }







    /**
     * 获取区域名称
     * @param {number} drivingRegionId - 区域ID
     * @returns {string} 区域名称
     */
    getRegionName(drivingRegionId) {
        // 🏗️ 使用统一的服务获取方法
        const apiService = this.getService('api');
        if (apiService && apiService.staticData && apiService.staticData.drivingRegions) {
            const region = apiService.staticData.drivingRegions.find(r => r.id === drivingRegionId);
            if (region) {
                // 简化显示，去掉括号内容
                const name = region.name;
                const simplified = name.split('(')[0].trim();
                return simplified;
            }
        }
        
        // 备用映射
        const regions = {
            1: '吉隆坡',
            2: '槟城',
            3: '新山',
            4: '沙巴',
            5: '新加坡',
            9: '举牌服务',
            12: '马六甲'
        };
        return regions[drivingRegionId] || '未知区域';
    }



    /**
     * 获取特殊需求组合
     * @param {Object} order - 订单对象
     * @returns {string} 特殊需求字符串
     */
    getSpecialRequirements(order) {
        const requirements = [];
        
        if (order.babyChair) requirements.push('儿童座椅');
        if (order.tourGuide) requirements.push('导游服务');
        if (order.meetAndGreet || order.meet_and_greet) requirements.push('迎接服务');
        if (order.extraRequirement || order.extra_requirement) {
            const extra = order.extraRequirement || order.extra_requirement;
            if (extra.trim() !== '') {
                requirements.push(extra.substring(0, 20) + (extra.length > 20 ? '...' : ''));
            }
        }
        
        return requirements.length > 0 ? requirements.join(', ') : '无';
    }

    /**
     * 编辑订单字段（内联编辑）
     * @param {number} index - 订单索引
     */
    editOrderFields(index) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const editableFields = orderItem.querySelectorAll('.editable-field');
        editableFields.forEach(field => {
            const currentValue = field.textContent.trim();
            const fieldName = field.getAttribute('data-field');
            
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentValue === 'N/A' ? '' : currentValue;
            input.className = 'field-input';
            input.setAttribute('data-field', fieldName);
            
            input.addEventListener('blur', () => {
                this.saveFieldEdit(index, fieldName, input.value);
                field.textContent = input.value || 'N/A';
                field.style.display = 'inline';
                input.remove();
            });
            
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    input.blur();
                }
            });
            
            field.style.display = 'none';
            field.parentNode.insertBefore(input, field.nextSibling);
            input.focus();
        });
    }

    /**
     * 保存字段编辑
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {string} value - 新值
     */
    saveFieldEdit(index, fieldName, value) {
        const processedOrder = this.state.processedOrders.get(index);
        if (processedOrder) {
            processedOrder.parsedData[fieldName] = value;
            this.state.processedOrders.set(index, processedOrder);
            getLogger()?.log(`订单 ${index + 1} 字段 ${fieldName} 已更新`, 'info');
        }
    }



    /**
     * 创建单个订单
     * @param {number} index - 订单索引
     */
    async createSingleOrder(index) {
        console.group(`🔍 多订单数据流追踪 - 第11步：createSingleOrder(${index})`);
        console.log('订单索引:', index);
        
        const processedOrder = this.state.processedOrders.get(index);
        console.log('processedOrder存在:', !!processedOrder);
        console.log('processedOrder数据:', processedOrder);
        
        if (!processedOrder) {
            console.error('❌ 订单未解析');
            const uiManager = window.OTA.getService('uiManager');
            uiManager?.showError('订单未解析，无法创建');
            console.groupEnd();
            return;
        }

        const apiService = this.getService('api');
        console.log('apiService存在:', !!apiService);
        
        if (!apiService) {
            console.error('❌ API服务不可用');
            const uiManager = window.OTA.getService('uiManager');
            uiManager?.showError('API服务不可用');
            console.groupEnd();
            return;
        }

        try {
            this.logger?.log(`开始创建订单 ${index + 1}`, 'info');
            
            // 🔧 修复：使用api-service的统一预处理逻辑（包含字段映射）
            console.log('开始数据预处理...');
            const preprocessedData = apiService.preprocessOrderData(processedOrder.parsedData);
            console.log('预处理后的数据:', preprocessedData);
            
            // 验证订单数据
            console.log('开始数据验证...');
            const validation = apiService.validateOrderData(preprocessedData);
            console.log('验证结果:', validation);
            
            if (!validation.isValid) {
                console.error('❌ 数据验证失败:', validation.errors);
                const uiManager = window.OTA.getService('uiManager');
                uiManager?.showValidationErrors(validation.errors);
                console.groupEnd();
                return;
            }

            console.group('🔍 多订单数据流追踪 - 第12步：调用GoMyHire API');
            console.log('即将调用apiService.createOrder...');
            console.log('发送的数据:', preprocessedData);
            const result = await apiService.createOrder(preprocessedData);
            console.log('API返回结果:', result);
            console.groupEnd();
            
            if (result.success) {
                const orderId = result.data?.id || result.data?.order_id || result.id || 'N/A';
                console.log('✅ 订单创建成功!', { orderId: orderId });
                this.logger?.log(`订单 ${index + 1} 创建成功`, 'success', { orderId });
                
                // 🔧 修复：使用与单订单相同的成功提示逻辑
                const uiManager = window.OTA?.uiManager || window.getUiManager?.();
                if (uiManager && typeof uiManager.showSimpleSuccessToast === 'function') {
                    uiManager.showSimpleSuccessToast(orderId);
                } else {
                    // 备用提示方式 - 使用简化UI
                    if (window.simpleUI) {
                        window.simpleUI.showMessage(`订单创建成功！订单ID: ${orderId}`, 'success');
                    } else {
                        alert(`订单创建成功！订单ID: ${orderId}`);
                    }
                }
                
                // 更新状态为已创建
                this.updateOrderProcessingStatus(index, 'created');
                
                // 🔧 修复：使用与单订单相同的历史记录逻辑
                try {
                    const historyManager = window.OTA?.orderHistoryManager || window.orderHistoryManager;
                    if (historyManager && typeof historyManager.addOrder === 'function') {
                        historyManager.addOrder(standardizedData, orderId, result);
                        this.logger?.log('订单已记录到历史', 'info', { orderId });
                    }
                } catch (historyError) {
                    this.logger?.logError('记录订单历史失败', historyError);
                }
            } else {
                const errorMessage = result.message || result.error || '订单创建失败';
                console.error('❌ API返回失败:', errorMessage);
                this.logger?.log(`订单 ${index + 1} 创建失败`, 'error', { error: errorMessage });
                const uiManager = window.OTA.getService('uiManager');
                uiManager?.showError(`订单创建失败: ${errorMessage}`);
            }
        } catch (error) {
            console.error('❌ 创建订单异常:', error);
            this.logger?.logError(`创建订单 ${index + 1} 异常`, error);
            const uiManager = window.OTA.getService('uiManager');
            uiManager?.showError(`创建订单异常: ${error.message}`);
        } finally {
            console.groupEnd();
        }
    }

    /**
     * 初始化面板事件
     */
    initPanelEvents() {
        // 关闭按钮
        const closeBtn = document.getElementById('closeMultiOrderBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideMultiOrderPanel();
                getLogger()?.log('多订单面板已关闭', 'info');
            });
        }

        // 全选/取消全选
        const selectAllBtn = document.getElementById('selectAllOrdersBtn');
        const deselectAllBtn = document.getElementById('deselectAllOrdersBtn');
        
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => this.selectAllOrders(true));
        }
        
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', () => this.selectAllOrders(false));
        }

        // 验证全部
        const validateAllBtn = document.getElementById('validateAllOrdersBtn');
        if (validateAllBtn) {
            validateAllBtn.addEventListener('click', () => this.processAllOrders());
        }

        // 批量创建
        const batchCreateBtn = document.getElementById('batchCreateBtn');
        const createSelectedBtn = document.getElementById('createSelectedOrdersBtn');
        
        if (batchCreateBtn) {
            batchCreateBtn.addEventListener('click', () => this.handleBatchCreate());
        }
        
        if (createSelectedBtn) {
            createSelectedBtn.addEventListener('click', () => this.createSelectedOrders());
        }

        // 点击面板外部关闭
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (multiOrderPanel) {
            multiOrderPanel.addEventListener('click', (event) => {
                if (event.target === multiOrderPanel) {
                    this.hideMultiOrderPanel();
                }
            });
        }

        // 添加面板拖拽和最小化/最大化功能
        this.addPanelDragFeature();

        // 新增：返回主页按钮
        const backToMainBtn = document.getElementById('backToMainBtn');
        if (backToMainBtn) {
            backToMainBtn.addEventListener('click', () => {
                this.hideMultiOrderPanel();
                getLogger()?.log('返回主页', 'info');
            });
        }



        getLogger()?.log('多订单面板事件已初始化', 'info');
    }

    /**
     * 选择/取消选择所有订单
     * @param {boolean} selected - 是否选中
     */
    selectAllOrders(selected) {
        const checkboxes = document.querySelectorAll('.order-item input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selected;
        });
        this.updateSelectedCount();
        getLogger()?.log(`${selected ? '全选' : '取消全选'}订单`, 'info');
    }

    /**
     * 🔧 处理所有订单（批量解析 - 修复竞态条件和异步错误链）
     */
    async processAllOrders() {
        
        // 检查是否已在运行
        if (this.state.batchProgress.isRunning) {
            this.logger?.log('⚠️ 批量解析已在运行中，跳过重复执行', 'warn');
            return;
        }

        this.logger?.log('开始批量解析所有订单', 'info');

        const orderItems = document.querySelectorAll('.order-item');
        const total = orderItems.length;
        
        if (total === 0) {
            this.logger?.log('没有订单需要处理', 'info');
            return;
        }

        // 使用状态锁防止竞态条件
        this.state.batchProgress = {
            total,
            completed: 0,
            failed: 0,
            isRunning: true,
            startTime: Date.now(),
            processingOrder: null // 当前正在处理的订单索引
        };

        this.updateBatchProgress();

        try {
            for (let i = 0; i < total; i++) {
                // 检查是否应该停止（用户可能取消）
                if (!this.state.batchProgress.isRunning) {
                    this.logger?.log('批量解析被用户中断', 'info');
                    break;
                }

                // 设置当前处理订单
                this.state.batchProgress.processingOrder = i;
                
                try {
                    await this.processOrder(i);
                    this.state.batchProgress.completed++;
                } catch (error) {
                    this.logger?.logError(`批量解析订单 ${i + 1} 失败`, error);
                    this.state.batchProgress.failed++;
                    
                    // 添加错误处理：继续处理下一个订单而不是中断
                    this.updateOrderProcessingStatus(i, 'error', error.message);
                    
                    // 增强错误处理：记录详细错误信息
                    this.logErrorDetails(error, i);
                }
                
                this.updateBatchProgress();
                
                // 添加延迟避免API过载
                if (i < total - 1 && this.state.batchProgress.isRunning) {
                    await new Promise(resolve => setTimeout(resolve, this.config.batchDelay));
                }
            }
        } catch (unexpectedError) {
            // 捕获未预期的异常
            this.logger?.logError('批量解析出现未预期错误', unexpectedError);
            this.handleUnexpectedBatchError(unexpectedError);
        } finally {
            // 确保状态正确重置
            this.state.batchProgress.isRunning = false;
            this.state.batchProgress.processingOrder = null;
            this.updateBatchProgress();
            
            const duration = Date.now() - this.state.batchProgress.startTime;
            this.logger?.log(`批量解析完成，成功: ${this.state.batchProgress.completed}, 失败: ${this.state.batchProgress.failed}, 耗时: ${duration}ms`, 'success');
            
            // 显示批量处理结果摘要
            this.showBatchProcessingSummary();
        }
    }

    /**
     * 🔧 记录详细错误信息
     * @param {Error} error - 错误对象
     * @param {number} orderIndex - 订单索引
     */
    logErrorDetails(error, orderIndex) {
        
        const errorDetails = {
            orderIndex: orderIndex,
            errorName: error.name,
            errorMessage: error.message,
            errorStack: error.stack,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        this.logger?.logError(`订单 ${orderIndex + 1} 详细错误信息`, errorDetails);
        
        // 存储错误信息用于后续分析
        if (!this.state.errorLog) {
            this.state.errorLog = [];
        }
        this.state.errorLog.push(errorDetails);
        
        // 限制错误日志大小
        if (this.state.errorLog.length > 50) {
            this.state.errorLog = this.state.errorLog.slice(-20);
        }
    }

    /**
     * 🔧 处理未预期的批量处理错误
     * @param {Error} error - 未预期的错误
     */
    handleUnexpectedBatchError(error) {
        
        this.logger?.logError('批量处理出现未预期错误', {
            error: error.message,
            stack: error.stack,
            state: {
                total: this.state.batchProgress.total,
                completed: this.state.batchProgress.completed,
                failed: this.state.batchProgress.failed
            }
        });
        
        // 显示用户友好的错误提示 - 使用简化UI
        if (window.simpleUI) {
            window.simpleUI.showMessage('批量处理出现意外错误，请稍后重试', 'error');
        } else {
            alert('批量处理出现意外错误，请稍后重试');
        }
    }

    /**
     * 🔧 显示批量处理结果摘要
     */
    showBatchProcessingSummary() {
        const { total, completed, failed } = this.state.batchProgress;
        
        if (!window.simpleUI) return;
        
        let message = `批量处理完成！\n总订单: ${total}\n成功: ${completed}\n失败: ${failed}`;
        
        if (failed === 0) {
            uiManager.showAlert(message, 'success');
        } else {
            uiManager.showAlert(message, failed === total ? 'error' : 'warning');
        }
    }

    /**
     * 更新批量进度显示
     */
    updateBatchProgress() {
        const statusElement = document.querySelector('.batch-create-status');
        if (!statusElement) return;

        const { total, completed, failed, isRunning } = this.state.batchProgress;
        
        if (isRunning) {
            statusElement.innerHTML = `
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${(completed + failed) / total * 100}%"></div>
                </div>
                <div class="progress-text">
                    解析进度: ${completed + failed}/${total} (成功: ${completed}, 失败: ${failed})
                </div>
            `;
            statusElement.style.display = 'block';
        } else {
            statusElement.style.display = 'none';
        }
    }

    /**
     * 批量创建处理
     */
    async handleBatchCreate() {
        // 先解析所有订单
        await this.processAllOrders();
        
        // 然后创建所有已解析的订单
        await this.createSelectedOrders();
    }

    /**
     * 创建选中的订单（委托给批量操作管理器）
     */
    async createSelectedOrders() {
        console.group('🔍 多订单数据流追踪 - 第9步：批量创建订单');

        // 🏗️ 获取选中的订单（委托给状态管理器）
        const selectedOrders = this.stateManager.getSelectedOrders();
        console.log('选中的订单数量:', selectedOrders.length);

        if (selectedOrders.length === 0) {
            console.warn('❌ 没有选中的订单');
            const uiManager = window.OTA.getService('uiManager');
            uiManager?.showError('没有选中的订单');
            console.groupEnd();
            return;
        }

        this.logger?.log(`开始批量创建 ${selectedOrders.length} 个选中订单`, 'info');

        try {
            // 🏗️ 委托给批量操作管理器处理
            const results = await this.batchManager.batchCreateOrders(selectedOrders, {
                enableRetry: true,
                stopOnError: false
            });

            // 处理结果
            const successCount = results.successful.length;
            const failedCount = results.failed.length;

            this.logger?.log(`批量创建完成: 成功 ${successCount}, 失败 ${failedCount}`, 'success');

            // 显示结果
            this.showBatchCreateResults(results);

            console.groupEnd();
            return results;

        } catch (error) {
            this.logger?.logError('批量创建订单失败', error);
            const uiManager = window.OTA.getService('uiManager');
            uiManager?.showError(`批量创建失败: ${error.message}`);
            console.groupEnd();
            throw error;
        }
    }

    /**
     * 显示批量创建结果
     * @param {Object} results - 批量创建结果
     */
    showBatchCreateResults(results) {
        const successCount = results.successful.length;
        const failedCount = results.failed.length;
        const message = `批量创建完成: 成功 ${successCount} 个，失败 ${failedCount} 个`;

        if (failedCount === 0) {
            uiManager?.showSuccess(message);
        } else {
            uiManager?.showAlert(message, 'warning');

            // 显示失败详情
            if (results.failed.length > 0) {
                const failureDetails = results.failed.map(failure =>
                    `订单 ${failure.index}: ${failure.error?.message || failure.error}`
                ).join('\n');

                console.error('批量创建失败详情:', failureDetails);
            }
        }
    }

    /**
     * Chrome MCP 集成接口（委托给Chrome MCP集成器）
     * 用于与Chrome Model Context Protocol集成测试
     */
    initChromeMCPIntegration() {
        // 🏗️ 委托给Chrome MCP集成器
        return this.chromeMCP.init();
    }

    /**
     * 获取Chrome MCP接口（委托给Chrome MCP集成器）
     * @returns {Object} Chrome MCP接口对象
     */
    getChromeMCP() {
        return this.chromeMCP.getChromeMCP();
    }

    /**
     * 测试Chrome MCP集成功能（委托给Chrome MCP集成器）
     */
    async testChromeMCPIntegration() {
        // 🏗️ 委托给Chrome MCP集成器
        return await this.chromeMCP.testChromeMCPIntegration();
    }

    /**
     * 生成订单摘要信息（重构版v2.0 - 核心字段显示，支持Paging订单）
     * @param {Object} order - 订单对象
     * @param {number} index - 订单索引
     * @returns {string} 摘要HTML
     */
    generateOrderSummary(order, index = 0) {
        // 🔍 数据流转调试：记录订单字段映射状态
        this.utils.debugOrderFieldMapping(order, index);

        const isSelected = this.state.selectedOrders.has(index);
        const currency = order.currency || 'MYR';
        const price = order.otaPrice ? parseFloat(order.otaPrice).toFixed(2) : '0.00';
        
        return `
            <div class="compact-card order-card" data-order-index="${index}">
                <div class="order-header compact-inline-layout">
                    <div class="inline-item">
                        <input type="checkbox" id="order-${index}" class="order-checkbox" ${isSelected ? 'checked' : ''}>
                        <label for="order-${index}" class="inline-label">📋#${(index + 1).toString().padStart(3, '0')}</label>
                    </div>
                    <div class="inline-item">${this.getOrderStatusIcon(order)}</div>
                </div>
                
                <div class="order-summary order-grid-layout extended">
                    <div class="order-grid-left">
                        <div class="grid-item editable-field" data-field="customerName" onclick="window.OTA.multiOrderManager.editField(${index}, 'customerName', event)">
                            <span class="grid-label">👤</span>
                            <span class="grid-value">${order.customerName || '未提供'}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item grid-item-route">
                            <span class="grid-label">📍</span>
                            <div class="grid-value">${this.formatRoute(order, index)}</div>
                        </div>
                        <div class="grid-item editable-field" data-field="pickupDate" onclick="window.OTA.multiOrderManager.editField(${index}, 'pickupDate', event)">
                            <span class="grid-label">📅</span>
                            <span class="grid-value">${order.pickupDate || '未指定'}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="pickupTime" onclick="window.OTA.multiOrderManager.editField(${index}, 'pickupTime', event)">
                            <span class="grid-label">⏰</span>
                            <span class="grid-value">${order.pickupTime || '未指定'}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="customerContact" onclick="window.OTA.multiOrderManager.editField(${index}, 'customerContact', event)">
                            <span class="grid-label">📞</span>
                            <span class="grid-value">${this.utils.formatPhone(order.customerContact)}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                    </div>
                    <div class="order-grid-right">
                        <div class="grid-item editable-field" data-field="passengerCount" onclick="window.OTA.multiOrderManager.editField(${index}, 'passengerCount', event)">
                            <span class="grid-label">👥</span>
                            <span class="grid-value">${order.passengerCount || 1}人</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="luggageCount" onclick="window.OTA.multiOrderManager.editField(${index}, 'luggageCount', event)">
                            <span class="grid-label">🧳</span>
                            <span class="grid-value">${order.luggageCount || 0}件</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="price" onclick="window.OTA.multiOrderManager.editField(${index}, 'price', event)">
                            <span class="grid-label">💰</span>
                            <span class="grid-value">${currency}${price}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="vehicleType" onclick="window.OTA.multiOrderManager.editField(${index}, 'vehicleType', event)">
                            <span class="grid-label">🚗</span>
                            <span class="grid-value">${this.utils.getCarTypeName(order.carTypeId)}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <!-- 简化版新增字段：只添加3个最重要的字段 -->
                        <div class="grid-item">
                            <span class="grid-label">🌐</span>
                            <span class="grid-value">${this.utils.getLanguageName(order.languagesIdArray, order)}</span>
                        </div>
                        <div class="grid-item">
                            <span class="grid-label">🏪</span>
                            <span class="grid-value">${this.utils.getOtaChannelName(order.ota, order)}</span>
                        </div>
                        <div class="grid-item">
                            <span class="grid-label">🔖</span>
                            <span class="grid-value">${this.utils.getOtaReferenceNumber(order)}</span>
                        </div>
                        <!-- 添加缺失的2个重要字段 -->
                        <div class="grid-item">
                            <span class="grid-label">💰</span>
                            <span class="grid-value">${this.utils.formatPrice(order.otaPrice, order.currency)}</span>
                        </div>
                        <div class="grid-item">
                            <span class="grid-label">🚗</span>
                            <span class="grid-value">${this.utils.getServiceTypeName(order.serviceTypeId, order)}</span>
                        </div>
                    </div>
                </div>
                
                <div class="order-actions compact-inline-layout">
                    <button type="button" class="btn-create btn-compact" onclick="window.getMultiOrderManager().createSingleOrder(${index})">
                        创建
                    </button>
                </div>
            </div>
        `;
    }
    /**
     * 切换订单详情显示 - 重构为直接调用快捷编辑
     * @param {number} index - 订单索引
     */
    toggleOrderDetails(index) {
        // 简化：直接调用快捷编辑，而不是切换复杂的详细字段界面
        this.quickEditOrder(index);
        
        getLogger()?.log(`订单 ${index + 1} 进入快捷编辑模式`, 'info');
    }

    /**
     * 更新订单字段值 - 增强版，确保数据同步
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {*} value - 字段值
     */
    updateOrderField(index, fieldName, value) {
        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            getLogger()?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        // 更新状态中的订单数据
        this.state.parsedOrders[index][fieldName] = value;
        
        // 执行字段验证（现在会正确找到DOM元素）
        this.validateField(index, fieldName, value);
        
        // 更新摘要显示（现在会传递正确的索引）
        this.updateOrderSummaryDisplay(index);
        
        // 如果在快捷编辑模式，确保UI同步
        const quickEditInput = document.querySelector(`.quick-edit-panel input[name="${fieldName}"]`);
        if (quickEditInput && quickEditInput.value !== value) {
            quickEditInput.value = value;
        }
        
        getLogger()?.log(`订单 ${index + 1} 的 ${fieldName} 已更新为: ${value}`, 'info');
    }

    /**
     * 验证字段（委托给验证管理器）
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {*} value - 字段值
     */
    validateField(index, fieldName, value) {
        this.validationManager.validateField(index, fieldName, value);
    }

    /**
     * 验证和增强OTA参考号（委托给验证管理器）
     * @param {number} index - 订单索引
     */
    validateAndEnhanceOtaReference(index) {
        this.validationManager.validateAndEnhanceOtaReference(index, this.state);
    }

    /**
     * 生成OTA参考号
     * @param {number} index - 订单索引
     */
    generateOtaReference(index) {
        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            getLogger()?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        const order = this.state.parsedOrders[index];

        try {
            // 构建参考号：客户名字前缀 + 日期 + 随机数
            let prefix = '';
            if (order.customerName) {
                // 提取客户名字的前几个字符
                const cleanName = order.customerName.replace(/[^a-zA-Z0-9\u4e00-\u9fff]/g, '');
                prefix = cleanName.substring(0, 3).toUpperCase();
            } else {
                prefix = 'OTA';
            }

            // 日期部分
            let datePart = '';
            if (order.pickupDate) {
                const date = new Date(order.pickupDate);
                if (!isNaN(date.getTime())) {
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    datePart = month + day;
                }
            }
            if (!datePart) {
                const today = new Date();
                const month = String(today.getMonth() + 1).padStart(2, '0');
                const day = String(today.getDate()).padStart(2, '0');
                datePart = month + day;
            }

            // 航班号部分（如果有）
            let flightPart = '';
            if (order.flightInfo) {
                const flightMatch = order.flightInfo.match(/[A-Z]{2}\d{3,4}/);
                if (flightMatch) {
                    flightPart = flightMatch[0];
                }
            }

            // 随机数部分
            const randomPart = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

            // 组合参考号
            const otaReference = `${prefix}${datePart}${flightPart}${randomPart}`;

            // 更新字段
            this.updateOrderField(index, 'otaReferenceNumber', otaReference);

            // 更新UI中的输入框
            const input = document.querySelector(`.order-item[data-order-index="${index}"] input[name="otaReferenceNumber"]`);
            if (input) {
                input.value = otaReference;
                input.classList.remove('ota-reference-missing');
            }

            this.logger?.log(`为订单 ${index + 1} 生成OTA参考号: ${otaReference}`, 'success');
            return otaReference;

        } catch (error) {
            this.logger?.logError(`生成OTA参考号失败`, error);
            // 生成简单的随机参考号作为备用
            const simpleRef = 'OTA' + Date.now().toString().slice(-6);
            this.updateOrderField(index, 'otaReferenceNumber', simpleRef);
            return simpleRef;
        }
    }

    /**
     * 快捷编辑订单（委托给快捷编辑管理器）
     * @param {number} index - 订单索引
     */
    quickEditOrder(index) {
        this.quickEditManager.quickEditOrder(index);
    }

    /**
     * 编辑单个字段
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {Event} event - 点击事件
     */
    editField(index, fieldName, event) {
        event.stopPropagation(); // 阻止事件冒泡到父元素

        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            getLogger()?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        const order = this.state.parsedOrders[index];
        const fieldElement = event.currentTarget;
        const valueElement = fieldElement.querySelector('.grid-value');

        if (!valueElement) return;

        // 如果已经在编辑状态，则退出编辑
        if (fieldElement.classList.contains('editing-field')) {
            this.exitFieldEdit(fieldElement, index, fieldName);
            return;
        }

        // 进入编辑状态
        fieldElement.classList.add('editing-field');

        // 获取当前值
        const currentValue = this.getFieldValue(order, fieldName);

        // 创建编辑输入框
        const inputElement = this.createFieldInput(fieldName, currentValue);

        // 替换显示元素
        valueElement.style.display = 'none';
        fieldElement.appendChild(inputElement);

        // 聚焦并选中文本
        inputElement.focus();
        if (inputElement.select) {
            inputElement.select();
        }

        // 绑定事件
        inputElement.addEventListener('blur', () => {
            this.saveFieldEdit(fieldElement, inputElement, index, fieldName);
        });

        inputElement.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.saveFieldEdit(fieldElement, inputElement, index, fieldName);
            } else if (e.key === 'Escape') {
                this.exitFieldEdit(fieldElement, index, fieldName);
            }
        });

        getLogger()?.log(`开始编辑字段: ${fieldName}`, 'info');
    }

    /**
     * 获取字段值
     * @param {Object} order - 订单对象
     * @param {string} fieldName - 字段名
     * @returns {string} 字段值
     */
    getFieldValue(order, fieldName) {
        switch (fieldName) {
            case 'customerName':
                return order.customerName || '';
            case 'pickupDate':
                return order.pickupDate || '';
            case 'pickupTime':
                return order.pickupTime || '';
            case 'customerContact':
                return order.customerContact || '';
            case 'passengerCount':
                return order.passengerCount || 1;
            case 'luggageCount':
                return order.luggageCount || 0;
            case 'price':
                return order.price || '';
            case 'vehicleType':
                return order.carTypeId || 1;
            default:
                return '';
        }
    }

    /**
     * 创建字段输入框
     * @param {string} fieldName - 字段名
     * @param {*} currentValue - 当前值
     * @returns {HTMLElement} 输入元素
     */
    createFieldInput(fieldName, currentValue) {
        let inputElement;

        switch (fieldName) {
            case 'pickupDate':
                inputElement = document.createElement('input');
                inputElement.type = 'date';
                inputElement.value = currentValue;
                break;

            case 'pickupTime':
                inputElement = document.createElement('input');
                inputElement.type = 'time';
                inputElement.value = currentValue;
                break;

            case 'passengerCount':
            case 'luggageCount':
                inputElement = document.createElement('input');
                inputElement.type = 'number';
                inputElement.min = '0';
                inputElement.max = fieldName === 'passengerCount' ? '20' : '50';
                inputElement.value = currentValue;
                break;

            case 'price':
                inputElement = document.createElement('input');
                inputElement.type = 'number';
                inputElement.step = '0.01';
                inputElement.min = '0';
                inputElement.value = currentValue;
                break;

            case 'vehicleType':
                inputElement = document.createElement('select');
                const vehicleTypes = [
                    { id: 1, name: '舒适5座' },
                    { id: 2, name: '豪华轿车' },
                    { id: 3, name: 'MPV 7座' },
                    { id: 4, name: '商务车' }
                ];
                vehicleTypes.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.name;
                    option.selected = type.id == currentValue;
                    inputElement.appendChild(option);
                });
                break;

            default:
                inputElement = document.createElement('input');
                inputElement.type = 'text';
                inputElement.value = currentValue;
                break;
        }

        inputElement.className = 'field-edit-input';
        return inputElement;
    }

    /**
     * 保存字段编辑
     * @param {HTMLElement} fieldElement - 字段元素
     * @param {HTMLElement} inputElement - 输入元素
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     */
    saveFieldEdit(fieldElement, inputElement, index, fieldName) {
        const newValue = inputElement.value;

        // 更新订单数据
        this.updateOrderField(index, fieldName, newValue);

        // 退出编辑状态
        this.exitFieldEdit(fieldElement, index, fieldName);

        // 重新渲染订单列表以反映更改
        this.renderOrderList();

        getLogger()?.log(`字段 ${fieldName} 已更新为: ${newValue}`, 'info');
    }

    /**
     * 退出字段编辑（委托给清理管理器）
     * @param {HTMLElement} fieldElement - 字段元素
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     */
    exitFieldEdit(fieldElement, index, fieldName) {
        this.cleanupManager.exitFieldEdit(fieldElement, index, fieldName);
    }



    /**
     * 🔍 分析数据流转过程中的字段变化
     * @param {Array} originalOrders - Gemini AI返回的原始订单数据
     * @param {Array} processedOrders - 处理后的订单数据
     */
    analyzeDataFlowTransformation(originalOrders, processedOrders) {
        console.group('🔍 数据流转分析报告');

        const analysis = {
            originalCount: originalOrders?.length || 0,
            processedCount: processedOrders?.length || 0,
            fieldTransformations: [],
            dataLoss: [],
            fieldMappingIssues: []
        };

        if (!originalOrders || !processedOrders) {
            console.warn('❌ 缺少原始数据或处理后数据');
            console.groupEnd();
            return analysis;
        }

        // 逐个订单分析
        originalOrders.forEach((originalOrder, index) => {
            const processedOrder = processedOrders[index];
            if (!processedOrder) {
                analysis.dataLoss.push(`订单 #${index + 1}: 整个订单丢失`);
                return;
            }

            const orderAnalysis = {
                orderIndex: index + 1,
                fieldChanges: {},
                missingFields: [],
                newFields: []
            };

            // 检查字段变化
            const allFields = new Set([
                ...Object.keys(originalOrder),
                ...Object.keys(processedOrder)
            ]);

            allFields.forEach(field => {
                const originalValue = originalOrder[field];
                const processedValue = processedOrder[field];

                if (originalValue !== undefined && processedValue === undefined) {
                    orderAnalysis.missingFields.push(field);
                } else if (originalValue === undefined && processedValue !== undefined) {
                    orderAnalysis.newFields.push(field);
                } else if (originalValue !== processedValue) {
                    orderAnalysis.fieldChanges[field] = {
                        original: originalValue,
                        processed: processedValue
                    };
                }
            });

            analysis.fieldTransformations.push(orderAnalysis);
        });

        // 输出分析结果
        console.log('📊 数据流转统计:', {
            原始订单数: analysis.originalCount,
            处理后订单数: analysis.processedCount,
            数据完整性: analysis.processedCount === analysis.originalCount ? '✅ 完整' : '❌ 有丢失'
        });

        analysis.fieldTransformations.forEach(orderAnalysis => {
            console.group(`📋 订单 #${orderAnalysis.orderIndex} 字段变化分析`);

            if (orderAnalysis.missingFields.length > 0) {
                console.warn('❌ 丢失字段:', orderAnalysis.missingFields);
            }

            if (orderAnalysis.newFields.length > 0) {
                console.log('✅ 新增字段:', orderAnalysis.newFields);
            }

            if (Object.keys(orderAnalysis.fieldChanges).length > 0) {
                console.log('🔄 字段变化:', orderAnalysis.fieldChanges);
            }

            console.groupEnd();
        });

        console.groupEnd();

        // 记录到日志系统
        getLogger()?.log('数据流转分析完成', 'info', analysis);

        return analysis;
    }

    /**
     * 检查常见字段显示问题（委托给验证管理器）
     * @param {Array} orders - 订单数组
     * @returns {Object} 问题分析报告
     */
    checkCommonFieldDisplayIssues(orders) {
        return this.validationManager.checkCommonFieldDisplayIssues(orders);
    }

    /**
     * 获取字段的备用名称
     * @param {string} fieldName - 字段名
     * @returns {string} 备用字段名
     */
    getAlternativeFieldName(fieldName) {
        const alternatives = {
            'pickup': 'pickupLocation',
            'dropoff': 'dropoffLocation',
            'customerContact': 'phone',
            'price': 'otaPrice'
        };
        return alternatives[fieldName] || fieldName;
    }

    /**
     * 🔍 生成订单内容字段显示问题排查报告
     * @param {Array} originalOrders - 原始订单数据
     * @param {Array} processedOrders - 处理后订单数据
     * @returns {Object} 完整的排查报告
     */
    generateFieldDisplayDiagnosticReport(originalOrders, processedOrders) {
        console.group('📋 订单内容字段显示问题排查报告');

        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalOrders: processedOrders?.length || 0,
                issuesFound: 0,
                criticalIssues: 0,
                recommendations: []
            },
            dataFlowAnalysis: null,
            fieldDisplayIssues: null,
            detailedFindings: [],
            actionItems: []
        };

        try {
            // 1. 数据流转分析
            console.log('🔍 步骤1: 数据流转分析');
            report.dataFlowAnalysis = this.analyzeDataFlowTransformation(originalOrders, processedOrders);

            // 2. 字段显示问题检查
            console.log('🔍 步骤2: 字段显示问题检查');
            report.fieldDisplayIssues = this.checkCommonFieldDisplayIssues(processedOrders);

            // 3. 详细问题分析
            console.log('🔍 步骤3: 详细问题分析');
            processedOrders.forEach((order, index) => {
                const orderFindings = this.analyzeIndividualOrderIssues(order, index);
                if (orderFindings.issues.length > 0) {
                    report.detailedFindings.push(orderFindings);
                }
            });

            // 4. 统计问题数量
            report.summary.issuesFound = report.detailedFindings.length;
            report.summary.criticalIssues = report.detailedFindings.filter(f => f.severity === 'critical').length;

            // 5. 生成修复建议
            report.actionItems = this.generateFixRecommendations(report);

            // 6. 输出报告摘要
            console.log('📊 排查报告摘要:', {
                订单总数: report.summary.totalOrders,
                发现问题: report.summary.issuesFound,
                严重问题: report.summary.criticalIssues,
                修复建议数: report.actionItems.length
            });

            if (report.summary.issuesFound > 0) {
                console.warn('❌ 发现字段显示问题，请查看详细报告');
            } else {
                console.log('✅ 未发现字段显示问题');
            }

        } catch (error) {
            console.error('❌ 生成排查报告时出错:', error);
            report.error = error.message;
        }

        console.groupEnd();

        // 记录完整报告到日志系统
        getLogger()?.log('订单字段显示问题排查报告', 'info', report);

        return report;
    }

    /**
     * 分析单个订单的问题
     * @param {Object} order - 订单对象
     * @param {number} index - 订单索引
     * @returns {Object} 订单问题分析结果
     */
    analyzeIndividualOrderIssues(order, index) {
        const findings = {
            orderIndex: index + 1,
            issues: [],
            severity: 'normal',
            affectedFields: []
        };

        // 检查关键字段缺失
        const criticalFields = ['customerName', 'pickup', 'dropoff'];
        criticalFields.forEach(field => {
            const value = order[field] || order[this.getAlternativeFieldName(field)];
            if (!value || value === '未指定' || value === '未提供') {
                findings.issues.push(`关键字段 ${field} 缺失或为空`);
                findings.affectedFields.push(field);
                findings.severity = 'critical';
            }
        });

        // 检查数据格式问题
        if (order.pickupDate && !/^\d{4}-\d{2}-\d{2}$/.test(order.pickupDate)) {
            findings.issues.push('日期格式不正确');
            findings.affectedFields.push('pickupDate');
        }

        if (order.pickupTime && !/^\d{2}:\d{2}$/.test(order.pickupTime)) {
            findings.issues.push('时间格式不正确');
            findings.affectedFields.push('pickupTime');
        }

        return findings;
    }

    /**
     * 生成修复建议
     * @param {Object} report - 排查报告
     * @returns {Array} 修复建议列表
     */
    generateFixRecommendations(report) {
        const recommendations = [];

        if (report.summary.criticalIssues > 0) {
            recommendations.push({
                priority: 'high',
                action: '修复关键字段缺失问题',
                description: '检查Gemini AI解析逻辑，确保关键字段被正确提取和映射'
            });
        }

        if (report.fieldDisplayIssues && Object.keys(report.fieldDisplayIssues.fieldMappingProblems).length > 0) {
            recommendations.push({
                priority: 'medium',
                action: '优化字段映射逻辑',
                description: '实现更智能的字段名映射，支持多种可能的字段名变体'
            });
        }

        if (report.fieldDisplayIssues && Object.keys(report.fieldDisplayIssues.dataTypeIssues).length > 0) {
            recommendations.push({
                priority: 'medium',
                action: '添加数据类型验证',
                description: '在显示前对数据进行类型检查和格式化处理'
            });
        }

        return recommendations;
    }

    /**
     * 🔧 应用与单订单一致的数据处理逻辑
     * @param {Array} orders - 原始订单数组
     * @returns {Array} 处理后的订单数组
     */
    applyConsistentDataProcessing(orders) {
        console.group('🔧 应用一致性数据处理');

        if (!orders || !Array.isArray(orders)) {
            console.warn('❌ 无效的订单数据');
            console.groupEnd();
            return [];
        }

        const processedOrders = orders.map((order, index) => {
            console.log(`🔧 处理订单 #${index + 1}`);

            // 创建订单副本，避免修改原始数据
            const processedOrder = { ...order };

            // 1. 应用智能默认值
            this.applySmartDefaultsToOrder(processedOrder);

            // 2. 验证和格式化字段
            this.validateAndFormatOrderFields(processedOrder);

            // 3. 确保必要字段存在
            this.ensureRequiredFields(processedOrder);

            console.log(`✅ 订单 #${index + 1} 处理完成`);
            return processedOrder;
        });

        console.log(`✅ 所有订单处理完成，共处理 ${processedOrders.length} 个订单`);
        console.groupEnd();

        return processedOrders;
    }

    /**
     * 🔧 为单个订单应用智能默认值（复用单订单逻辑）
     * @param {Object} order - 订单对象
     */
    applySmartDefaultsToOrder(order) {
        // 1. 智能默认：车型推荐（同步单订单系统逻辑）
        if (!order.carTypeId || order.carTypeId === 0) {
            // 使用与单订单系统完全一致的车型推荐逻辑
            const passengers = parseInt(order.passengerCount) || 1;
            if (passengers <= 3) order.carTypeId = 5; // 5 Seater
            else if (passengers <= 4) order.carTypeId = 37; // Extended 5
            else if (passengers <= 5) order.carTypeId = 15; // 7 Seater MPV
            else if (passengers <= 6) order.carTypeId = 32; // Velfire/Alphard
            else if (passengers <= 7) order.carTypeId = 20; // 10 Seater MPV
            else order.carTypeId = 23; // 14 Seater Van
            console.log(`🚗 应用车型推荐: ${order.carTypeId} (基于乘客数: ${passengers})`);
        }

        // 2. 智能默认：子分类（服务类型）
        if (!order.subCategoryId || order.subCategoryId === 0) {
            order.subCategoryId = 2; // 默认接机服务
            console.log('📋 应用默认子分类: 接机服务');
        }

        // 3. 智能默认：驾驶区域
        if (!order.drivingRegionId || order.drivingRegionId === 0) {
            order.drivingRegionId = 1; // 默认吉隆坡
            console.log('🌍 应用默认驾驶区域: 吉隆坡');
        }

        // 4. 智能默认：语言（简化的单一语言选择策略）
        if (!order.languagesIdArray || order.languagesIdArray.length === 0) {
            // 使用统一的扩展中文检测正则表达式
            const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;
            const customerName = order.customerName || '';
            const extraRequirement = order.extraRequirement || '';
            const combinedText = `${customerName} ${extraRequirement}`;

            order.languagesIdArray = chineseRegex.test(combinedText) ? [4] : [2]; // 中文[4] 或 英文[2]
            console.log(`🌐 应用默认语言: ${order.languagesIdArray} (基于内容检测)`);
        }

        // 5. 智能默认：乘客和行李数量
        if (!order.passengerCount || order.passengerCount === 0) {
            order.passengerCount = 1;
            console.log('👥 应用默认乘客数量: 1');
        }

        if (!order.luggageCount || order.luggageCount === 0) {
            order.luggageCount = 1;
            console.log('🧳 应用默认行李数量: 1');
        }

        // 6. 智能默认：货币
        if (!order.currency) {
            order.currency = 'MYR';
            console.log('💰 应用默认货币: MYR');
        }
    }

    /**
     * 验证和格式化订单字段（委托给验证管理器）
     * @param {Object} order - 订单对象
     */
    validateAndFormatOrderFields(order) {
        this.validationManager.validateAndFormatOrderFields(order);
    }

    /**
     * 🔧 应用备用字段名映射（同步单订单系统的字段映射逻辑）
     * @param {Object} order - 订单对象
     */
    applyAlternativeFieldMapping(order) {
        // 地址字段映射
        if (!order.pickup && order.pickupLocation) {
            order.pickup = order.pickupLocation;
            console.log('🔄 使用备用字段: pickupLocation → pickup');
        }
        if (!order.dropoff && order.dropoffLocation) {
            order.dropoff = order.dropoffLocation;
            console.log('🔄 使用备用字段: dropoffLocation → dropoff');
        }

        // 联系方式字段映射
        if (!order.customerContact && order.phone) {
            order.customerContact = order.phone;
            console.log('🔄 使用备用字段: phone → customerContact');
        }

        // 价格字段映射
        if (!order.price && order.otaPrice) {
            order.price = order.otaPrice;
            console.log('🔄 使用备用字段: otaPrice → price');
        }

        // 客户姓名字段映射
        if (!order.customerName && order.customer_name) {
            order.customerName = order.customer_name;
            console.log('🔄 使用备用字段: customer_name → customerName');
        }

        // 日期时间字段映射
        if (!order.pickupDate && order.pickup_date) {
            order.pickupDate = order.pickup_date;
            console.log('🔄 使用备用字段: pickup_date → pickupDate');
        }
        if (!order.pickupTime && order.pickup_time) {
            order.pickupTime = order.pickup_time;
            console.log('🔄 使用备用字段: pickup_time → pickupTime');
        }

        // 乘客和行李字段映射
        if (!order.passengerCount && order.passenger_count) {
            order.passengerCount = order.passenger_count;
            console.log('🔄 使用备用字段: passenger_count → passengerCount');
        }
        if (!order.luggageCount && order.luggage_count) {
            order.luggageCount = order.luggage_count;
            console.log('🔄 使用备用字段: luggage_count → luggageCount');
        }

        // 特殊要求字段映射
        if (!order.extraRequirement && order.extra_requirement) {
            order.extraRequirement = order.extra_requirement;
            console.log('🔄 使用备用字段: extra_requirement → extraRequirement');
        }
    }

    /**
     * 提取字段值（同步单订单系统的字段提取逻辑）
     * @param {string} text - 文本
     * @param {Array} labels - 标签数组
     * @returns {string|null} 提取的值
     */
    extractField(text, labels) {
        for (const label of labels) {
            const pattern = new RegExp(`${label}\\s*[:：]\\s*([^\\n\\r]+)`, 'i');
            const match = text.match(pattern);
            if (match && match[1]) {
                return match[1].trim().replace(/\[.*?\]/g, ''); // 移除[待确认]等标记
            }
        }
        return null;
    }

    /**
     * 推荐车型（同步单订单系统的车型推荐逻辑）
     * @param {number} passengerCount - 乘客人数
     * @returns {number} 车型ID
     */
    recommendCarType(passengerCount) {
        if (!passengerCount || passengerCount <= 3) return 5; // 5 Seater
        if (passengerCount <= 4) return 37; // Extended 5
        if (passengerCount <= 5) return 15; // 7 Seater MPV
        if (passengerCount <= 6) return 32; // Velfire/Alphard
        if (passengerCount <= 7) return 20; // 10 Seater MPV
        return 23; // 14 Seater Van
    }

    /**
     * 检测服务类型（同步单订单系统的服务类型检测逻辑）
     * @param {string} text - 文本
     * @returns {number} 服务类型ID
     */
    detectServiceType(text) {
        const serviceField = this.extractField(text, ['服务类型']);
        if (serviceField) {
            if (serviceField.includes('接机')) return 2;
            if (serviceField.includes('送机')) return 3;
            if (serviceField.includes('包车')) return 4;
        }

        // 基于关键词检测
        if (text.includes('接机') || text.includes('pickup')) return 2;
        if (text.includes('送机') || text.includes('drop off')) return 3;
        if (text.includes('包车') || text.includes('charter')) return 4;

        return 2; // 默认接机
    }

    /**
     * 检测语言（简化的单一语言选择策略）
     * @param {string} text - 文本
     * @returns {Array} 语言ID数组
     */
    detectLanguages(text) {
        // 使用统一的扩展中文检测正则表达式
        const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;

        return chineseRegex.test(text) ? [4] : [2]; // 中文[4] 或 英文[2]
    }

    /**
     * 获取语言ID数组（简化的单一语言选择策略）
     * @param {string} text - 文本内容（备注、额外要求等）
     * @param {string} customerName - 客户姓名
     * @returns {Array} 语言ID数组
     */
    getLanguagesIdArray(text = '', customerName = '') {
        // 使用统一的扩展中文检测正则表达式
        const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;

        // 合并文本和客户姓名进行检测
        const combinedText = `${text || ''} ${customerName || ''}`;

        return chineseRegex.test(combinedText) ? [4] : [2]; // 中文[4] 或 英文[2]
    }

    /**
     * 🔧 确保必要字段存在
     * @param {Object} order - 订单对象
     */
    ensureRequiredFields(order) {
        // 确保显示字段有合理的默认值
        if (!order.customerName || order.customerName.trim() === '') {
            order.customerName = '待确认客户';
            console.log('📝 设置默认客户姓名');
        }

        if (!order.customerContact || order.customerContact.trim() === '') {
            order.customerContact = '待确认联系方式';
            console.log('📞 设置默认联系方式');
        }

        if (!order.pickup || order.pickup.trim() === '') {
            order.pickup = '待确认上车地点';
            console.log('📍 设置默认上车地点');
        }

        if (!order.dropoff || order.dropoff.trim() === '') {
            order.dropoff = '待确认下车地点';
            console.log('📍 设置默认下车地点');
        }

        if (!order.pickupDate || order.pickupDate.trim() === '') {
            // 设置为明天的日期
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            order.pickupDate = tomorrow.toISOString().split('T')[0];
            console.log('📅 设置默认日期: 明天');
        }

        if (!order.pickupTime || order.pickupTime.trim() === '') {
            order.pickupTime = '09:00';
            console.log('⏰ 设置默认时间: 09:00');
        }

        console.log('✅ 必要字段检查完成');
    }

    /**
     * 🔧 创建快捷编辑面板（修复内存泄漏）
     * @param {number} index - 订单索引
     */
    createQuickEditPanel(index) {
        // 先清理现有的快捷编辑面板
        this.cleanupQuickEditPanels();

        const order = this.state.parsedOrders[index];
        if (!order) {
            getLogger()?.log(`订单 ${index} 不存在，无法创建快捷编辑面板`, 'warn');
            return;
        }
        
        // 创建覆盖层
        const overlay = document.createElement('div');
        overlay.className = 'quick-edit-overlay';
        overlay.setAttribute('data-order-index', index.toString());
        
        // 创建编辑面板
        const panel = document.createElement('div');
        panel.className = 'quick-edit-panel';
        
        // 常用字段的快捷编辑
        const quickFields = [
            { name: 'customerName', label: '客户姓名', type: 'text', required: true },
            { name: 'customerContact', label: '联系电话', type: 'tel', required: false },
            { name: 'pickup', label: '上车地点', type: 'text', required: true },
            { name: 'dropoff', label: '目的地', type: 'text', required: true },
            { name: 'pickupDate', label: '接送日期', type: 'date', required: true },
            { name: 'pickupTime', label: '接送时间', type: 'time', required: false },
            { name: 'otaReferenceNumber', label: 'OTA参考号', type: 'text', required: true }
        ];

        let fieldsHTML = quickFields.map(field => {
            const value = order[field.name] || '';
            const isOtaRef = field.name === 'otaReferenceNumber';
            const hasValue = value && value.toString().trim() !== '';
            const isRequired = field.required;
            const isInvalid = isRequired && !hasValue;
            
            return `
                <div class="order-field-group ${isInvalid ? 'invalid' : ''}" data-field="${field.name}" data-required="${isRequired}">
                    <label class="order-field-label">${field.label}${isRequired ? ' *' : ''}</label>
                    <div class="${isOtaRef ? 'ota-reference-group' : 'field-input-wrapper'}">
                        <input 
                            type="${field.type}" 
                            name="${field.name}" 
                            value="${this.escapeHtml(value)}" 
                            class="order-field-input ${isOtaRef && !hasValue ? 'ota-reference-missing' : ''}"
                            data-field="${field.name}"
                            data-index="${index}"
                            onblur="window.OTA.multiOrderManager.handleQuickEditBlur(event, ${index}, '${field.name}')"
                            oninput="window.OTA.multiOrderManager.handleQuickEditInput(event, ${index}, '${field.name}')"
                            onkeypress="window.OTA.multiOrderManager.handleQuickEditKeypress(event)">
                        ${isOtaRef && !hasValue ? `
                            <button type="button" class="ota-reference-generate-btn" 
                                    onclick="window.OTA.multiOrderManager.generateOtaReference(${index})" 
                                    title="生成随机参考号">🎲</button>
                        ` : ''}
                    </div>
                    <div class="field-validation-container"></div>
                </div>
            `;
        }).join('');

        panel.innerHTML = `
            <div class="quick-edit-header">
                <h3 class="quick-edit-title">快速编辑订单 ${index + 1}</h3>
                <button class="quick-edit-close" onclick="window.OTA.multiOrderManager.exitQuickEdit(${index})">✕</button>
            </div>
            <div class="quick-edit-fields">
                ${fieldsHTML}
            </div>
            <div class="quick-edit-actions">
                <button class="btn btn-outline" onclick="window.OTA.multiOrderManager.exitQuickEdit(${index})">取消</button>
                <button class="btn btn-primary" onclick="window.OTA.multiOrderManager.saveQuickEdit(${index})">保存</button>
            </div>
        `;

        overlay.appendChild(panel);
        document.body.appendChild(overlay);

        // 标记为使用中，防止被定期清理误删
        overlay.classList.add('in-use');

        // 显示面板动画
        requestAnimationFrame(() => {
            panel.classList.add('show');
        });

        // 为输入框添加事件监听器（使用绑定的方法避免内存泄漏）
        const inputs = panel.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                input.select();
            });
        });

        // 点击覆盖层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.exitQuickEdit(index);
            }
        });

        // 添加键盘事件监听
        document.addEventListener('keydown', this.handleQuickEditEscape);

        getLogger()?.log(`创建快捷编辑面板完成: 订单 ${index + 1}`, 'info');
    }

    /**
     * 🔧 HTML转义函数（防止XSS）
     */
    escapeHtml(unsafe) {
        if (typeof unsafe !== 'string') return unsafe;
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    /**
     * 🔧 快捷编辑事件处理器（修复内存泄漏）
     */
    handleQuickEditBlur(event, index, fieldName) {
        const value = event.target.value;
        this.saveFieldEdit(index, fieldName, value);
        this.updateOrderField(index, fieldName, value);
        this.validateField(index, fieldName, value);
    }

    handleQuickEditInput(event, index, fieldName) {
        const value = event.target.value;
        this.updateOrderField(index, fieldName, value);
        this.validateField(index, fieldName, value);
    }

    handleQuickEditKeypress(event) {
        if (event.key === 'Enter') {
            event.target.blur();
        }
    }

    handleQuickEditEscape(event) {
        if (event.key === 'Escape') {
            const overlay = document.querySelector('.quick-edit-overlay');
            if (overlay) {
                const index = overlay.getAttribute('data-order-index');
                if (index) {
                    window.OTA.multiOrderManager.exitQuickEdit(parseInt(index));
                }
            }
        }
    }

    /**
     * 退出快捷编辑（委托给清理管理器）
     * @param {number} index - 订单索引
     */
    exitQuickEdit(index) {
        this.cleanupManager.exitQuickEdit(index);
    }

    /**
     * 保存快捷编辑
     * @param {number} index - 订单索引
     */
    saveQuickEdit(index) {
        // 验证所有必填字段
        const requiredFields = ['customerName', 'pickup', 'dropoff', 'pickupDate', 'otaReferenceNumber'];
        const order = this.state.parsedOrders[index];
        let hasErrors = false;

        for (const fieldName of requiredFields) {
            const value = order[fieldName];
            if (!value || value.toString().trim() === '') {
                hasErrors = true;
                break;
            }
        }

        if (hasErrors) {
            // 显示错误提示
            const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
            if (orderItem) {
                orderItem.classList.add('invalid');
                setTimeout(() => {
                    orderItem.classList.remove('invalid');
                }, 2000);
            }
            
            getLogger()?.log(`订单 ${index + 1} 存在必填字段缺失，无法保存`, 'warn');
            return;
        }

        // 保存成功，退出编辑模式
        this.exitQuickEdit(index);
        getLogger()?.log(`订单 ${index + 1} 快捷编辑已保存`, 'success');
    }

    /**
     * 更新订单摘要显示
     * @param {number} index - 订单索引
     */
    updateOrderSummaryDisplay(index) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem || !this.state.parsedOrders[index]) return;

        const order = this.state.parsedOrders[index];
        const summaryDiv = orderItem.querySelector('.order-summary');
        
        if (summaryDiv) {
            // 修复：传递正确的索引参数到generateOrderSummary
            summaryDiv.innerHTML = this.generateOrderSummary(order, index);
        }
    }

    /**
     * 获取OTA渠道选项HTML
     * @returns {string} OTA渠道选项HTML
     */
    getOtaChannelOptions() {
        // 从ota-channel-mapping.js模组获取渠道数据
        const appState = this.getService('appState');
        const currentUser = appState?.get ? appState.get('auth.user') : null;
        let otaChannels = [];

        try {
            // 优先获取当前用户的特定渠道配置
            if (currentUser && window.OTA?.otaChannelMapping?.getConfig) {
                const userConfig = window.OTA.otaChannelMapping.getConfig(currentUser.id) || 
                                 window.OTA.otaChannelMapping.getConfig(currentUser.email);
                
                if (userConfig && userConfig.options) {
                    // 使用用户特定的渠道配置
                    otaChannels = userConfig.options;
                    getLogger()?.log(`使用用户特定OTA渠道配置`, 'info', { 
                        userId: currentUser.id, 
                        channelCount: otaChannels.length 
                    });
                }
            }

            // 如果没有用户特定配置，使用通用渠道列表
            if (otaChannels.length === 0 && window.OTA?.otaChannelMapping?.commonChannels) {
                otaChannels = window.OTA.otaChannelMapping.commonChannels;
                getLogger()?.log(`使用通用OTA渠道列表`, 'info', { 
                    channelCount: otaChannels.length 
                });
            }

            // 如果仍然没有数据，使用fallback列表
            if (otaChannels.length === 0) {
                otaChannels = [
                    { value: 'Ctrip', text: '携程' },
                    { value: 'Klook', text: 'Klook客路' },
                    { value: 'KKday', text: 'KKday' },
                    { value: 'Other', text: '其他' }
                ];
                getLogger()?.log(`OTA渠道模组未加载，使用fallback列表`, 'warn');
            }

        } catch (error) {
            getLogger()?.logError('获取OTA渠道配置时出错', error);
            // 错误时使用基本列表
            otaChannels = [
                { value: 'Ctrip', text: '携程' },
                { value: 'Klook', text: 'Klook客路' },
                { value: 'KKday', text: 'KKday' },
                { value: 'Other', text: '其他' }
            ];
        }
        
        return otaChannels.map(channel => 
            `<option value="${channel.value}">${channel.text}</option>`
        ).join('');
    }

    /**
     * 获取语言选择组件HTML (下拉菜单+多选择tick box方式)
     * @returns {string} 语言选择组件HTML
     */
    getLanguageSelectionComponent() {
        // **修复**: 使用与单订单相同的语言数据源，添加更好的错误处理
        const apiService = this.getService('api');
        let languages = [];

        try {
            // 尝试从API服务获取语言数据
            if (apiService && apiService.staticData && apiService.staticData.languages) {
                languages = apiService.staticData.languages;
                getLogger().log('✅ 多订单管理器：使用API服务语言数据', 'info', {
                    count: languages.length
                });
            } else {
                // 尝试从应用状态获取语言数据
                const appState = this.getService('appState');
                const systemData = appState ? appState.getSystemData() : null;

                if (systemData && systemData.languages) {
                    languages = systemData.languages;
                    getLogger().log('✅ 多订单管理器：使用应用状态语言数据', 'info', {
                        count: languages.length
                    });
                } else {
                    // 使用简化语言工具获取数据
                    if (window.simpleLanguageUtils) {
                        languages = window.simpleLanguageUtils.languages;
                        getLogger().log('⚠️ 多订单管理器：使用简化语言工具数据', 'warn');
                    }
                }
            }
        } catch (error) {
            getLogger().logError('多订单管理器：获取语言数据失败', {
                error: error.message
            });
            // 使用简化语言工具的备用数据
            try {
                if (window.simpleLanguageUtils) {
                    languages = window.simpleLanguageUtils.languages;
                }
            } catch (fallbackError) {
                // 如果统一管理器也失败，使用最基础的数据
                languages = [
                    { id: 2, name: 'English (EN)' },
                    { id: 3, name: 'Malay (MY)' },
                    { id: 4, name: 'Chinese (CN)' }
                ];
            }
        }
        
        // **修复**: 添加国际化支持
        const i18nManager = this.getI18nManagerWithRetry();
        const placeholderText = i18nManager ?
            i18nManager.t('form.selectLanguages') : '选择语言...';

        return `
            <div class="language-selection-wrapper">
                <div class="language-dropdown" id="batchLanguageDropdown">
                    <div class="language-dropdown-header" onclick="window.OTA.multiOrderManager.toggleLanguageDropdown()">
                        <span id="languageSelectedText" data-i18n="form.selectLanguages">${placeholderText}</span>
                        <span class="dropdown-arrow">▼</span>
                    </div>
                    <div class="language-dropdown-content" id="languageDropdownContent" style="display: none;">
                        ${languages.map(lang => `
                            <label class="language-checkbox-item">
                                <input type="checkbox" value="${lang.id}" onchange="window.OTA.multiOrderManager.updateLanguageSelection()">
                                <span class="language-name">${lang.name}</span>
                            </label>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }




    /**
     * 获取选中订单的索引数组（委托给验证管理器）
     * @returns {Array<number>} 选中订单的索引数组
     */
    getSelectedOrderIndexes() {
        return this.validationManager.getSelectedOrderIndexes();
    }


    /**
     * 确保面板在视窗范围内可见
     */
    ensurePanelVisible() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (!multiOrderPanel) return;

        // 确保面板居中显示在视窗中
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // 获取面板的实际尺寸
        const panelRect = multiOrderPanel.getBoundingClientRect();
        
        // 如果面板超出视窗范围，调整其大小
        let adjustments = {};
        
        if (panelRect.width > viewportWidth * 0.95) {
            adjustments.width = '95vw';
        }
        
        if (panelRect.height > viewportHeight * 0.9) {
            adjustments.height = '90vh';
        }
        
        // 应用调整
        Object.assign(multiOrderPanel.style, adjustments);
        
        // 滚动到面板位置（如果需要）
        multiOrderPanel.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center',
            inline: 'center'
        });
        
        getLogger()?.log('📱 多订单面板位置已优化', 'info', {
            viewport: `${viewportWidth}x${viewportHeight}`,
            panel: `${panelRect.width}x${panelRect.height}`,
            adjustments
        });
    }

    /**
     * 添加面板拖拽功能（增强浮窗体验）
     */
    addPanelDragFeature() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        const header = multiOrderPanel?.querySelector('.multi-order-header');
        
        if (!multiOrderPanel || !header) return;
        
        // 检查是否已经添加了拖拽功能，避免重复绑定
        if (header.dataset.dragEnabled === 'true') {
            getLogger()?.log('🔒 面板拖拽功能已存在，跳过重复绑定', 'info');
            return;
        }

        let isDragging = false;
        let currentX = 0;
        let currentY = 0;
        let initialX = 0;
        let initialY = 0;

        header.style.cursor = 'move';
        header.title = '拖拽面板来移动位置';

        const dragStart = (e) => {
            if (e.target.closest('button')) return; // 避免按钮干扰拖拽
            
            initialX = e.clientX - currentX;
            initialY = e.clientY - currentY;
            
            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
                multiOrderPanel.style.transition = 'none';
            }
        };

        const dragEnd = (e) => {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            multiOrderPanel.style.transition = 'all var(--transition-normal)';
        };

        const drag = (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                
                // 限制拖拽范围在视窗内
                const rect = multiOrderPanel.getBoundingClientRect();
                const maxX = window.innerWidth - rect.width;
                const maxY = window.innerHeight - rect.height;
                
                currentX = Math.max(0, Math.min(currentX, maxX));
                currentY = Math.max(0, Math.min(currentY, maxY));
                
                multiOrderPanel.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        };

        header.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);
        
        // 标记拖拽功能已启用
        header.dataset.dragEnabled = 'true';

        getLogger()?.log('🖱️ 面板拖拽功能已启用', 'info');
    }


    // **修复**: 删除重复的方法定义，保留第1036行和第1051行的版本



    /**
     * 🔧 设置清理机制（防止内存泄漏）
     */
    setupCleanupMechanism() {
        
        // 保存事件处理器引用
        this.boundEventHandlers.set('multiOrderDetected', (event) => {
            try {
                console.group('🔍 多订单数据流追踪 - 第5步：事件接收');
                const { multiOrderResult, orderText } = event.detail;
                
                console.log('事件类型:', event.type);
                console.log('事件详情:', event.detail);
                console.log('多订单结果:', multiOrderResult);
                console.log('订单文本长度:', orderText?.length);
                console.log('订单数量:', multiOrderResult?.orderCount);
                console.log('是否多订单:', multiOrderResult?.isMultiOrder);
                console.log('订单数据:', multiOrderResult?.orders);
                console.groupEnd();
                
                this.logger?.log('🔔 收到多订单检测事件（统一入口）', 'info', { 
                    hasMultiOrderResult: !!multiOrderResult,
                    orderCount: multiOrderResult?.orderCount || 0,
                    isMultiOrder: multiOrderResult?.isMultiOrder || false,
                    hasOrderText: !!orderText 
                });
                
                console.log('🔄 即将调用handleMultiOrderDetectionUnified...');
                this.handleMultiOrderDetectionUnified(multiOrderResult, orderText);
            } catch (error) {
                console.error('❌ 多订单事件处理失败:', error);
                this.logger?.logError('多订单事件处理失败', error);
            }
        });
        
        this.boundEventHandlers.set('appStateChanged', (event) => {
            try {
                if (event.detail.key === 'currentOrder') {
                    this.handleOrderStateChange(event.detail.value);
                }
            } catch (error) {
                this.logger?.logError('应用状态变化处理失败', error);
            }
        });
        
        // 设置页面卸载时的清理（委托给清理管理器）
        this.boundEventHandlers.set('beforeunload', () => {
            this.cleanupManager.cleanup(this.state, this.boundEventHandlers, this.debounceTimer);
        });

        // 绑定事件监听器
        document.addEventListener('multiOrderDetected', this.boundEventHandlers.get('multiOrderDetected'));
        document.addEventListener('appStateChanged', this.boundEventHandlers.get('appStateChanged'));
        window.addEventListener('beforeunload', this.boundEventHandlers.get('beforeunload'));
        
        this.logger?.log('🔧 多订单管理器清理机制已设置', 'info');
    }

    /**
     * 🔧 启动定期清理机制（委托给清理管理器）
     */
    startPeriodicCleanup() {
        // 定期清理现在由清理管理器处理
        this.logger?.log('🕐 定期清理机制已启动（委托给清理管理器）', 'info');
    }

    /**
     * 🔧 定期清理未使用资源（委托给清理管理器）
     */
    periodicCleanup() {
        // 定期清理现在由清理管理器处理
        this.cleanupManager.periodicCleanup();
    }

    /**
     * 🔧 停止定期清理机制（委托给清理管理器）
     */
    stopPeriodicCleanup() {
        // 停止清理现在由清理管理器处理
        this.cleanupManager.stopPeriodicCleanup();
    }


    /**
     * 🔧 清理资源（委托给清理管理器）
     */
    cleanup() {
        this.cleanupManager.cleanup(this.state, this.boundEventHandlers, this.debounceTimer);
    }

    /**
     * 🔧 清理缓存（委托给清理管理器）
     */
    clearCache() {
        this.cleanupManager.clearCache(this.state);
    }

    /**
     * 🔧 清理快捷编辑面板和DOM节点（委托给清理管理器）
     */
    cleanupQuickEditPanels() {
        this.cleanupManager.cleanupQuickEditPanels();
    }

    /**
     * 🔧 清理批量处理状态（委托给清理管理器）
     */
    cleanupBatchProcessing() {
        this.cleanupManager.cleanupBatchProcessing(this.state);
    }

    /**
     * 🔧 清理DOM引用（委托给清理管理器）
     */
    cleanupDomReferences() {
        this.cleanupManager.cleanupDomReferences();
    }

    /**
     * 集成学习系统UI更正功能
     * @param {Array} orders - 订单数组
     */
    integrateLearningSystemUI(orders) {
        try {
            // 检查学习系统是否可用
            const uiCorrectionManager = window.OTA?.uiCorrectionManager || window.uiCorrectionManager;
            if (!uiCorrectionManager) {
                getLogger()?.log('学习系统UI更正管理器不可用，跳过集成', 'info');
                return;
            }

            // 为每个订单项添加学习系统功能
            const orderItems = document.querySelectorAll('.order-item');
            orderItems.forEach((orderItem, index) => {
                if (index < orders.length) {
                    uiCorrectionManager.enhanceOrderItem(orderItem, orders[index], index);
                }
            });

            getLogger()?.log('学习系统UI更正功能集成完成', 'success', {
                enhancedOrders: Math.min(orderItems.length, orders.length)
            });

        } catch (error) {
            getLogger()?.logError('集成学习系统UI更正功能失败', error);
        }
    }

    // 简化为使用固定置信度阈值，提升性能和可维护性



    // 简化为固定阈值，无需复杂的学习机制

    /**
     * 获取Gemini配置
     * @returns {Object} Gemini配置对象
     */
    getGeminiConfig() {
        return {
            enabled: true,
            attempts: 2,
            consistencyThreshold: this.state.adaptiveState.currentThreshold || this.config.confidenceThreshold,
            timeout: 25000
        };
    }

    /**
     * **新增**: 获取i18n管理器，带重试机制
     * @returns {Object|null} i18n管理器实例
     */
    getI18nManagerWithRetry() {
        // 尝试多种方式获取i18n管理器
        const attempts = [
            () => window.getI18nManager && window.getI18nManager(),
            () => window.OTA && window.OTA.i18nManager,
            () => window.i18nManager
        ];

        for (const attempt of attempts) {
            try {
                const manager = attempt();
                if (manager && typeof manager.t === 'function') {
                    return manager;
                }
            } catch (error) {
                // 继续尝试下一种方式
            }
        }

        return null;
    }

    // ...existing code...
}

// 全局实例变量
let multiOrderManagerInstance = null;

/**
 * @OTA_FACTORY 创建多订单管理器实例
 * 🏷️ 标签: @OTA_MULTI_ORDER_MANAGER_FACTORY
 * 📝 说明: 单例工厂函数，获取多订单管理器实例
 * ⚠️ 警告: 已注册，请勿重复开发
 * @returns {MultiOrderManager} 管理器实例
 */
function getMultiOrderManager() {
    if (!multiOrderManagerInstance) {
        multiOrderManagerInstance = new MultiOrderManager();
        // 延迟注册到依赖容器，避免循环依赖
        registerMultiOrderManager();
    }
    return multiOrderManagerInstance;
}

/**
 * 注册多订单管理器到依赖容器
 * 延迟执行避免循环依赖
 */
function registerMultiOrderManager() {
    if (window.OTA && window.OTA.Registry && multiOrderManagerInstance) {
        // 🔧 方案A：统一注册到dependency-container系统
        // 移除ota-registry注册，确保架构统一
        try {
            if (window.OTA && window.OTA.container) {
                window.OTA.container.register('multiOrderManager', () => getMultiOrderManager(), { singleton: true });
            }
        } catch (error) {
            console.warn('注册多订单管理器到依赖容器失败:', error);
        }
    }
}

// 导出到全局作用域
window.MultiOrderManager = MultiOrderManager;
window.getMultiOrderManager = getMultiOrderManager;

// 确保OTA命名空间存在并暴露管理器
window.OTA = window.OTA || {};
window.OTA.MultiOrderManager = MultiOrderManager;
window.OTA.getMultiOrderManager = getMultiOrderManager;

// 延迟初始化，避免循环依赖
Object.defineProperty(window.OTA, 'multiOrderManager', {
    get: function() {
        return getMultiOrderManager();
    },
    configurable: true
});

// 向后兼容 - 延迟初始化
Object.defineProperty(window, 'multiOrderManager', {
    get: function() {
        return getMultiOrderManager();
    },
    configurable: true
});

// 注册到OTA注册中心 - 已在getMultiOrderManager()中处理，避免循环依赖

console.log('✅ 重构版多订单管理器已加载', {
    version: '4.0.0-refactored',
    modules: {
        detector: !!window.OTA?.MultiOrderDetector,
        renderer: !!window.OTA?.MultiOrderRenderer,
        processor: !!window.OTA?.MultiOrderProcessor,
        transformer: !!window.OTA?.MultiOrderTransformer
    }
});

// 结束防重复加载检查
}
