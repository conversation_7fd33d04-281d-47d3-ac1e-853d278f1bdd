/**
 * Debug utility for checking service registration
 */
(function() {
    'use strict';
    
    window.debugServices = function() {
        console.log('=== Service Registration Debug ===');
        
        // Check OTA namespace
        console.log('1. window.OTA exists:', !!window.OTA);
        
        // Check services namespace
        console.log('2. window.OTA.services exists:', !!window.OTA?.services);
        if (window.OTA?.services) {
            console.log('   Available services:', Object.keys(window.OTA.services));
        }
        
        // Check specific uiService
        console.log('3. uiService loaded:', !!window.OTA?.services?.uiService);
        if (window.OTA?.services?.uiService) {
            const uiService = window.OTA.services.uiService;
            console.log('   uiService methods:', Object.getOwnPropertyNames(uiService).filter(prop => typeof uiService[prop] === 'function'));
        }
        
        // Check dependency container
        console.log('4. Dependency container exists:', !!window.OTA?.container);
        if (window.OTA?.container) {
            console.log('   Registered services:', window.OTA.container.getRegisteredServices());
        }
        
        // Check service locator
        console.log('5. Service locator exists:', !!window.OTA?.serviceLocator);
        
        // Check getService function
        console.log('6. getService function exists:', !!window.OTA?.getService);
        
        // Try to get uiService
        if (window.OTA?.getService) {
            try {
                const uiService = window.OTA.getService('uiService');
                console.log('7. getService("uiService") success:', !!uiService);
                if (uiService && typeof uiService.getStatus === 'function') {
                    console.log('   uiService status:', uiService.getStatus());
                }
            } catch (error) {
                console.log('7. getService("uiService") failed:', error.message);
            }
        }
        
        console.log('=== End Debug ===');
    };
    
    // Auto-run debug when loaded
    console.log('📊 Debug utilities loaded. Use window.debugServices() for service debugging.');
})();
