/**
 * 事件管理器模块 (优化版)
 * 负责特定事件处理和协调，与GlobalEventCoordinator协同工作
 * 保留核心功能，去除过度复杂的事件系统
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    /**
     * 事件管理器类 (优化版)
     * 专注于特定组件的事件处理，与GlobalEventCoordinator协同工作
     */
    class EventManager {
        constructor() {
            this.initialized = false;
            this.eventHandlers = new Map();
            this.quickEditManager = null;
            this.debounceTimers = new Map();
        }

        /**
         * 初始化事件管理器
         */
        init() {
            if (this.initialized) {
                getLogger().log('事件管理器已经初始化', 'warning');
                return;
            }

            this.bindGlobalEventHandlers();
            this.bindKeyboardShortcuts();
            this.initialized = true;
            getLogger().log('事件管理器初始化完成', 'success');
        }

        /**
         * 绑定全局事件处理器 (已迁移到EventService微服务)
         * @deprecated 此方法已废弃，事件处理已统一到EventService微服务
         */
        bindGlobalEventHandlers() {
            // 🗑️ 已移除重复的事件绑定 - 减法重构优化
            // 全局事件处理已统一到EventService微服务中
            // 避免重复绑定：document.click, document.change, document.input
            getLogger().log('全局事件处理已委托给EventService微服务', 'info');
        }

        /**
         * 绑定面板事件
         */
        bindPanelEvents() {
            const panels = document.querySelectorAll('.panel, .order-panel, .multi-order-panel');
            panels.forEach(panel => {
                this.addEventHandler(panel, 'click', this.handlePanelClick.bind(this));
                this.addEventHandler(panel, 'change', this.handlePanelChange.bind(this));
            });
        }

        /**
         * 绑定键盘快捷键
         */
        bindKeyboardShortcuts() {
            this.addEventHandler(document, 'keydown', this.handleKeyboardShortcuts.bind(this));
        }

        /**
         * 绑定触摸事件 (移动端支持)
         */
        bindTouchEvents() {
            if ('ontouchstart' in window) {
                this.addEventHandler(document, 'touchstart', this.handleTouchStart.bind(this));
                this.addEventHandler(document, 'touchend', this.handleTouchEnd.bind(this));
            }
        }

        /**
         * 添加事件处理器
         */
        addEventHandler(element, eventType, handler, options = {}) {
            if (!element || !eventType || !handler) return;

            const key = `${eventType}_${Date.now()}_${Math.random()}`;
            const wrappedHandler = (event) => {
                try {
                    handler(event);
                } catch (error) {
                    getLogger().log('事件处理器执行失败', 'error', { 
                        eventType, 
                        error: error.message 
                    });
                }
            };

            element.addEventListener(eventType, wrappedHandler, options);
            this.eventHandlers.set(key, {
                element,
                eventType,
                handler: wrappedHandler,
                originalHandler: handler
            });

            return key;
        }

        /**
         * 移除事件处理器
         */
        removeEventHandler(key) {
            const handlerInfo = this.eventHandlers.get(key);
            if (handlerInfo) {
                handlerInfo.element.removeEventListener(
                    handlerInfo.eventType, 
                    handlerInfo.handler
                );
                this.eventHandlers.delete(key);
                return true;
            }
            return false;
        }

        /**
         * 防抖函数
         */
        debounce(func, delay) {
            return (...args) => {
                const key = func.toString().substring(0, 50); // 函数标识
                
                if (this.debounceTimers.has(key)) {
                    clearTimeout(this.debounceTimers.get(key));
                }

                const timer = setTimeout(() => {
                    func.apply(this, args);
                    this.debounceTimers.delete(key);
                }, delay);

                this.debounceTimers.set(key, timer);
            };
        }

        /**
         * 处理文档点击事件
         */
        handleDocumentClick(event) {
            // 检查是否点击在编辑区域
            if (this.quickEditManager && event.target.closest('.quick-edit-active')) {
                return; // 让QuickEditManager处理
            }

            // 其他文档点击处理逻辑
            this.notifyClickHandlers('document', event);
        }

        /**
         * 处理面板点击事件
         */
        handlePanelClick(event) {
            const panel = event.currentTarget;
            const panelId = panel.id || panel.dataset.panelId;
            
            this.notifyClickHandlers('panel', event, { panelId });
        }

        /**
         * 处理面板变化事件
         */
        handlePanelChange(event) {
            const panel = event.currentTarget;
            const panelId = panel.id || panel.dataset.panelId;
            
            this.notifyChangeHandlers('panel', event, { panelId });
        }

        /**
         * 处理表单变化事件
         * @deprecated 此方法已废弃，表单事件处理已统一到EventService微服务
         */
        handleFormChange(event) {
            // 🗑️ 已移除重复的表单事件处理 - 减法重构优化
            // 表单变化处理已统一到EventService微服务中
            getLogger().log('表单变化事件已委托给EventService微服务', 'info');
        }

        /**
         * 处理输入事件 (防抖)
         * @deprecated 此方法已废弃，输入事件处理已统一到EventService微服务
         */
        handleInput(event) {
            // 🗑️ 已移除重复的输入事件处理 - 减法重构优化
            // 输入事件处理已统一到EventService微服务中
            getLogger().log('输入事件已委托给EventService微服务', 'info');
        }

        /**
         * 处理键盘快捷键
         */
        handleKeyboardShortcuts(event) {
            // Ctrl/Cmd + S: 保存
            if ((event.ctrlKey || event.metaKey) && event.key === 's') {
                event.preventDefault();
                this.triggerSave();
                return;
            }

            // Ctrl/Cmd + Enter: 提交
            if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                event.preventDefault();
                this.triggerSubmit();
                return;
            }

            // F2: 快速编辑
            if (event.key === 'F2') {
                event.preventDefault();
                this.triggerQuickEdit();
                return;
            }
        }

        /**
         * 处理触摸开始事件
         */
        handleTouchStart(event) {
            this.touchStartTime = Date.now();
            this.touchStartTarget = event.target;
        }

        /**
         * 处理触摸结束事件
         */
        handleTouchEnd(event) {
            const touchDuration = Date.now() - (this.touchStartTime || 0);
            const isLongPress = touchDuration > 500;
            
            if (isLongPress && this.touchStartTarget === event.target) {
                this.handleLongPress(event);
            }
        }

        /**
         * 处理长按事件
         */
        handleLongPress(event) {
            // 触发快速编辑或上下文菜单
            if (this.quickEditManager) {
                this.quickEditManager.handleLongPress(event);
            }
        }

        /**
         * 通知点击处理器
         */
        notifyClickHandlers(type, event, context = {}) {
            document.dispatchEvent(new CustomEvent('eventManagerClick', {
                detail: { type, event, context }
            }));
        }

        /**
         * 通知变化处理器
         */
        notifyChangeHandlers(type, event, context = {}) {
            document.dispatchEvent(new CustomEvent('eventManagerChange', {
                detail: { type, event, context }
            }));
        }

        /**
         * 通知输入处理器
         */
        notifyInputHandlers(input, event) {
            document.dispatchEvent(new CustomEvent('eventManagerInput', {
                detail: { input, event, value: input.value }
            }));
        }

        /**
         * 触发保存操作
         */
        triggerSave() {
            document.dispatchEvent(new CustomEvent('triggerSave'));
            getLogger().log('触发保存快捷键', 'info');
        }

        /**
         * 触发提交操作
         */
        triggerSubmit() {
            const activeForm = document.querySelector('form:focus-within');
            if (activeForm) {
                activeForm.dispatchEvent(new Event('submit', { bubbles: true }));
            } else {
                document.dispatchEvent(new CustomEvent('triggerSubmit'));
            }
            getLogger().log('触发提交快捷键', 'info');
        }

        /**
         * 触发快速编辑
         */
        triggerQuickEdit() {
            if (this.quickEditManager) {
                this.quickEditManager.activateQuickEdit();
            } else {
                document.dispatchEvent(new CustomEvent('triggerQuickEdit'));
            }
            getLogger().log('触发快速编辑快捷键', 'info');
        }

        /**
         * 设置快速编辑管理器
         */
        setQuickEditManager(quickEditManager) {
            this.quickEditManager = quickEditManager;
            getLogger().log('快速编辑管理器已设置', 'info');
        }

        /**
         * 获取事件统计信息
         */
        getEventStats() {
            return {
                handlerCount: this.eventHandlers.size,
                debounceTimers: this.debounceTimers.size,
                hasQuickEditManager: !!this.quickEditManager,
                initialized: this.initialized
            };
        }

        /**
         * 清理所有事件处理器
         */
        cleanup() {
            // 清理所有事件处理器
            for (const [key] of this.eventHandlers) {
                this.removeEventHandler(key);
            }

            // 清理防抖定时器
            for (const timer of this.debounceTimers.values()) {
                clearTimeout(timer);
            }
            this.debounceTimers.clear();

            this.initialized = false;
            getLogger().log('事件管理器已清理', 'info');
        }

        /**
         * 检查是否可用
         */
        isAvailable() {
            return this.initialized;
        }
    }

    // 导出到全局命名空间
    window.OTA.managers.EventManager = EventManager;

    // 创建全局实例并注册到依赖容器
    if (window.OTA && window.OTA.container) {
        try {
            window.OTA.container.register('eventManager', () => {
                const manager = new EventManager();
                manager.init();
                return manager;
            }, { singleton: true });
            getLogger().log('EventManager已注册到依赖容器', 'info');
        } catch (error) {
            console.warn('EventManager注册到依赖容器失败:', error.message);
        }
    }

    // 向后兼容性暴露
    if (!window.eventManager && !window.OTA.eventManager) {
        const globalInstance = new EventManager();
        globalInstance.init();
        window.OTA.eventManager = globalInstance;
        window.eventManager = globalInstance;
    }

})();