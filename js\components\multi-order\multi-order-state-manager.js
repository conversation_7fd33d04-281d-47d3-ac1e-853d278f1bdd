/**
 * 文件: multi-order-state-manager.js
 * 路径: js\components\multi-order\multi-order-state-manager.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.getLogger
 * - console (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.getMultiOrderStateManager
 * - window.MultiOrderStateManager
 * - window.OTA
 * - window.OTA
 * - window.OTA.multiOrder
 * - window.OTA.multiOrder
 * - window.OTA.multiOrder.StateManager
 * - window.OTA.multiOrder.getStateManager
 * 
 * === 类声明 ===
 * - class MultiOrderStateManager
 * 
 * === 函数声明 ===
 * - function createMultiOrderStateManager()
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.630Z
 */

/**
 * 🏗️ 多订单状态管理器
 * 负责处理多订单系统的状态管理、数据同步和状态持久化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-20
 */

(function() {
    'use strict';

    /**
     * 多订单状态管理器类
     * 专门处理状态初始化、更新、查询、验证和持久化
     */
    class MultiOrderStateManager {
        /**
         * 构造函数
         * @param {Object} dependencies - 依赖注入对象
         * @param {Object} dependencies.logger - 日志服务
         * @param {Object} dependencies.config - 配置对象
         * @param {Object} dependencies.appState - 应用状态服务
         */
        constructor(dependencies = {}) {
            this.logger = dependencies.logger || this.getLogger();
            this.config = {
                // 状态管理配置
                maxErrorLogSize: 50,
                errorLogRetentionSize: 20,
                batchDelay: 500,
                confidenceThreshold: 0.8,
                autoSaveInterval: 30000,
                stateValidationInterval: 60000,
                ...dependencies.config
            };

            // 依赖注入
            this.appState = dependencies.appState;

            // 🏗️ 核心状态结构
            this.state = {
                // 多订单模式状态
                isMultiOrderMode: false,
                currentSegments: [],
                selectedOrders: new Set(),
                processedOrders: new Map(),
                parsedOrders: [],
                multiOrderResult: null,

                // 批量处理状态
                batchProgress: {
                    total: 0,
                    completed: 0,
                    failed: 0,
                    isRunning: false,
                    startTime: null,
                    processingOrder: null
                },

                // 错误日志状态
                errorLog: [],

                // 自适应状态
                adaptiveState: {
                    currentThreshold: this.config.confidenceThreshold,
                    lastUpdateTime: Date.now(),
                    performanceMetrics: {
                        averageProcessingTime: 0,
                        successRate: 1.0,
                        errorRate: 0.0
                    }
                },

                // UI状态
                uiState: {
                    panelVisible: false,
                    currentEditingIndex: -1,
                    sortOrder: 'created-asc',
                    filterCriteria: null
                }
            };

            // 状态监听器
            this.stateListeners = new Map();
            this.stateHistory = [];
            this.maxHistorySize = 50;

            // 初始化
            this.init();
        }

        /**
         * 初始化状态管理器
         */
        init() {
            this.logger?.log('🏗️ 状态管理器初始化开始', 'info');
            
            try {
                // 设置状态验证定时器
                this.setupStateValidation();
                
                // 设置自动保存
                this.setupAutoSave();
                
                this.logger?.log('✅ 状态管理器初始化完成', 'success');
            } catch (error) {
                this.logger?.logError('状态管理器初始化失败', error);
                throw error;
            }
        }

        /**
         * 设置状态验证定时器
         */
        setupStateValidation() {
            if (this.config.stateValidationInterval > 0) {
                setInterval(() => {
                    this.validateState();
                }, this.config.stateValidationInterval);
            }
        }

        /**
         * 设置自动保存
         */
        setupAutoSave() {
            if (this.config.autoSaveInterval > 0) {
                setInterval(() => {
                    this.saveStateToAppState();
                }, this.config.autoSaveInterval);
            }
        }

        /**
         * 获取完整状态
         * @returns {Object} 当前状态对象
         */
        getState() {
            return this.state;
        }

        /**
         * 获取状态中的特定值
         * @param {string} path - 状态路径，支持点号分隔
         * @returns {*} 状态值
         */
        getStateValue(path) {
            const keys = path.split('.');
            let value = this.state;
            
            for (const key of keys) {
                if (value && typeof value === 'object' && key in value) {
                    value = value[key];
                } else {
                    return undefined;
                }
            }
            
            return value;
        }

        /**
         * 设置状态值
         * @param {string} path - 状态路径
         * @param {*} value - 新值
         * @param {boolean} notify - 是否通知监听器
         */
        setStateValue(path, value, notify = true) {
            const keys = path.split('.');
            const lastKey = keys.pop();
            let target = this.state;
            
            // 导航到目标对象
            for (const key of keys) {
                if (!target[key] || typeof target[key] !== 'object') {
                    target[key] = {};
                }
                target = target[key];
            }
            
            // 记录旧值
            const oldValue = target[lastKey];
            
            // 设置新值
            target[lastKey] = value;
            
            // 记录状态变更历史
            this.recordStateChange(path, oldValue, value);
            
            // 通知监听器
            if (notify) {
                this.notifyStateChange(path, value, oldValue);
            }
            
            this.logger?.log(`状态已更新: ${path}`, 'info', { oldValue, newValue: value });
        }

        /**
         * 重置多订单状态
         */
        resetMultiOrderState() {
            this.state.isMultiOrderMode = false;
            this.state.currentSegments = [];
            this.state.selectedOrders.clear();
            this.state.processedOrders.clear();
            this.state.parsedOrders = [];
            this.state.multiOrderResult = null;
            
            this.notifyStateChange('multiOrder', 'reset');
            this.logger?.log('多订单状态已重置', 'info');
        }

        /**
         * 设置多订单模式
         * @param {boolean} enabled - 是否启用多订单模式
         * @param {Array} segments - 订单分段
         * @param {Array} orders - 解析的订单
         */
        setMultiOrderMode(enabled, segments = [], orders = []) {
            this.state.isMultiOrderMode = enabled;
            this.state.currentSegments = segments;
            this.state.parsedOrders = orders;
            
            // 重置选择状态
            this.state.selectedOrders.clear();
            this.state.processedOrders.clear();
            
            // 默认选中所有订单
            if (enabled && orders.length > 0) {
                orders.forEach((_, index) => {
                    this.state.selectedOrders.add(index);
                });
            }
            
            this.notifyStateChange('multiOrderMode', enabled);
            this.logger?.log(`多订单模式${enabled ? '已启用' : '已禁用'}`, 'info', {
                segments: segments.length,
                orders: orders.length
            });
        }

        /**
         * 更新订单选择状态
         * @param {number} index - 订单索引
         * @param {boolean} selected - 是否选中
         */
        updateOrderSelection(index, selected) {
            if (selected) {
                this.state.selectedOrders.add(index);
            } else {
                this.state.selectedOrders.delete(index);
            }
            
            this.notifyStateChange('orderSelection', { index, selected });
        }

        /**
         * 获取选中的订单
         * @returns {Array} 选中的订单数组
         */
        getSelectedOrders() {
            if (!this.state.parsedOrders) {
                return [];
            }
            
            return Array.from(this.state.selectedOrders)
                .map(index => this.state.parsedOrders[index])
                .filter(Boolean);
        }

        /**
         * 获取选中的订单索引
         * @returns {Array} 选中的订单索引数组
         */
        getSelectedOrderIndexes() {
            return Array.from(this.state.selectedOrders);
        }

        /**
         * 更新订单数据
         * @param {number} index - 订单索引
         * @param {Object} orderData - 订单数据
         */
        updateOrderData(index, orderData) {
            if (this.state.parsedOrders[index]) {
                this.state.parsedOrders[index] = { ...this.state.parsedOrders[index], ...orderData };
                this.notifyStateChange('orderData', { index, data: orderData });
            }
        }

        /**
         * 更新订单字段
         * @param {number} index - 订单索引
         * @param {string} fieldName - 字段名
         * @param {*} value - 新值
         */
        updateOrderField(index, fieldName, value) {
            if (this.state.parsedOrders[index]) {
                this.state.parsedOrders[index][fieldName] = value;
                this.notifyStateChange('orderField', { index, fieldName, value });
            }
        }

        /**
         * 设置处理后的订单数据
         * @param {number} index - 订单索引
         * @param {Object} processedData - 处理后的数据
         */
        setProcessedOrder(index, processedData) {
            this.state.processedOrders.set(index, {
                ...processedData,
                timestamp: Date.now()
            });
            
            this.notifyStateChange('processedOrder', { index, data: processedData });
        }

        /**
         * 获取处理后的订单数据
         * @param {number} index - 订单索引
         * @returns {Object|null} 处理后的订单数据
         */
        getProcessedOrder(index) {
            return this.state.processedOrders.get(index) || null;
        }

        /**
         * 初始化批量处理状态
         * @param {number} total - 总订单数
         */
        initBatchProgress(total) {
            this.state.batchProgress = {
                total,
                completed: 0,
                failed: 0,
                isRunning: true,
                startTime: Date.now(),
                processingOrder: null
            };

            this.notifyStateChange('batchProgress', 'init');
            this.logger?.log(`批量处理状态已初始化，总订单数: ${total}`, 'info');
        }

        /**
         * 更新批量处理进度
         * @param {Object} progress - 进度更新
         */
        updateBatchProgress(progress = {}) {
            Object.assign(this.state.batchProgress, progress);
            this.notifyStateChange('batchProgress', 'update');
        }

        /**
         * 完成批量处理
         */
        completeBatchProgress() {
            this.state.batchProgress.isRunning = false;
            this.state.batchProgress.processingOrder = null;

            const duration = Date.now() - this.state.batchProgress.startTime;
            this.logger?.log(`批量处理完成，耗时: ${duration}ms`, 'success', this.state.batchProgress);

            this.notifyStateChange('batchProgress', 'complete');
        }

        /**
         * 获取批量处理状态
         * @returns {Object} 批量处理状态
         */
        getBatchProgress() {
            return { ...this.state.batchProgress };
        }

        /**
         * 添加错误日志
         * @param {Object} errorDetails - 错误详情
         */
        addErrorLog(errorDetails) {
            if (!this.state.errorLog) {
                this.state.errorLog = [];
            }

            this.state.errorLog.push({
                ...errorDetails,
                timestamp: Date.now()
            });

            // 限制错误日志大小
            if (this.state.errorLog.length > this.config.maxErrorLogSize) {
                this.state.errorLog = this.state.errorLog.slice(-this.config.errorLogRetentionSize);
            }

            this.notifyStateChange('errorLog', 'add');
        }

        /**
         * 获取错误日志
         * @returns {Array} 错误日志数组
         */
        getErrorLog() {
            return [...(this.state.errorLog || [])];
        }

        /**
         * 清空错误日志
         */
        clearErrorLog() {
            this.state.errorLog = [];
            this.notifyStateChange('errorLog', 'clear');
        }

        /**
         * 添加状态监听器
         * @param {string} event - 事件名称
         * @param {Function} callback - 回调函数
         */
        addStateListener(event, callback) {
            if (!this.stateListeners.has(event)) {
                this.stateListeners.set(event, new Set());
            }
            this.stateListeners.get(event).add(callback);
        }

        /**
         * 移除状态监听器
         * @param {string} event - 事件名称
         * @param {Function} callback - 回调函数
         */
        removeStateListener(event, callback) {
            if (this.stateListeners.has(event)) {
                this.stateListeners.get(event).delete(callback);
            }
        }

        /**
         * 通知状态变更
         * @param {string} event - 事件名称
         * @param {*} data - 事件数据
         * @param {*} oldValue - 旧值
         */
        notifyStateChange(event, data, oldValue = null) {
            if (this.stateListeners.has(event)) {
                this.stateListeners.get(event).forEach(callback => {
                    try {
                        callback(data, oldValue);
                    } catch (error) {
                        this.logger?.logError(`状态监听器执行失败: ${event}`, error);
                    }
                });
            }
        }

        /**
         * 记录状态变更历史
         * @param {string} path - 状态路径
         * @param {*} oldValue - 旧值
         * @param {*} newValue - 新值
         */
        recordStateChange(path, oldValue, newValue) {
            this.stateHistory.push({
                path,
                oldValue,
                newValue,
                timestamp: Date.now()
            });

            // 限制历史记录大小
            if (this.stateHistory.length > this.maxHistorySize) {
                this.stateHistory = this.stateHistory.slice(-Math.floor(this.maxHistorySize * 0.8));
            }
        }

        /**
         * 获取状态变更历史
         * @param {number} limit - 限制数量
         * @returns {Array} 状态变更历史
         */
        getStateHistory(limit = 10) {
            return this.stateHistory.slice(-limit);
        }

        /**
         * 验证状态完整性
         * @returns {Object} 验证结果
         */
        validateState() {
            const issues = [];

            try {
                // 验证基本状态结构
                if (!this.state) {
                    issues.push('状态对象不存在');
                    return { valid: false, issues };
                }

                // 验证订单数据一致性
                if (this.state.parsedOrders && this.state.selectedOrders) {
                    const invalidSelections = Array.from(this.state.selectedOrders)
                        .filter(index => index >= this.state.parsedOrders.length);

                    if (invalidSelections.length > 0) {
                        issues.push(`无效的订单选择索引: ${invalidSelections.join(', ')}`);
                    }
                }

                // 验证批量处理状态
                if (this.state.batchProgress) {
                    const { total, completed, failed } = this.state.batchProgress;
                    if (completed + failed > total) {
                        issues.push('批量处理进度数据不一致');
                    }
                }

                // 验证处理订单数据
                if (this.state.processedOrders) {
                    const processedIndexes = Array.from(this.state.processedOrders.keys());
                    const invalidProcessed = processedIndexes.filter(index =>
                        !this.state.parsedOrders || index >= this.state.parsedOrders.length
                    );

                    if (invalidProcessed.length > 0) {
                        issues.push(`无效的处理订单索引: ${invalidProcessed.join(', ')}`);
                    }
                }

                const isValid = issues.length === 0;

                if (!isValid) {
                    this.logger?.log('状态验证发现问题', 'warn', { issues });
                } else {
                    this.logger?.log('状态验证通过', 'info');
                }

                return { valid: isValid, issues };

            } catch (error) {
                this.logger?.logError('状态验证失败', error);
                return { valid: false, issues: ['状态验证过程中发生错误'] };
            }
        }

        /**
         * 保存状态到应用状态服务
         */
        saveStateToAppState() {
            if (!this.appState) return;

            try {
                const stateSnapshot = {
                    isMultiOrderMode: this.state.isMultiOrderMode,
                    selectedOrderCount: this.state.selectedOrders.size,
                    totalOrders: this.state.parsedOrders.length,
                    batchProgress: this.state.batchProgress,
                    timestamp: Date.now()
                };

                this.appState.set?.('multiOrder.state', stateSnapshot);
                this.logger?.log('状态已保存到应用状态服务', 'info');

            } catch (error) {
                this.logger?.logError('保存状态到应用状态服务失败', error);
            }
        }

        /**
         * 从应用状态服务恢复状态
         */
        restoreStateFromAppState() {
            if (!this.appState) return;

            try {
                const savedState = this.appState.get?.('multiOrder.state');
                if (savedState) {
                    // 只恢复安全的状态数据
                    if (savedState.isMultiOrderMode !== undefined) {
                        this.state.isMultiOrderMode = savedState.isMultiOrderMode;
                    }

                    this.logger?.log('状态已从应用状态服务恢复', 'info', savedState);
                }
            } catch (error) {
                this.logger?.logError('从应用状态服务恢复状态失败', error);
            }
        }

        /**
         * 获取状态统计信息
         * @returns {Object} 状态统计
         */
        getStateStats() {
            return {
                totalOrders: this.state.parsedOrders.length,
                selectedOrders: this.state.selectedOrders.size,
                processedOrders: this.state.processedOrders.size,
                errorLogSize: this.state.errorLog?.length || 0,
                isMultiOrderMode: this.state.isMultiOrderMode,
                batchProgress: this.state.batchProgress,
                stateHistorySize: this.stateHistory.length,
                listenersCount: Array.from(this.stateListeners.values())
                    .reduce((total, listeners) => total + listeners.size, 0)
            };
        }

        /**
         * 重置所有状态
         */
        resetAllState() {
            // 保存重要配置
            const config = this.config;
            const appState = this.appState;

            // 重置状态
            this.state = {
                isMultiOrderMode: false,
                currentSegments: [],
                selectedOrders: new Set(),
                processedOrders: new Map(),
                parsedOrders: [],
                multiOrderResult: null,
                batchProgress: {
                    total: 0,
                    completed: 0,
                    failed: 0,
                    isRunning: false,
                    startTime: null,
                    processingOrder: null
                },
                errorLog: [],
                adaptiveState: {
                    currentThreshold: config.confidenceThreshold,
                    lastUpdateTime: Date.now(),
                    performanceMetrics: {
                        averageProcessingTime: 0,
                        successRate: 1.0,
                        errorRate: 0.0
                    }
                },
                uiState: {
                    panelVisible: false,
                    currentEditingIndex: -1,
                    sortOrder: 'created-asc',
                    filterCriteria: null
                }
            };

            // 清空历史记录
            this.stateHistory = [];

            // 通知重置
            this.notifyStateChange('reset', 'all');

            this.logger?.log('所有状态已重置', 'info');
        }

        /**
         * 销毁状态管理器
         */
        destroy() {
            // 清空监听器
            this.stateListeners.clear();

            // 清空历史记录
            this.stateHistory = [];

            // 重置状态
            this.resetAllState();

            this.logger?.log('🏗️ 状态管理器已销毁', 'info');
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务对象
         */
        getLogger() {
            return window.getLogger?.() || {
                log: console.log.bind(console),
                logError: console.error.bind(console)
            };
        }
    }

    /**
     * 创建状态管理器实例的工厂函数
     * @param {Object} dependencies - 依赖注入对象
     * @returns {MultiOrderStateManager} 状态管理器实例
     */
    function createMultiOrderStateManager(dependencies = {}) {
        return new MultiOrderStateManager(dependencies);
    }

    // 导出到全局作用域
    window.getMultiOrderStateManager = createMultiOrderStateManager;
    window.MultiOrderStateManager = MultiOrderStateManager;

    // 确保OTA命名空间存在
    if (typeof window.OTA === 'undefined') {
        window.OTA = {};
    }
    if (typeof window.OTA.multiOrder === 'undefined') {
        window.OTA.multiOrder = {};
    }

    // 注册到OTA命名空间
    window.OTA.multiOrder.StateManager = MultiOrderStateManager;
    window.OTA.multiOrder.getStateManager = createMultiOrderStateManager;

})();
