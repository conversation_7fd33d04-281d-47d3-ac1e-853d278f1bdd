/**
 * 文件: gemini-error-handler.js
 * 路径: js\ai\gemini-error-handler.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 对外暴露 ===
 * - window.OTA
 * - window.OTA.ai
 * - window.OTA.ai.gemini
 * - window.OTA.ai.gemini.errorHandler
 * - window.geminiErrorHandler
 * - window.geminiErrorHandler
 * 
 * === 类声明 ===
 * - class GeminiErrorHandler
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.574Z
 */

/**
 * Gemini AI 错误处理增强模块
 * 提供智能错误处理、重试机制、降级策略
 * <AUTHOR> System
 * @version 1.0.0
 * @created 2025-07-30
 */

window.OTA = window.OTA || {};
window.OTA.ai = window.OTA.ai || {};
window.OTA.ai.gemini = window.OTA.ai.gemini || {};

(function() {
    'use strict';

    /**
     * Gemini AI 错误处理器类
     * 提供智能错误处理和恢复策略
     */
    class GeminiErrorHandler {
        constructor() {
            this.logger = window.OTA?.services?.logger || console;
            this.version = '1.0.0';
            
            // 错误处理配置
            this.config = {
                // 重试配置
                retry: {
                    maxAttempts: 3,
                    baseDelay: 1000,        // 基础延迟1秒
                    exponentialBackoff: true,
                    jitterRange: 0.1        // 抖动范围10%
                },
                // 错误分类
                errorCategories: {
                    NETWORK_ERROR: 'network',
                    API_LIMIT_ERROR: 'api_limit', 
                    INVALID_REQUEST: 'invalid_request',
                    TIMEOUT_ERROR: 'timeout',
                    AUTHENTICATION_ERROR: 'auth',
                    UNKNOWN_ERROR: 'unknown'
                },
                // 降级策略
                fallbackStrategies: {
                    useCache: true,
                    useSimpleParser: true,
                    userNotification: true
                }
            };
            
            // 错误统计
            this.errorStats = {
                totalErrors: 0,
                errorsByCategory: {},
                recentErrors: [],
                successRate: 100
            };
            
            // 重试队列
            this.retryQueue = new Map();
            
            this.initialize();
        }
        
        /**
         * 初始化错误处理器
         */
        initialize() {
            this.logger.log('Gemini错误处理器初始化完成', 'info');
            
            // 定期清理旧的错误记录
            setInterval(() => this.cleanupOldErrors(), 60000);
        }
        
        /**
         * 处理API错误
         * @param {Error} error - 错误对象
         * @param {Object} context - 上下文信息
         * @returns {Object} 处理结果
         */
        async handleError(error, context = {}) {
            try {
                const errorCategory = this.categorizeError(error);
                const errorInfo = {
                    error,
                    category: errorCategory,
                    context,
                    timestamp: Date.now(),
                    id: this.generateErrorId()
                };
                
                // 记录错误
                this.recordError(errorInfo);
                
                // 根据错误类型决定处理策略
                switch (errorCategory) {
                    case 'network':
                    case 'timeout':
                        return await this.handleRetryableError(errorInfo);
                        
                    case 'api_limit':
                        return await this.handleRateLimitError(errorInfo);
                        
                    case 'auth':
                        return await this.handleAuthError(errorInfo);
                        
                    case 'invalid_request':
                        return await this.handleInvalidRequestError(errorInfo);
                        
                    default:
                        return await this.handleUnknownError(errorInfo);
                }
                
            } catch (handlingError) {
                this.logger.log(`错误处理器自身出错: ${handlingError.message}`, 'error');
                return this.createErrorResult('HANDLER_ERROR', handlingError.message);
            }
        }
        
        /**
         * 分类错误类型
         * @param {Error} error - 错误对象
         * @returns {string} 错误分类
         */
        categorizeError(error) {
            const message = error.message?.toLowerCase() || '';
            
            if (message.includes('network') || message.includes('fetch')) {
                return 'network';
            }
            if (message.includes('timeout')) {
                return 'timeout';
            }
            if (message.includes('rate limit') || message.includes('quota')) {
                return 'api_limit';
            }
            if (message.includes('unauthorized') || message.includes('api key')) {
                return 'auth';
            }
            if (message.includes('invalid') || message.includes('bad request')) {
                return 'invalid_request';
            }
            
            return 'unknown';
        }
        
        /**
         * 处理可重试错误
         * @param {Object} errorInfo - 错误信息
         * @returns {Object} 处理结果
         */
        async handleRetryableError(errorInfo) {
            const { context } = errorInfo;
            const retryKey = this.generateRetryKey(context);
            
            // 检查重试次数
            if (!this.retryQueue.has(retryKey)) {
                this.retryQueue.set(retryKey, { attempts: 0, lastAttempt: 0 });
            }
            
            const retryInfo = this.retryQueue.get(retryKey);
            
            if (retryInfo.attempts >= this.config.retry.maxAttempts) {
                this.logger.log(`重试次数已达上限，使用降级策略`, 'warning');
                return await this.applyFallbackStrategy(errorInfo);
            }
            
            // 计算延迟时间
            const delay = this.calculateRetryDelay(retryInfo.attempts);
            
            this.logger.log(`${delay}ms后进行第${retryInfo.attempts + 1}次重试`, 'info');
            
            // 更新重试信息
            retryInfo.attempts++;
            retryInfo.lastAttempt = Date.now();
            
            // 延迟重试
            await this.delay(delay);
            
            return {
                shouldRetry: true,
                retryDelay: delay,
                attemptNumber: retryInfo.attempts
            };
        }
        
        /**
         * 处理API限制错误
         * @param {Object} errorInfo - 错误信息
         * @returns {Object} 处理结果
         */
        async handleRateLimitError(errorInfo) {
            this.logger.log('检测到API限制，等待后重试', 'warning');
            
            // API限制通常需要更长的等待时间
            const waitTime = 60000; // 1分钟
            await this.delay(waitTime);
            
            return {
                shouldRetry: true,
                retryDelay: waitTime,
                reason: 'API_RATE_LIMIT'
            };
        }
        
        /**
         * 应用降级策略
         * @param {Object} errorInfo - 错误信息
         * @returns {Object} 处理结果
         */
        async applyFallbackStrategy(errorInfo) {
            this.logger.log('应用降级策略', 'info');
            
            const fallbackResult = {
                success: false,
                usedFallback: true,
                fallbackType: null,
                data: null
            };
            
            // 尝试使用缓存
            if (this.config.fallbackStrategies.useCache) {
                const cachedResult = await this.tryGetCachedResult(errorInfo.context);
                if (cachedResult) {
                    fallbackResult.success = true;
                    fallbackResult.fallbackType = 'CACHE';
                    fallbackResult.data = cachedResult;
                    return fallbackResult;
                }
            }
            
            // 尝试使用简单解析器
            if (this.config.fallbackStrategies.useSimpleParser) {
                const simpleResult = await this.trySimpleParser(errorInfo.context);
                if (simpleResult) {
                    fallbackResult.success = true;
                    fallbackResult.fallbackType = 'SIMPLE_PARSER';
                    fallbackResult.data = simpleResult;
                    return fallbackResult;
                }
            }
            
            // 通知用户
            if (this.config.fallbackStrategies.userNotification) {
                this.notifyUserOfError(errorInfo);
            }
            
            return fallbackResult;
        }
        
        /**
         * 计算重试延迟
         * @param {number} attemptNumber - 尝试次数
         * @returns {number} 延迟时间（毫秒）
         */
        calculateRetryDelay(attemptNumber) {
            let delay = this.config.retry.baseDelay;
            
            // 指数退避
            if (this.config.retry.exponentialBackoff) {
                delay *= Math.pow(2, attemptNumber);
            }
            
            // 添加抖动
            const jitter = delay * this.config.retry.jitterRange * (Math.random() - 0.5);
            delay += jitter;
            
            return Math.max(delay, 100); // 最小100ms
        }
        
        /**
         * 记录错误信息
         * @param {Object} errorInfo - 错误信息
         */
        recordError(errorInfo) {
            this.errorStats.totalErrors++;
            
            // 按分类统计
            const category = errorInfo.category;
            this.errorStats.errorsByCategory[category] = 
                (this.errorStats.errorsByCategory[category] || 0) + 1;
            
            // 记录最近错误
            this.errorStats.recentErrors.push({
                id: errorInfo.id,
                category: errorInfo.category,
                message: errorInfo.error.message,
                timestamp: errorInfo.timestamp
            });
            
            // 保持最近错误记录不超过100条
            if (this.errorStats.recentErrors.length > 100) {
                this.errorStats.recentErrors = this.errorStats.recentErrors.slice(-100);
            }
            
            // 更新成功率
            this.updateSuccessRate();
            
            this.logger.log(`记录错误: ${category} - ${errorInfo.error.message}`, 'error');
        }
        
        /**
         * 生成错误ID
         * @returns {string} 错误ID
         */
        generateErrorId() {
            return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        
        /**
         * 生成重试键
         * @param {Object} context - 上下文
         * @returns {string} 重试键
         */
        generateRetryKey(context) {
            return `retry_${JSON.stringify(context).substring(0, 100)}`;
        }
        
        /**
         * 延迟函数
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise} Promise对象
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        /**
         * 创建错误结果
         * @param {string} type - 错误类型
         * @param {string} message - 错误消息
         * @returns {Object} 错误结果
         */
        createErrorResult(type, message) {
            return {
                success: false,
                error: {
                    type,
                    message,
                    timestamp: Date.now()
                }
            };
        }
        
        /**
         * 清理旧错误记录
         */
        cleanupOldErrors() {
            const oneHourAgo = Date.now() - (60 * 60 * 1000);
            
            // 清理重试队列中的过期项
            for (const [key, retryInfo] of this.retryQueue.entries()) {
                if (retryInfo.lastAttempt < oneHourAgo) {
                    this.retryQueue.delete(key);
                }
            }
            
            // 清理旧的错误记录
            this.errorStats.recentErrors = this.errorStats.recentErrors.filter(
                error => error.timestamp > oneHourAgo
            );
        }
        
        /**
         * 更新成功率
         */
        updateSuccessRate() {
            // 这里可以实现更复杂的成功率计算逻辑
            // 暂时使用简单的统计
            const recentErrorCount = this.errorStats.recentErrors.length;
            const totalRecentOperations = Math.max(recentErrorCount * 2, 100); // 假设操作数
            this.errorStats.successRate = ((totalRecentOperations - recentErrorCount) / totalRecentOperations) * 100;
        }
        
        /**
         * 获取错误统计信息
         * @returns {Object} 错误统计
         */
        getErrorStats() {
            return {
                ...this.errorStats,
                retryQueueSize: this.retryQueue.size
            };
        }
    }
    
    // 导出到全局命名空间
    window.OTA.ai.gemini.errorHandler = new GeminiErrorHandler();
    
    // 向后兼容
    if (typeof window.geminiErrorHandler === 'undefined') {
        window.geminiErrorHandler = window.OTA.ai.gemini.errorHandler;
    }
    
})();