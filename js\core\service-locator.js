/**
 * 文件: service-locator.js
 * 路径: js\core\service-locator.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - window.OTA.xxx
 * - window.xxx
 * - window.OTA.appState
 * - window.appState
 * - window.OTA.logger
 * - window.logger
 * - window.OTA.apiService
 * - window.apiService
 * - window.OTA.geminiService
 * - window.geminiService
 * - window.OTA.uiManager
 * - window.uiManager
 * - window.OTA.utils
 * - window.utils
 * - window.OTA.i18nManager
 * - window.i18nManager
 * - window.OTA.formManager
 * - window.formManager
 * - window.OTA.imageUploadManager
 * - window.imageUploadManager
 * - window.OTA.currencyConverter
 * - window.currencyConverter
 * - window.OTA.multiOrderManager
 * - window.multiOrderManager
 * - window.OTA.orderHistoryManager
 * - window.orderHistoryManager
 * - window.OTA.pagingServiceManager
 * - window.pagingServiceManager
 * - window.OTA.languageManager
 * - window.languageManager
 * - window.OTA.kimiService
 * - window.kimiService
 * - window.OTA.apiKeyManager
 * - window.apiKeyManager
 * - window.OTA.core
 * - window.warningManager
 * - window.OTA.gemini
 * - window.configManager
 * - window.OTA.componentLifecycleManager
 * - window.componentLifecycleManager
 * - window.OTA.unifiedDataManager
 * - window.unifiedDataManager
 * - window.OTA.appState
 * - window.appState
 * - window.OTA.logger
 * - window.logger
 * - window.OTA.apiService
 * - window.apiService
 * - window.OTA.geminiService
 * - window.OTA.geminiService
 * - window.OTA.gemini
 * - window.OTA.gemini.coordinator
 * - window.OTA.uiManager
 * - window.uiManager
 * - window.OTA.utils
 * - window.utils
 * - window.OTA.formManager
 * - window.formManager
 * - window.OTA.languageManager
 * - window.getLanguageManager
 * - window.OTA.kimiService
 * - window.OTA.imageUploadManager
 * - window.OTA.currencyConverter
 * - window.OTA.multiOrderManager
 * - window.OTA.orderHistoryManager
 * - window.OTA.pagingServiceManager
 * - window.OTA.realtimeAnalysisManager
 * - window.getRealtimeAnalysisManager
 * - window.OTA.i18nManager
 * - window.getI18nManager
 * - window.OTA.apiKeyManager
 * - window.getApiKeyManager
 * - window.OTA.core
 * - window.getWarningManager
 * - window.OTA.gemini
 * - window.OTA.gemini
 * - window.OTA.componentLifecycleManager
 * - window.OTA.unifiedDataManager
 * - window.AppState
 * - window.OTA.xxx
 * - window.xxx
 * - window.OTA.container
 * - window.OTA.container
 * - window.OTA.container
 * - window.OTA.container
 * - window.OTA.Registry
 * - window.OTA.Registry.registerService
 * - console (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.OTA
 * - window.OTA.serviceLocator
 * - window.OTA.getService
 * - window.getService
 * - window.OTA.getServiceUsageStats
 * - window.OTA.getServiceMigrationReport
 * - window.OTA.resetServiceMonitoring
 * - window.OTA.setServiceMonitoringEnabled
 * - window.getApiService
 * - window.getGeminiService
 * - window.getUIManager
 * - window.getUtils
 * - window.getImageUploadManager
 * - window.getCurrencyConverter
 * - window.getMultiOrderManager
 * - window.getOrderHistoryManager
 * - window.getPagingServiceManager
 * - window.getKimiService
 * 
 * === 类声明 ===
 * - class ServiceLocator
 * - class DefaultAppState
 * 
 * === 函数声明 ===
 * - function getService()
 * - function getServiceUsageStats()
 * - function getServiceMigrationReport()
 * - function resetServiceMonitoring()
 * - function setServiceMonitoringEnabled()
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.662Z
 */

/**
 * 服务定位器 - 统一服务获取接口
 * 解决当前系统中 window.OTA.xxx || window.xxx 的双重模式问题
 * 
 * 使用方式:
 * - 替换所有 getAppState() 为 getService('appState')
 * - 替换所有 getLogger() 为 getService('logger')
 * - 统一所有依赖获取方式
 */

// 确保OTA命名空间和依赖容器存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 服务定位器类
     * 提供统一的服务获取接口，兼容旧的获取方式
     */
    class ServiceLocator {
        constructor() {
            this.container = null;
            this.fallbackMap = new Map();
            this.migrationWarnings = new Set();
            
            // 监控数据收集
            this.serviceUsageStats = new Map();
            this.serviceAccessTimes = new Map();
            this.fallbackUsageCount = new Map();
            this.monitoringEnabled = true;
        }

        /**
         * 初始化服务定位器
         * @param {DependencyContainer} container - 依赖容器实例
         */
        init(container) {
            this.container = container;
            this.setupFallbackMap();
            this.registerCoreServices();
            this.registerManagerServices();
            console.log('✅ 服务定位器已初始化');
        }

        /**
         * 设置降级映射
         * 用于兼容旧的获取方式
         */
        setupFallbackMap() {
            // 核心服务
            this.fallbackMap.set('appState', () => window.OTA.appState || window.appState);
            this.fallbackMap.set('logger', () => window.OTA.logger || window.logger);
            this.fallbackMap.set('apiService', () => window.OTA.apiService || window.apiService);
            this.fallbackMap.set('geminiService', () => window.OTA.geminiService || window.geminiService);
            this.fallbackMap.set('uiManager', () => window.OTA.uiManager || window.uiManager);
            this.fallbackMap.set('utils', () => window.OTA.utils || window.utils);
            this.fallbackMap.set('i18nManager', () => window.OTA.i18nManager || window.i18nManager);
            
            // 管理器服务
            this.fallbackMap.set('formManager', () => window.OTA.formManager || window.formManager);
            this.fallbackMap.set('languageManager', () => window.OTA.languageManager || window.languageManager);
            
            // 简化工具
            this.fallbackMap.set('simplePriceUtils', () => window.simplePriceUtils);
            this.fallbackMap.set('simpleLanguageUtils', () => window.simpleLanguageUtils);
            this.fallbackMap.set('simpleFormUtils', () => window.simpleFormUtils);
            this.fallbackMap.set('simpleUI', () => window.simpleUI);
            
            // 功能管理器
            this.fallbackMap.set('imageUploadManager', () => window.OTA.imageUploadManager || window.imageUploadManager);
            this.fallbackMap.set('multiOrderManager', () => window.OTA.multiOrderManager || window.multiOrderManager);
            this.fallbackMap.set('orderHistoryManager', () => window.OTA.orderHistoryManager || window.orderHistoryManager);
            this.fallbackMap.set('pagingServiceManager', () => window.OTA.pagingServiceManager || window.pagingServiceManager);
            this.fallbackMap.set('kimiService', () => window.OTA.kimiService || window.kimiService);
            
            // P0优先级：核心架构服务
            this.fallbackMap.set('apiKeyManager', () => window.OTA.apiKeyManager || window.apiKeyManager);
            this.fallbackMap.set('warningManager', () => window.OTA.core?.warningManager || window.warningManager);
            this.fallbackMap.set('configManager', () => window.OTA.gemini?.core?.configManager || window.configManager);
            this.fallbackMap.set('componentLifecycleManager', () => window.OTA.componentLifecycleManager || window.componentLifecycleManager);
            this.fallbackMap.set('unifiedDataManager', () => window.OTA.unifiedDataManager || window.unifiedDataManager);
        }

        /**
         * 注册核心服务到依赖容器
         */
        registerCoreServices() {
            if (!this.container) return;

            // 注册核心服务工厂函数
            this.container.register('appState', () => {
                return window.OTA.appState || window.appState || this.createAppState();
            });

            this.container.register('logger', () => {
                return window.OTA.logger || window.logger || this.createLogger();
            });

            this.container.register('apiService', () => {
                return window.OTA.apiService || window.apiService || this.createApiService();
            });

            this.container.register('geminiService', () => {
                // 先检查已注册的实例
                if (window.OTA.geminiService) {
                    return window.OTA.geminiService;
                }
                // 尝试从coordinator获取
                if (window.OTA.gemini?.coordinator) {
                    return window.OTA.gemini.coordinator;
                }
                // 最后降级到默认实例
                return this.createGeminiService();
            });

            // 注册微服务模块
            this.registerMicroservices();
        }

        /**
         * 注册微服务模块
         */
        registerMicroservices() {
            if (!this.container) return;

            // 获取微服务配置
            const servicesConfig = window.OTA?.ServicesConfig || window.ServicesConfig;
            if (!servicesConfig || !servicesConfig.services) {
                console.warn('微服务配置不可用，跳过微服务注册');
                return;
            }

            // 按优先级排序服务
            const sortedServices = Object.entries(servicesConfig.services)
                .filter(([_, config]) => config.enabled)
                .sort(([, a], [, b]) => (a.priority || 999) - (b.priority || 999));

            // 注册每个微服务
            sortedServices.forEach(([serviceName, config]) => {
                this.registerMicroservice(serviceName, config);
            });

            console.log(`✅ 已注册 ${sortedServices.length} 个微服务`);
        }

        /**
         * 注册单个微服务
         * @param {string} serviceName - 服务名称
         * @param {Object} config - 服务配置
         */
        registerMicroservice(serviceName, config) {
            if (!this.container) return;

            const serviceFactory = () => {
                try {
                    // 检查服务是否已加载
                    const service = window.OTA?.services?.[serviceName] || window[serviceName];
                    if (!service) {
                        throw new Error(`微服务 ${serviceName} 未加载，请检查 ${config.path} 是否正确引入`);
                    }

                    // 检查依赖（宽容模式，不阻止服务注册）
                    if (config.dependencies && config.dependencies.length > 0) {
                        const missingDeps = config.dependencies.filter(dep => {
                            try {
                                // 使用hasService检查而不是getService，避免触发初始化
                                return !this.hasService(dep);
                            } catch {
                                return true;
                            }
                        });

                        if (missingDeps.length > 0) {
                            console.warn(`微服务 ${serviceName} 的依赖 [${missingDeps.join(', ')}] 不可用，将在运行时检查`);
                        }
                    }

                    // 初始化服务
                    if (service.init && typeof service.init === 'function') {
                        const initResult = service.init();
                        if (initResult && typeof initResult.then === 'function') {
                            // 异步初始化
                            initResult.catch(error => {
                                console.error(`微服务 ${serviceName} 初始化失败:`, error);
                            });
                        }
                    }

                    return service;
                } catch (error) {
                    console.error(`注册微服务 ${serviceName} 失败:`, error);
                    return this.createFallbackService(serviceName, config);
                }
            };

            // 注册到容器
            this.container.register(serviceName, serviceFactory, {
                singleton: true,
                description: config.description || `微服务: ${serviceName}`,
                priority: config.priority || 999,
                dependencies: config.dependencies || []
            });

            // 添加到降级映射
            this.fallbackMap.set(serviceName, () => {
                return window.OTA?.services?.[serviceName] || window[serviceName];
            });
        }

        /**
         * 创建降级服务实例
         * @param {string} serviceName - 服务名称
         * @param {Object} config - 服务配置
         * @returns {Object} 降级服务实例
         */
        createFallbackService(serviceName, config) {
            console.warn(`创建 ${serviceName} 的降级服务实例`);

            return {
                init: () => Promise.resolve(),
                destroy: () => Promise.resolve(),
                getStatus: () => ({
                    name: serviceName,
                    initialized: false,
                    state: 'fallback',
                    error: 'Service not properly loaded',
                    version: '0.0.0'
                }),
                isAvailable: () => false
            };
        }

        /**
         * 注册管理器服务
         */
        registerManagerServices() {
            if (!this.container) return;

            // 注册微服务（替代传统管理器）
            // 检查是否已经存在于依赖容器中，避免重复注册
            if (!this.container.has('uiService')) {
                this.container.register('uiService', () => {
                    return window.OTA.services?.uiService || this.createUIService();
                });
            }

            this.container.register('utils', () => {
                return window.OTA.utils || window.utils || this.createUtils();
            });

            // 管理器服务
            this.container.register('formManager', () => {
                if (window.OTA.managers?.FormManager) {
                    const manager = new window.OTA.managers.FormManager();
                    manager.init();
                    return manager;
                }
                return window.OTA.formManager || window.formManager || this.createFormManager();
            });

            this.container.register('languageManager', () => {
                return window.OTA.languageManager || window.getLanguageManager?.() || this.createLanguageManager();
            });

            // 微服务注册将在后续阶段实现

            this.container.register('kimiService', () => {
                return window.OTA.kimiService || window.getKimiService?.() || this.createKimiService();
            });

            this.container.register('imageUploadManager', () => {
                return window.OTA.imageUploadManager || window.getImageUploadManager?.() || this.createImageUploadManager();
            });

            this.container.register('multiOrderManager', () => {
                return window.OTA.multiOrderManager || window.getMultiOrderManager?.() || this.createMultiOrderManager();
            });

            this.container.register('orderHistoryManager', () => {
                return window.OTA.orderHistoryManager || window.getOrderHistoryManager?.() || this.createOrderHistoryManager();
            });

            this.container.register('pagingServiceManager', () => {
                return window.OTA.pagingServiceManager || window.getPagingServiceManager?.() || this.createPagingServiceManager();
            });

            this.container.register('realtimeAnalysisManager', () => {
                return window.OTA.realtimeAnalysisManager || window.getRealtimeAnalysisManager?.() || this.createRealtimeAnalysisManager();
            });

            this.container.register('i18nManager', () => {
                return window.OTA.i18nManager || window.getI18nManager?.() || this.createI18nManager();
            });

            // P0优先级：核心架构服务
            this.container.register('apiKeyManager', () => {
                return window.OTA.apiKeyManager || window.getApiKeyManager?.() || this.createApiKeyManager();
            });

            this.container.register('warningManager', () => {
                return window.OTA.core?.warningManager || window.getWarningManager?.() || this.createWarningManager();
            });

            this.container.register('configManager', () => {
                // 优化查找顺序：先检查全局配置管理器，再检查gemini模块
                return window.OTA.configManager ||
                       window.OTA.core?.configManager ||
                       window.OTA.gemini?.core?.configManager || 
                       window.OTA.gemini?.core?.getConfigManager?.() ||
                       window.configManager ||
                       this.createConfigManager();
            });

            this.container.register('componentLifecycleManager', () => {
                return window.OTA.componentLifecycleManager || this.createComponentLifecycleManager();
            });

            this.container.register('unifiedDataManager', () => {
                return window.OTA.unifiedDataManager || this.createUnifiedDataManager();
            });
        }

        /**
         * 获取服务实例
         * @param {string} serviceName - 服务名称
         * @returns {any} 服务实例
         */
        getService(serviceName) {
            // 记录服务使用统计
            this.recordServiceUsage(serviceName);
            
            // 优先从依赖容器获取
            if (this.container && this.container.has(serviceName)) {
                try {
                    const service = this.container.get(serviceName);
                    this.recordServiceAccess(serviceName, 'container');
                    return service;
                } catch (error) {
                    console.warn(`从依赖容器获取 ${serviceName} 失败，尝试降级方案:`, error.message);
                    this.recordServiceAccess(serviceName, 'container_error');
                }
            }

            // 🗑️ 已简化fallback机制 - 减法重构优化
            // 移除复杂的多层降级逻辑，统一到容器注册机制
            if (this.fallbackMap.has(serviceName)) {
                const service = this.fallbackMap.get(serviceName)();
                this.recordServiceAccess(serviceName, 'fallback');

                // 简化警告机制
                if (!this.migrationWarnings.has(serviceName)) {
                    console.warn(`⚠️ 服务 ${serviceName} 使用fallback，建议注册到容器`);
                    this.migrationWarnings.add(serviceName);
                }

                return service;
            }

            // 🗑️ 已简化全局服务获取 - 减法重构优化
            // 移除复杂的嵌套路径查找和多层警告机制
            const globalService = window.OTA?.[serviceName] || window[serviceName];

            if (globalService) {
                this.recordServiceAccess(serviceName, 'global');
                console.warn(`⚠️ 服务 ${serviceName} 从全局获取，建议注册到容器`);
                return globalService;
            }

            this.recordServiceAccess(serviceName, 'not_found');

            throw new Error(`服务 ${serviceName} 未找到`);
        }

        /**
         * 检查服务是否可用
         * @param {string} serviceName - 服务名称
         * @returns {boolean}
         */
        hasService(serviceName) {
            try {
                // 首先检查容器中是否已注册
                if (this.container && this.container.has(serviceName)) {
                    return true;
                }

                // 检查降级映射
                if (this.fallbackMap.has(serviceName)) {
                    const fallbackFactory = this.fallbackMap.get(serviceName);
                    const service = fallbackFactory();
                    return !!service;
                }

                // 检查全局对象
                const globalService = window.OTA?.[serviceName] || window[serviceName];
                if (globalService) {
                    return true;
                }

                // 特殊处理嵌套路径
                if (serviceName === 'configManager') {
                    const nestedService = window.OTA?.gemini?.core?.configManager ||
                                         window.OTA?.gemini?.core?.getConfigManager?.();
                    return !!nestedService;
                }

                return false;
            } catch {
                return false;
            }
        }

        /**
         * 获取所有可用服务
         * @returns {string[]}
         */
        getAvailableServices() {
            const services = new Set();
            
            // 从依赖容器获取
            if (this.container) {
                this.container.getRegisteredServices().forEach(name => services.add(name));
            }
            
            // 从降级映射获取
            this.fallbackMap.forEach((_, name) => services.add(name));
            
            // 从全局获取
            Object.keys(window.OTA || {}).forEach(name => services.add(name));
            
            return Array.from(services);
        }

        /**
         * 创建默认的AppState实例
         */
        createAppState() {
            console.warn('创建默认AppState实例，建议使用正式的工厂函数');
            return new (window.AppState || class DefaultAppState {
                constructor() {
                    this.state = {};
                }
                get(path) { return path.split('.').reduce((obj, key) => obj?.[key], this.state); }
                set(path, value) { 
                    const keys = path.split('.');
                    const lastKey = keys.pop();
                    const target = keys.reduce((obj, key) => obj[key] = obj[key] || {}, this.state);
                    target[lastKey] = value;
                }
            })();
        }

        /**
         * 创建默认的Logger实例
         */
        createLogger() {
            // 移除警告日志：创建默认Logger实例的提示
            return {
                log: (message, level = 'info', data = null) => {
                    const levelStr = typeof level === 'string' ? level : 'info';
                    console.log(`[${levelStr.toUpperCase()}] ${message}`, data || '');
                },
                logError: (message, error) => {
                    console.error(`[ERROR] ${message}`, error);
                }
            };
        }

        /**
         * 创建默认的ApiService实例
         */
        createApiService() {
            // 移除警告日志：创建默认ApiService实例的提示
            return {
                isAvailable: () => false,
                createOrder: () => Promise.reject(new Error('ApiService not properly initialized'))
            };
        }

        /**
         * 创建默认的GeminiService实例
         */
        createGeminiService() {
            return {
                isAvailable: () => false,
                analyzeOrder: () => Promise.reject(new Error('GeminiService not properly initialized'))
            };
        }

        /**
         * 创建默认的UIService实例（微服务架构）
         */
        createUIService() {
            // 降低警告等级，仅在调试模式下显示
            if (window.OTA?.debug || window.DEBUG) {
                console.warn('创建默认UIService实例，建议使用正式的微服务');
            }
            return {
                init: () => Promise.resolve(),
                destroy: () => Promise.resolve(),
                getStatus: () => ({ name: 'UIService', initialized: false, state: 'fallback' }),
                isAvailable: () => false,
                showAlert: () => {},
                showError: () => {},
                showSuccess: () => {},
                showLoading: () => {},
                hideLoading: () => {},
                updateLoginUI: () => {}
            };
        }

        /**
         * 创建默认的UIManager实例（向后兼容）
         * @deprecated 使用createUIService替代
         */
        createUIManager() {
            console.warn('创建默认UIManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getManager: () => null
            };
        }

        /**
         * 创建默认的Utils实例
         */
        createUtils() {
            console.warn('创建默认Utils实例，建议使用正式的工厂函数');
            return {
                debounce: (fn, delay) => fn,
                throttle: (fn, delay) => fn,
                formatDate: (date) => date.toString()
            };
        }

        // Simple工具工厂方法已移除，将使用微服务架构替代

        /**
         * 创建默认的KimiService实例
         */
        createKimiService() {
            console.warn('创建默认KimiService实例，建议使用正式的工厂函数');
            return {
                isAvailable: () => false,
                analyzeOrder: () => Promise.reject(new Error('KimiService not properly initialized')),
                init: () => {}
            };
        }

        /**
         * 创建默认的ImageUploadManager实例
         */
        createImageUploadManager() {
            console.warn('创建默认ImageUploadManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                upload: () => Promise.reject(new Error('ImageUploadManager not properly initialized')),
                isAvailable: () => false
            };
        }


        /**
         * 创建默认的MultiOrderManager实例
         */
        createMultiOrderManager() {
            console.warn('创建默认MultiOrderManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                processMultipleOrders: () => Promise.reject(new Error('MultiOrderManager not properly initialized')),
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的OrderHistoryManager实例
         */
        createOrderHistoryManager() {
            console.warn('创建默认OrderHistoryManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getOrderHistory: () => Promise.reject(new Error('OrderHistoryManager not properly initialized')),
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的PagingServiceManager实例
         */
        createPagingServiceManager() {
            console.warn('创建默认PagingServiceManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                paginate: () => Promise.reject(new Error('PagingServiceManager not properly initialized')),
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的RealtimeAnalysisManager实例
         */
        createRealtimeAnalysisManager() {
            console.warn('创建默认RealtimeAnalysisManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                triggerRealtimeAnalysis: () => Promise.resolve([]),
                setRealtimeAnalysisEnabled: () => {},
                getRealtimeAnalysisStatus: () => ({ enabled: false, isAnalyzing: false }),
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的I18nManager实例
         */
        createI18nManager() {
            console.warn('创建默认I18nManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                t: (key) => key,
                getCurrentLanguage: () => 'en',
                setLanguage: (lang) => console.log('I18nManager: 设置语言', lang),
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的ApiKeyManager实例
         */
        createApiKeyManager() {
            console.warn('创建默认ApiKeyManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getApiKey: (service) => null,
                setApiKey: (service, key) => console.log('ApiKeyManager: 设置API密钥', service),
                hasApiKey: (service) => false,
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的WarningManager实例
         */
        createWarningManager() {
            console.warn('创建默认WarningManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                warn: (type, message, level, data) => console.warn(`[${level}] ${type}: ${message}`, data),
                isAvailable: () => false,
                getWarningStats: () => ({ total: 0, byType: {} })
            };
        }

        /**
         * 创建默认的ConfigManager实例
         */
        createConfigManager() {
            // 降低警告等级，仅在调试模式下显示
            if (window.OTA?.debug || window.DEBUG) {
                console.warn('创建默认ConfigManager实例，建议使用正式的工厂函数');
            }
            return {
                init: () => {},
                getConfig: (key, defaultValue) => defaultValue,
                setConfig: (key, value) => console.log('ConfigManager: 设置配置', key, value),
                hasConfig: (key) => false,
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的ComponentLifecycleManager实例
         */
        createComponentLifecycleManager() {
            console.warn('创建默认ComponentLifecycleManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                register: (component) => console.log('ComponentLifecycleManager: 注册组件', component),
                unregister: (component) => console.log('ComponentLifecycleManager: 注销组件', component),
                getComponents: () => [],
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的UnifiedDataManager实例
         */
        createUnifiedDataManager() {
            console.warn('创建默认UnifiedDataManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getData: (key) => null,
                setData: (key, value) => console.log('UnifiedDataManager: 设置数据', key, value),
                hasData: (key) => false,
                clearData: () => console.log('UnifiedDataManager: 清理数据'),
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的FormManager实例
         */
        createFormManager() {
            console.warn('创建默认FormManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                fillFormFromData: (data) => console.log('FormManager: 填充表单数据', data),
                collectFormData: () => ({}),
                getFormData: () => ({}),
                setLanguageSelection: (langIds) => console.log('FormManager: 设置语言选择', langIds),
                populateDropdowns: () => {},
                validateForm: () => true,
                resetForm: () => {},
                elements: {},
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的LanguageManager实例
         */
        createLanguageManager() {
            console.warn('创建默认LanguageManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getLanguages: () => [],
                getLanguage: (id) => null,
                setSelectedLanguages: (ids) => console.log('LanguageManager: 设置选中语言', ids),
                getSelectedLanguages: () => [],
                isAvailable: () => false
            };
        }

        /**
         * 记录服务使用统计
         * @param {string} serviceName - 服务名称
         */
        recordServiceUsage(serviceName) {
            if (!this.monitoringEnabled) return;
            
            const current = this.serviceUsageStats.get(serviceName) || 0;
            this.serviceUsageStats.set(serviceName, current + 1);
        }

        /**
         * 记录服务访问方式
         * @param {string} serviceName - 服务名称
         * @param {string} accessType - 访问类型: container, fallback, global, not_found, container_error
         */
        recordServiceAccess(serviceName, accessType) {
            if (!this.monitoringEnabled) return;
            
            if (!this.serviceAccessTimes.has(serviceName)) {
                this.serviceAccessTimes.set(serviceName, {
                    container: 0,
                    fallback: 0,
                    global: 0,
                    not_found: 0,
                    container_error: 0
                });
            }
            
            const stats = this.serviceAccessTimes.get(serviceName);
            stats[accessType] = (stats[accessType] || 0) + 1;
        }

        /**
         * 记录降级使用次数
         * @param {string} serviceName - 服务名称
         */
        recordFallbackUsage(serviceName) {
            if (!this.monitoringEnabled) return;
            
            const current = this.fallbackUsageCount.get(serviceName) || 0;
            this.fallbackUsageCount.set(serviceName, current + 1);
        }

        /**
         * 获取服务使用统计
         * @returns {Object}
         */
        getServiceUsageStats() {
            return {
                totalRequests: Array.from(this.serviceUsageStats.values()).reduce((sum, count) => sum + count, 0),
                serviceUsage: Object.fromEntries(this.serviceUsageStats),
                accessPatterns: Object.fromEntries(this.serviceAccessTimes),
                fallbackUsage: Object.fromEntries(this.fallbackUsageCount),
                topServices: Array.from(this.serviceUsageStats.entries())
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 10)
                    .map(([name, count]) => ({ name, count })),
                healthScore: this.calculateServiceHealthScore()
            };
        }

        /**
         * 计算服务健康分数
         * @returns {number} 0-100的分数
         */
        calculateServiceHealthScore() {
            const totalRequests = Array.from(this.serviceUsageStats.values()).reduce((sum, count) => sum + count, 0);
            if (totalRequests === 0) return 100;

            let containerRequests = 0;
            let fallbackRequests = 0;
            let globalRequests = 0;
            let errorRequests = 0;

            for (const [serviceName, stats] of this.serviceAccessTimes) {
                containerRequests += stats.container || 0;
                fallbackRequests += stats.fallback || 0;
                globalRequests += stats.global || 0;
                errorRequests += stats.container_error || 0;
                errorRequests += stats.not_found || 0;
            }

            const containerRatio = containerRequests / totalRequests;
            const fallbackRatio = fallbackRequests / totalRequests;
            const globalRatio = globalRequests / totalRequests;
            const errorRatio = errorRequests / totalRequests;

            // 健康分数计算：容器访问得满分，降级扣分，全局访问扣更多分，错误扣最多分
            const score = 100 * containerRatio + 
                         70 * fallbackRatio + 
                         50 * globalRatio + 
                         0 * errorRatio;

            return Math.round(score);
        }

        /**
         * 重置监控数据
         */
        resetMonitoringData() {
            this.serviceUsageStats.clear();
            this.serviceAccessTimes.clear();
            this.fallbackUsageCount.clear();
            console.log('🔄 监控数据已重置');
        }

        /**
         * 启用/禁用监控
         * @param {boolean} enabled - 是否启用
         */
        setMonitoringEnabled(enabled) {
            this.monitoringEnabled = enabled;
            console.log(`📊 服务监控已${enabled ? '启用' : '禁用'}`);
        }

        /**
         * 获取迁移状态报告
         * @returns {Object}
         */
        getMigrationReport() {
            const usageStats = this.getServiceUsageStats();

            return {
                totalServices: this.getAvailableServices().length,
                containerServices: this.container ? this.container.getRegisteredServices().length : 0,
                fallbackUsed: this.migrationWarnings.size,
                warnings: Array.from(this.migrationWarnings),
                usageStats: usageStats,
                recommendations: [
                    '将所有服务注册到依赖容器',
                    '替换直接的全局访问为 getService() 调用',
                    '移除双重获取模式 (window.OTA.xxx || window.xxx)',
                    `当前健康分数: ${usageStats.healthScore}/100`
                ]
            };
        }

        /**
         * 初始化所有微服务
         * @returns {Promise<Object>} 初始化结果
         */
        async initializeAllMicroservices() {
            const servicesConfig = window.OTA?.ServicesConfig || window.ServicesConfig;
            if (!servicesConfig || !servicesConfig.services) {
                return { success: false, error: '微服务配置不可用' };
            }

            const results = {
                success: true,
                initialized: [],
                failed: [],
                skipped: []
            };

            // 按优先级排序
            const sortedServices = Object.entries(servicesConfig.services)
                .filter(([_, config]) => config.enabled)
                .sort(([, a], [, b]) => (a.priority || 999) - (b.priority || 999));

            for (const [serviceName, config] of sortedServices) {
                try {
                    const service = this.getService(serviceName);

                    if (service && service.init && typeof service.init === 'function') {
                        const initResult = await service.init();
                        results.initialized.push({
                            name: serviceName,
                            priority: config.priority,
                            result: initResult
                        });
                        console.log(`✅ 微服务 ${serviceName} 初始化成功`);
                    } else {
                        results.skipped.push({
                            name: serviceName,
                            reason: 'No init method or service not available'
                        });
                    }
                } catch (error) {
                    results.failed.push({
                        name: serviceName,
                        error: error.message
                    });
                    results.success = false;
                    console.error(`❌ 微服务 ${serviceName} 初始化失败:`, error);
                }
            }

            return results;
        }

        /**
         * 销毁所有微服务
         * @returns {Promise<Object>} 销毁结果
         */
        async destroyAllMicroservices() {
            const servicesConfig = window.OTA?.ServicesConfig || window.ServicesConfig;
            if (!servicesConfig || !servicesConfig.services) {
                return { success: false, error: '微服务配置不可用' };
            }

            const results = {
                success: true,
                destroyed: [],
                failed: [],
                skipped: []
            };

            // 按优先级逆序销毁
            const sortedServices = Object.entries(servicesConfig.services)
                .filter(([_, config]) => config.enabled)
                .sort(([, a], [, b]) => (b.priority || 999) - (a.priority || 999));

            for (const [serviceName, config] of sortedServices) {
                try {
                    const service = this.getService(serviceName);

                    if (service && service.destroy && typeof service.destroy === 'function') {
                        const destroyResult = await service.destroy();
                        results.destroyed.push({
                            name: serviceName,
                            result: destroyResult
                        });
                        console.log(`✅ 微服务 ${serviceName} 销毁成功`);
                    } else {
                        results.skipped.push({
                            name: serviceName,
                            reason: 'No destroy method or service not available'
                        });
                    }
                } catch (error) {
                    results.failed.push({
                        name: serviceName,
                        error: error.message
                    });
                    results.success = false;
                    console.error(`❌ 微服务 ${serviceName} 销毁失败:`, error);
                }
            }

            return results;
        }

        /**
         * 获取所有微服务状态
         * @returns {Object} 服务状态报告
         */
        getMicroservicesStatus() {
            const servicesConfig = window.OTA?.ServicesConfig || window.ServicesConfig;
            if (!servicesConfig || !servicesConfig.services) {
                return { error: '微服务配置不可用' };
            }

            const status = {
                total: 0,
                ready: 0,
                failed: 0,
                uninitialized: 0,
                services: {}
            };

            Object.entries(servicesConfig.services).forEach(([serviceName, config]) => {
                if (!config.enabled) return;

                status.total++;

                try {
                    const service = this.getService(serviceName);
                    const serviceStatus = service?.getStatus?.() || {
                        name: serviceName,
                        initialized: false,
                        state: 'unknown'
                    };

                    status.services[serviceName] = {
                        ...serviceStatus,
                        config: {
                            priority: config.priority,
                            dependencies: config.dependencies,
                            description: config.description
                        }
                    };

                    // 统计状态
                    if (serviceStatus.state === 'ready') {
                        status.ready++;
                    } else if (serviceStatus.state === 'error' || serviceStatus.state === 'fallback') {
                        status.failed++;
                    } else {
                        status.uninitialized++;
                    }

                } catch (error) {
                    status.failed++;
                    status.services[serviceName] = {
                        name: serviceName,
                        initialized: false,
                        state: 'error',
                        error: error.message,
                        config: {
                            priority: config.priority,
                            dependencies: config.dependencies,
                            description: config.description
                        }
                    };
                }
            });

            return status;
        }

        /**
         * 健康检查所有微服务
         * @returns {Promise<Object>} 健康检查结果
         */
        async healthCheckMicroservices() {
            const servicesConfig = window.OTA?.ServicesConfig || window.ServicesConfig;
            if (!servicesConfig || !servicesConfig.services) {
                return { success: false, error: '微服务配置不可用' };
            }

            const results = {
                timestamp: new Date().toISOString(),
                healthy: [],
                unhealthy: [],
                unknown: []
            };

            for (const [serviceName, config] of Object.entries(servicesConfig.services)) {
                if (!config.enabled) continue;

                try {
                    const service = this.getService(serviceName);

                    if (service && service.getStatus && typeof service.getStatus === 'function') {
                        const status = service.getStatus();

                        if (status.state === 'ready' && status.initialized) {
                            results.healthy.push({
                                name: serviceName,
                                status: status
                            });
                        } else {
                            results.unhealthy.push({
                                name: serviceName,
                                status: status,
                                reason: `State: ${status.state}, Initialized: ${status.initialized}`
                            });
                        }
                    } else {
                        results.unknown.push({
                            name: serviceName,
                            reason: 'No getStatus method available'
                        });
                    }
                } catch (error) {
                    results.unhealthy.push({
                        name: serviceName,
                        error: error.message
                    });
                }
            }

            return results;
        }
    }

    // 创建全局服务定位器实例
    const serviceLocator = new ServiceLocator();

    // 等待依赖容器准备就绪后初始化
    if (window.OTA.container) {
        serviceLocator.init(window.OTA.container);
    } else {
        // 延迟初始化
        setTimeout(() => {
            if (window.OTA.container) {
                serviceLocator.init(window.OTA.container);
            }
        }, 100);
    }

    // 暴露到OTA命名空间
    window.OTA.serviceLocator = serviceLocator;

    // 提供统一的服务获取函数
    window.OTA.getService = function(serviceName) {
        return serviceLocator.getService(serviceName);
    };

    // 向后兼容的全局函数
    window.getService = window.OTA.getService;

    // 暴露监控功能到全局
    window.OTA.getServiceUsageStats = function() {
        return serviceLocator.getServiceUsageStats();
    };

    window.OTA.getServiceMigrationReport = function() {
        return serviceLocator.getMigrationReport();
    };

    window.OTA.resetServiceMonitoring = function() {
        return serviceLocator.resetMonitoringData();
    };

    window.OTA.setServiceMonitoringEnabled = function(enabled) {
        return serviceLocator.setMonitoringEnabled(enabled);
    };

    // 微服务生命周期管理API
    window.OTA.initializeAllMicroservices = function() {
        return serviceLocator.initializeAllMicroservices();
    };

    window.OTA.destroyAllMicroservices = function() {
        return serviceLocator.destroyAllMicroservices();
    };

    window.OTA.getMicroservicesStatus = function() {
        return serviceLocator.getMicroservicesStatus();
    };

    window.OTA.healthCheckMicroservices = function() {
        return serviceLocator.healthCheckMicroservices();
    };

    // 提供便捷的服务获取函数（仅提供没有专门文件的服务）
    // 注意：有专门服务文件的函数（如getAppState, getLogger, getAPIService等）由各自文件负责定义
    window.getApiService = () => serviceLocator.getService('apiService'); // getApiService是getAPIService的别名
    window.getGeminiService = () => serviceLocator.getService('geminiService');
    window.getUIManager = () => serviceLocator.getService('uiManager');
    window.getUtils = () => serviceLocator.getService('utils');
    window.getImageUploadManager = () => serviceLocator.getService('imageUploadManager');
    window.getMultiOrderManager = () => serviceLocator.getService('multiOrderManager');
    window.getOrderHistoryManager = () => serviceLocator.getService('orderHistoryManager');
    window.getPagingServiceManager = () => serviceLocator.getService('pagingServiceManager');
    window.getKimiService = () => serviceLocator.getService('kimiService');
    window.getFormManager = () => serviceLocator.getService('formManager');
    window.getLanguageManager = () => serviceLocator.getService('languageManager');

    // 注册服务定位器到Registry
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('serviceLocator', serviceLocator, '@SERVICE_LOCATOR');
    }

    console.log('✅ 服务定位器已加载');

})();
