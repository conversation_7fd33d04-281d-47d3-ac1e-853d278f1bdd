/**
 * 状态服务模块 - 微服务架构
 * 负责UI状态管理、主题控制和状态展示
 * 从StateManager转换而来，采用函数式编程风格
 * 
 * <AUTHOR> System
 * @created 2025-01-01
 */

(function() {
    'use strict';

    // 确保全局命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.services = window.OTA.services || {};

    /**
     * 获取依赖服务的辅助函数
     */
    function getLogger() {
        return window.OTA?.logger || window.getLogger?.() || console;
    }

    function getAppState() {
        return window.OTA?.appState || window.appState;
    }

    /**
     * 状态服务状态
     */
    let serviceState = {
        initialized: false,
        appState: null,
        uiElements: {},
        stateListeners: new Map()
    };

    /**
     * 缓存UI元素
     */
    function cacheUIElements() {
        serviceState.uiElements = {
            themeToggle: document.getElementById('themeToggle'),
            geminiStatus: document.getElementById('geminiStatus'),
            connectionStatus: document.getElementById('connectionStatus'),
            loginStatus: document.getElementById('loginStatus'),
            userInfo: document.getElementById('userInfo')
        };

        getLogger().log('状态服务UI元素缓存完成', 'info', {
            found: Object.values(serviceState.uiElements).filter(Boolean).length,
            total: Object.keys(serviceState.uiElements).length
        });
    }

    /**
     * 设置主题切换功能
     */
    function setupThemeToggle() {
        if (serviceState.uiElements.themeToggle) {
            const themeToggleHandler = () => toggleTheme();
            serviceState.uiElements.themeToggle.addEventListener('click', themeToggleHandler);
            
            // 存储监听器引用以便清理
            serviceState.stateListeners.set('themeToggle', themeToggleHandler);
            
            // 初始化主题图标
            updateThemeIcon();
        }
    }

    /**
     * 设置状态监听器
     */
    function setupStateListeners() {
        if (serviceState.appState) {
            // 监听主题变化
            const themeChangeHandler = () => updateThemeIcon();
            serviceState.appState.on('config.theme', themeChangeHandler);
            serviceState.stateListeners.set('config.theme', themeChangeHandler);

            // 监听登录状态变化
            const loginChangeHandler = () => updateLoginStatus();
            serviceState.appState.on('auth.isLoggedIn', loginChangeHandler);
            serviceState.stateListeners.set('auth.isLoggedIn', loginChangeHandler);

            // 监听连接状态变化
            const connectionChangeHandler = () => updateConnectionStatus();
            serviceState.appState.on('system.connected', connectionChangeHandler);
            serviceState.stateListeners.set('system.connected', connectionChangeHandler);
        }
    }

    /**
     * 切换主题
     */
    function toggleTheme() {
        if (!serviceState.appState) return;

        const currentTheme = serviceState.appState.get('config.theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        // 更新AppState
        serviceState.appState.set('config.theme', newTheme);
        
        // 应用主题到DOM
        document.documentElement.setAttribute('data-theme', newTheme);
        
        // 保存到localStorage
        localStorage.setItem('ota_theme_preference', newTheme);
        
        getLogger().log('主题已切换', 'info', { theme: newTheme });
    }

    /**
     * 更新主题图标
     */
    function updateThemeIcon() {
        if (!serviceState.uiElements.themeToggle || !serviceState.appState) return;

        const currentTheme = serviceState.appState.get('config.theme') || 'light';
        const icon = currentTheme === 'light' ? '🌙' : '☀️';
        const title = currentTheme === 'light' ? '切换到深色模式' : '切换到浅色模式';
        
        serviceState.uiElements.themeToggle.textContent = icon;
        serviceState.uiElements.themeToggle.title = title;
    }

    /**
     * 更新Gemini状态
     * @param {Object} status - 状态信息
     */
    function updateGeminiStatus(status = null) {
        if (!serviceState.uiElements.geminiStatus) return;

        let statusInfo;
        if (status) {
            statusInfo = status;
        } else {
            // 从服务获取状态
            const geminiService = window.OTA?.geminiService || window.geminiService;
            statusInfo = geminiService ? {
                available: geminiService.isAvailable?.() || false,
                analyzing: false
            } : { available: false, analyzing: false };
        }

        const statusText = statusInfo.analyzing ? '分析中...' :
                          statusInfo.available ? '就绪' : '不可用';
        const statusClass = statusInfo.analyzing ? 'status-processing' :
                           statusInfo.available ? 'status-ready' : 'status-unavailable';

        serviceState.uiElements.geminiStatus.textContent = statusText;
        serviceState.uiElements.geminiStatus.className = `status-indicator ${statusClass}`;
    }

    /**
     * 更新连接状态
     */
    function updateConnectionStatus() {
        if (!serviceState.uiElements.connectionStatus || !serviceState.appState) return;

        const connected = serviceState.appState.get('system.connected') || false;
        const statusText = connected ? '已连接' : '未连接';
        const statusClass = connected ? 'status-connected' : 'status-disconnected';

        serviceState.uiElements.connectionStatus.textContent = statusText;
        serviceState.uiElements.connectionStatus.className = `status-indicator ${statusClass}`;
    }

    /**
     * 更新登录状态
     */
    function updateLoginStatus() {
        if (!serviceState.appState) return;

        const isLoggedIn = serviceState.appState.get('auth.isLoggedIn') || false;
        const user = serviceState.appState.get('auth.user');

        // 更新登录状态指示器
        if (serviceState.uiElements.loginStatus) {
            const statusText = isLoggedIn ? '已登录' : '未登录';
            const statusClass = isLoggedIn ? 'status-logged-in' : 'status-logged-out';
            
            serviceState.uiElements.loginStatus.textContent = statusText;
            serviceState.uiElements.loginStatus.className = `status-indicator ${statusClass}`;
        }

        // 更新用户信息显示
        if (serviceState.uiElements.userInfo && isLoggedIn && user) {
            // 修复：只更新currentUser元素，不覆盖整个userInfo内容
            const currentUserElement = document.getElementById('currentUser');
            if (currentUserElement) {
                currentUserElement.textContent = user.name || user.username || '用户';
            }
            serviceState.uiElements.userInfo.style.display = 'inline';
        } else if (serviceState.uiElements.userInfo) {
            serviceState.uiElements.userInfo.style.display = 'none';
        }

        // 触发登录状态更新事件
        document.dispatchEvent(new CustomEvent('loginStatusUpdated', {
            detail: { isLoggedIn, user }
        }));
    }

    /**
     * 更新所有UI状态
     */
    function updateAllUIStatus() {
        updateThemeIcon();
        updateGeminiStatus();
        updateConnectionStatus();
        updateLoginStatus();
        getLogger().log('所有UI状态已更新', 'info');
    }

    /**
     * 设置Gemini分析状态
     * @param {boolean} analyzing - 是否正在分析
     */
    function setGeminiAnalyzing(analyzing) {
        updateGeminiStatus({
            available: true,
            analyzing
        });
    }

    /**
     * 显示状态消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     * @param {number} duration - 显示时长
     */
    function showStatusMessage(message, type = 'info', duration = 3000) {
        // 使用UIService显示消息
        const uiService = window.OTA?.services?.uiService || window.uiService;
        if (uiService && uiService.showAlert) {
            uiService.showAlert(message, type, duration);
        } else {
            // 降级到控制台
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    /**
     * 获取当前状态快照
     * @returns {Object} 状态快照
     */
    function getStateSnapshot() {
        if (!serviceState.appState) return {};

        return {
            theme: serviceState.appState.get('config.theme'),
            isLoggedIn: serviceState.appState.get('auth.isLoggedIn'),
            connected: serviceState.appState.get('system.connected'),
            user: serviceState.appState.get('auth.user'),
            errors: serviceState.appState.get('system.errors') || []
        };
    }

    /**
     * 监听状态变化
     * @param {string} path - 状态路径
     * @param {Function} callback - 回调函数
     */
    function onStateChange(path, callback) {
        if (serviceState.appState && serviceState.appState.on) {
            serviceState.appState.on(path, callback);
            
            // 存储监听器引用
            const listeners = serviceState.stateListeners.get(path) || [];
            listeners.push(callback);
            serviceState.stateListeners.set(path, listeners);
        }
    }

    /**
     * 取消状态监听
     * @param {string} path - 状态路径
     * @param {Function} callback - 回调函数
     */
    function offStateChange(path, callback) {
        if (serviceState.appState && serviceState.appState.off) {
            serviceState.appState.off(path, callback);
            
            // 从监听器引用中移除
            const listeners = serviceState.stateListeners.get(path) || [];
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
                serviceState.stateListeners.set(path, listeners);
            }
        }
    }

    /**
     * 重置UI状态
     */
    function resetUIState() {
        // 重置主题到默认
        if (serviceState.appState) {
            serviceState.appState.set('config.theme', 'light');
        }
        document.documentElement.setAttribute('data-theme', 'light');
        localStorage.setItem('ota_theme_preference', 'light');

        // 更新所有UI
        updateAllUIStatus();
        
        getLogger().log('UI状态已重置', 'info');
    }

    /**
     * 获取状态统计
     * @returns {Object} 状态统计信息
     */
    function getStatusStats() {
        return {
            initialized: serviceState.initialized,
            hasAppState: !!serviceState.appState,
            elementsFound: Object.values(serviceState.uiElements).filter(Boolean).length,
            totalElements: Object.keys(serviceState.uiElements).length,
            listenersCount: serviceState.stateListeners.size,
            currentState: getStateSnapshot()
        };
    }

    /**
     * 清理所有监听器
     */
    function cleanupListeners() {
        // 清理DOM事件监听器
        if (serviceState.uiElements.themeToggle && serviceState.stateListeners.has('themeToggle')) {
            const handler = serviceState.stateListeners.get('themeToggle');
            serviceState.uiElements.themeToggle.removeEventListener('click', handler);
        }

        // 清理状态监听器
        if (serviceState.appState) {
            serviceState.stateListeners.forEach((listeners, path) => {
                if (Array.isArray(listeners)) {
                    listeners.forEach(callback => {
                        serviceState.appState.off(path, callback);
                    });
                } else if (typeof listeners === 'function') {
                    serviceState.appState.off(path, listeners);
                }
            });
        }

        serviceState.stateListeners.clear();
    }

    /**
     * 状态服务对象 - 微服务接口
     */
    const StateService = {
        /**
         * 初始化服务
         */
        init() {
            if (serviceState.initialized) {
                getLogger().log('状态服务已经初始化', 'warning');
                return Promise.resolve();
            }

            try {
                serviceState.appState = getAppState();
                cacheUIElements();
                setupThemeToggle();
                setupStateListeners();
                updateAllUIStatus();
                
                serviceState.initialized = true;
                getLogger().log('状态服务初始化完成', 'success');
                return Promise.resolve();
            } catch (error) {
                getLogger().log('状态服务初始化失败', 'error', { error: error.message });
                return Promise.reject(error);
            }
        },

        /**
         * 销毁服务
         */
        destroy() {
            cleanupListeners();
            
            serviceState.initialized = false;
            serviceState.appState = null;
            serviceState.uiElements = {};
            
            getLogger().log('状态服务已销毁', 'info');
            return Promise.resolve();
        },

        /**
         * 获取服务状态
         */
        getStatus() {
            return {
                name: 'StateService',
                initialized: serviceState.initialized,
                state: serviceState.initialized ? 'ready' : 'uninitialized',
                stats: getStatusStats(),
                version: '1.0.0'
            };
        },

        // 公共API方法
        updateAllUIStatus,
        updateGeminiStatus,
        updateConnectionStatus,
        updateLoginStatus,
        updateThemeIcon,
        toggleTheme,
        setGeminiAnalyzing,
        showStatusMessage,
        getStateSnapshot,
        onStateChange,
        offStateChange,
        resetUIState,
        getStatusStats,
        
        // 别名方法
        updateUI: updateAllUIStatus,
        isAvailable: () => serviceState.initialized && !!serviceState.appState
    };

    // 注册到全局命名空间
    window.OTA.services.stateService = StateService;

    // 向后兼容性支持
    window.stateService = StateService;

    // 日志记录
    getLogger().log('状态服务模块已加载', 'info');

})();
