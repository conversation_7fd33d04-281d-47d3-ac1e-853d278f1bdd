/**
 * 配置驱动管理器 - 微服务架构
 * 基于配置文件动态管理服务的生命周期和行为
 * 实现配置驱动的服务管理模式
 * 
 * <AUTHOR> System
 * @created 2025-01-01
 */

(function() {
    'use strict';

    // 确保全局命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.core = window.OTA.core || {};

    /**
     * 获取依赖服务的辅助函数
     */
    function getLogger() {
        return window.OTA?.logger || window.getLogger?.() || console;
    }

    function getServiceLocator() {
        return window.OTA?.serviceLocator;
    }

    /**
     * 配置驱动管理器类
     */
    class ConfigDrivenManager {
        constructor() {
            this.initialized = false;
            this.configCache = new Map();
            this.serviceConfigs = new Map();
            this.runtimeConfigs = new Map();
            this.configWatchers = new Map();
            this.logger = getLogger();
        }

        /**
         * 初始化配置驱动管理器
         * @returns {Promise<void>}
         */
        async init() {
            if (this.initialized) {
                this.logger.log('配置驱动管理器已经初始化', 'warning');
                return;
            }

            try {
                // 加载基础配置
                await this.loadBaseConfigurations();
                
                // 加载服务配置
                await this.loadServiceConfigurations();
                
                // 设置配置监听器
                this.setupConfigWatchers();
                
                this.initialized = true;
                this.logger.log('配置驱动管理器初始化完成', 'success');
            } catch (error) {
                this.logger.log('配置驱动管理器初始化失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 加载基础配置
         * @returns {Promise<void>}
         */
        async loadBaseConfigurations() {
            // 加载微服务配置
            const servicesConfig = window.OTA?.ServicesConfig || window.ServicesConfig;
            if (servicesConfig) {
                this.configCache.set('services', servicesConfig);
                this.logger.log('微服务配置已加载', 'info');
            }

            // 加载API接口配置
            const interfacesConfig = window.OTA?.interfaces;
            if (interfacesConfig) {
                this.configCache.set('interfaces', interfacesConfig);
                this.logger.log('API接口配置已加载', 'info');
            }

            // 加载统一配置中心
            const configCenter = window.OTA?.configCenter;
            if (configCenter) {
                this.configCache.set('configCenter', configCenter);
                this.logger.log('统一配置中心已连接', 'info');
            }
        }

        /**
         * 加载服务配置
         * @returns {Promise<void>}
         */
        async loadServiceConfigurations() {
            const servicesConfig = this.configCache.get('services');
            if (!servicesConfig || !servicesConfig.services) {
                throw new Error('微服务配置不可用');
            }

            // 为每个服务加载配置
            Object.entries(servicesConfig.services).forEach(([serviceName, config]) => {
                this.serviceConfigs.set(serviceName, {
                    ...config,
                    loadedAt: new Date().toISOString(),
                    status: 'configured'
                });
            });

            this.logger.log(`已加载 ${this.serviceConfigs.size} 个服务配置`, 'info');
        }

        /**
         * 设置配置监听器
         */
        setupConfigWatchers() {
            // 监听配置中心变化
            const configCenter = this.configCache.get('configCenter');
            if (configCenter && typeof configCenter.onConfigChange === 'function') {
                configCenter.onConfigChange((key, newValue, oldValue) => {
                    this.handleConfigChange(key, newValue, oldValue);
                });
            }

            // 监听服务状态变化
            if (window.OTA?.appState) {
                window.OTA.appState.addListener('services', (newState) => {
                    this.handleServiceStateChange(newState);
                });
            }
        }

        /**
         * 处理配置变化
         * @param {string} key - 配置键
         * @param {any} newValue - 新值
         * @param {any} oldValue - 旧值
         */
        handleConfigChange(key, newValue, oldValue) {
            this.logger.log(`配置变化: ${key}`, 'info', { newValue, oldValue });

            // 通知相关服务
            const watchers = this.configWatchers.get(key) || [];
            watchers.forEach(watcher => {
                try {
                    watcher(newValue, oldValue);
                } catch (error) {
                    this.logger.log(`配置监听器执行失败: ${key}`, 'error', { error: error.message });
                }
            });
        }

        /**
         * 处理服务状态变化
         * @param {Object} newState - 新状态
         */
        handleServiceStateChange(newState) {
            this.logger.log('服务状态变化', 'info', { newState });
            
            // 更新运行时配置
            this.updateRuntimeConfigs(newState);
        }

        /**
         * 获取服务配置
         * @param {string} serviceName - 服务名称
         * @returns {Object|null} 服务配置
         */
        getServiceConfig(serviceName) {
            return this.serviceConfigs.get(serviceName) || null;
        }

        /**
         * 更新服务配置
         * @param {string} serviceName - 服务名称
         * @param {Object} config - 新配置
         * @returns {boolean} 是否成功
         */
        updateServiceConfig(serviceName, config) {
            try {
                const existingConfig = this.serviceConfigs.get(serviceName) || {};
                const updatedConfig = {
                    ...existingConfig,
                    ...config,
                    updatedAt: new Date().toISOString()
                };

                this.serviceConfigs.set(serviceName, updatedConfig);
                
                // 通知服务配置变化
                this.notifyServiceConfigChange(serviceName, updatedConfig, existingConfig);
                
                this.logger.log(`服务配置已更新: ${serviceName}`, 'info');
                return true;
            } catch (error) {
                this.logger.log(`更新服务配置失败: ${serviceName}`, 'error', { error: error.message });
                return false;
            }
        }

        /**
         * 通知服务配置变化
         * @param {string} serviceName - 服务名称
         * @param {Object} newConfig - 新配置
         * @param {Object} oldConfig - 旧配置
         */
        notifyServiceConfigChange(serviceName, newConfig, oldConfig) {
            const serviceLocator = getServiceLocator();
            if (!serviceLocator) return;

            try {
                const service = serviceLocator.getService(serviceName);
                if (service && typeof service.configure === 'function') {
                    service.configure(newConfig);
                    this.logger.log(`服务 ${serviceName} 配置已应用`, 'info');
                }
            } catch (error) {
                this.logger.log(`应用服务配置失败: ${serviceName}`, 'error', { error: error.message });
            }
        }

        /**
         * 获取运行时配置
         * @param {string} key - 配置键
         * @returns {any} 配置值
         */
        getRuntimeConfig(key) {
            return this.runtimeConfigs.get(key);
        }

        /**
         * 设置运行时配置
         * @param {string} key - 配置键
         * @param {any} value - 配置值
         */
        setRuntimeConfig(key, value) {
            const oldValue = this.runtimeConfigs.get(key);
            this.runtimeConfigs.set(key, value);
            
            // 触发配置变化事件
            this.handleConfigChange(`runtime.${key}`, value, oldValue);
        }

        /**
         * 更新运行时配置
         * @param {Object} newState - 新状态
         */
        updateRuntimeConfigs(newState) {
            // 根据服务状态更新运行时配置
            Object.entries(newState).forEach(([key, value]) => {
                this.setRuntimeConfig(key, value);
            });
        }

        /**
         * 添加配置监听器
         * @param {string} key - 配置键
         * @param {Function} watcher - 监听器函数
         */
        addConfigWatcher(key, watcher) {
            if (!this.configWatchers.has(key)) {
                this.configWatchers.set(key, []);
            }
            this.configWatchers.get(key).push(watcher);
        }

        /**
         * 移除配置监听器
         * @param {string} key - 配置键
         * @param {Function} watcher - 监听器函数
         */
        removeConfigWatcher(key, watcher) {
            const watchers = this.configWatchers.get(key) || [];
            const index = watchers.indexOf(watcher);
            if (index > -1) {
                watchers.splice(index, 1);
            }
        }

        /**
         * 获取所有配置
         * @returns {Object} 所有配置
         */
        getAllConfigs() {
            return {
                base: Object.fromEntries(this.configCache),
                services: Object.fromEntries(this.serviceConfigs),
                runtime: Object.fromEntries(this.runtimeConfigs),
                watchers: Array.from(this.configWatchers.keys())
            };
        }

        /**
         * 验证配置完整性
         * @returns {Object} 验证结果
         */
        validateConfigurations() {
            const result = {
                valid: true,
                errors: [],
                warnings: [],
                timestamp: new Date().toISOString()
            };

            // 验证基础配置
            if (!this.configCache.has('services')) {
                result.valid = false;
                result.errors.push('缺少微服务配置');
            }

            // 验证服务配置
            this.serviceConfigs.forEach((config, serviceName) => {
                if (!config.enabled) {
                    result.warnings.push(`服务 ${serviceName} 已禁用`);
                }
                
                if (!config.path) {
                    result.errors.push(`服务 ${serviceName} 缺少路径配置`);
                    result.valid = false;
                }
            });

            return result;
        }

        /**
         * 重置配置
         */
        reset() {
            this.configCache.clear();
            this.serviceConfigs.clear();
            this.runtimeConfigs.clear();
            this.configWatchers.clear();
            this.initialized = false;
            this.logger.log('配置驱动管理器已重置', 'info');
        }

        /**
         * 获取状态
         * @returns {Object} 管理器状态
         */
        getStatus() {
            return {
                initialized: this.initialized,
                configCount: this.configCache.size,
                serviceConfigCount: this.serviceConfigs.size,
                runtimeConfigCount: this.runtimeConfigs.size,
                watcherCount: this.configWatchers.size
            };
        }
    }

    // 创建全局实例
    const configDrivenManager = new ConfigDrivenManager();

    // 暴露到全局命名空间
    window.OTA.core.configDrivenManager = configDrivenManager;

    // 提供便捷的全局函数
    window.OTA.getServiceConfig = function(serviceName) {
        return configDrivenManager.getServiceConfig(serviceName);
    };

    window.OTA.updateServiceConfig = function(serviceName, config) {
        return configDrivenManager.updateServiceConfig(serviceName, config);
    };

    window.OTA.validateConfigurations = function() {
        return configDrivenManager.validateConfigurations();
    };

    // 日志记录
    getLogger().log('配置驱动管理器已加载', 'info');

})();
