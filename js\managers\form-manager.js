/**
 * 表单管理器模块 (优化版)
 * 负责表单数据处理、验证和UI交互
 * 保留核心功能，去除过度复杂的部分
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    /**
     * 表单管理器类 (优化版)
     * 保留核心表单处理功能，简化实现逻辑
     */
    class FormManager {
        constructor() {
            this.elements = {};
            this.fieldMappings = this.createFieldMappings();
            this.initialized = false;
            this.validationRules = this.createValidationRules();
        }

        /**
         * 创建字段映射关系 (统一化版本)
         * 确保Gemini返回、表单填充、API字段规格完全一致
         */
        createFieldMappings() {
            return {
                // === API标准字段 → HTML元素ID (基于GoMyHire API文档) ===
                // 基础信息字段
                'customer_name': 'customerName',
                'customer_contact': 'customerContact',  // API: customer_contact → HTML: customerContact
                'customer_email': 'customerEmail',
                'sub_category_id': 'subCategoryId',
                'car_type_id': 'carTypeId',
                'driving_region_id': 'drivingRegionId',
                'incharge_by_backend_user_id': 'inchargeByBackendUserId',
                
                // 日期和时间字段 - 统一为YYYY-MM-DD格式
                'date': 'pickupDate',
                'time': 'pickupTime',
                'pickup_date': 'pickupDate',
                'pickup_time': 'pickupTime',
                'return_date': 'returnDate',
                'return_time': 'returnTime',
                
                // 地点字段
                'pickup': 'pickup',
                'destination': 'dropoff',
                'pickup_location': 'pickup',
                'destination_location': 'dropoff',
                'dropoff_location': 'dropoff',  // 兼容旧格式
                
                // 价格和货币 (保留数值类型)
                'ota_price': 'otaPrice',
                'price': 'otaPrice',  // Gemini返回的price映射到ota_price
                'currency': 'currency',
                
                // 语言选择 - 统一为数组格式 [2,4] 转 {"0":"2","1":"4"}
                'languages_id_array': 'languagesIdArray',
                'languages': 'languagesIdArray',
                
                // 数量字段 (数值类型)
                'passenger_number': 'passengerCount',
                'passenger_count': 'passengerCount',
                'pax': 'passengerCount',  // 兼容旧格式
                'luggage_number': 'luggageCount',
                'luggage_count': 'luggageCount',
                
                // 航班信息
                'flight_info': 'flightInfo',
                'flight_number': 'flightInfo',
                'flight_no': 'flightInfo',
                
                // 其他信息
                'extra_requirement': 'extraRequirement',
                'special_requirements': 'extraRequirement',
                'special_request': 'extraRequirement',
                'remarks': 'extraRequirement',
                'notes': 'extraRequirement',
                
                // OTA相关
                'ota_reference_number': 'otaReferenceNumber',
                'ota_reference': 'otaReferenceNumber',
                'booking_reference': 'otaReferenceNumber',
                'order_number': 'otaReferenceNumber',
                
                // 费用相关
                'driver_fee': 'driverFee',
                'driver_collect': 'driverCollect',
                'meet_and_greet': 'meetAndGreet',
                'baby_chair': 'babyChair',
                'tour_guide': 'tourGuide'
            };
        }

        /**
         * 创建验证规则 (基于HTML元素ID)
         */
        createValidationRules() {
            return {
                customerName: { required: true, minLength: 2 },
                customerContact: { required: true, pattern: /^[\d\s\-\+\(\)]+$/ },
                customerEmail: { required: false, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
                pickupDate: { required: true, type: 'date' },
                pickupTime: { required: true, type: 'time' },
                otaPrice: { required: false, type: 'number', min: 0 },
                passengerCount: { required: false, type: 'number', min: 1, max: 50 }
            };
        }

        /**
         * 初始化表单管理器
         */
        init() {
            if (this.initialized) {
                getLogger().log('表单管理器已经初始化', 'warning');
                return;
            }

            this.cacheFormElements();
            this.setupBasicValidation();
            this.setupLanguageHandling();
            this.initialized = true;
            getLogger().log('表单管理器初始化完成', 'success');
        }

        /**
         * 缓存表单元素 (匹配HTML实际ID)
         */
        cacheFormElements() {
            const elementIds = [
                'customerName', 'customerContact', 'customerEmail', 'subCategoryId',
                'carTypeId', 'drivingRegionId', 'inchargeByBackendUserId', 'pickupDate',
                'pickupTime', 'returnDate', 'returnTime', 'pickup',
                'dropoff', 'pickupAddress', 'returnAddress', 'pickupLandmark',
                'returnLandmark', 'otaPrice', 'currency', 'languagesIdArray',
                'extraRequirement', 'passengerCount', 'luggageCount',
                'flightInfo', 'otaReferenceNumber', 'driverFee', 'driverCollect',
                'babyChair', 'tourGuide', 'meetAndGreet'
            ];

            elementIds.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    this.elements[id] = element;
                }
            });

            // 缓存特殊元素
            this.elements.languagesCheckboxes = document.querySelectorAll('input[name="languagesIdArray"]');
            this.elements.submitButton = document.getElementById('createOrder');
            this.elements.form = document.getElementById('orderForm');

            getLogger().log('表单元素缓存完成', 'info', {
                cached: Object.keys(this.elements).length
            });
        }

        /**
         * 设置基础验证
         */
        setupBasicValidation() {
            Object.keys(this.validationRules).forEach(fieldName => {
                const element = this.elements[fieldName];
                if (element) {
                    element.addEventListener('blur', () => this.validateField(fieldName));
                    element.addEventListener('input', () => this.clearFieldError(fieldName));
                }
            });
        }

        /**
         * 设置语言处理
         */
        setupLanguageHandling() {
            if (this.elements.languagesCheckboxes) {
                this.elements.languagesCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', () => {
                        this.updateLanguageDisplay();
                    });
                });
            }
        }

        /**
         * 填充表单数据 (统一化版本)
         * 支持Gemini返回和API数据格式
         */
        fillFormFromData(data) {
            if (!data || typeof data !== 'object') {
                getLogger().log('表单数据无效', 'error');
                return;
            }

            try {
                // 标准化数据格式
                const normalizedData = this.normalizeDataFormat(data);
                
                // 处理字段映射和填充
                Object.entries(this.fieldMappings).forEach(([apiField, formField]) => {
                    if (normalizedData.hasOwnProperty(apiField) && this.elements[formField]) {
                        const value = normalizedData[apiField];
                        
                        if (value !== null && value !== undefined && value !== '') {
                            this.setFieldValue(formField, value);
                        }
                    }
                });

                // 特殊处理语言数组
                if (normalizedData.languages_id_array) {
                    this.setLanguageSelection(normalizedData.languages_id_array);
                }

                // 触发表单更新事件
                this.triggerFormUpdate();
                getLogger().log('表单数据填充完成', 'success', {
                    fieldsFilled: Object.keys(normalizedData).length
                });

            } catch (error) {
                getLogger().log('表单数据填充失败', 'error', { error: error.message });
            }
        }

        /**
         * 设置字段值
         */
        setFieldValue(fieldName, value) {
            const element = this.elements[fieldName];
            if (!element) return;

            try {
                if (element.type === 'checkbox') {
                    element.checked = Boolean(value);
                } else if (element.tagName === 'SELECT') {
                    // 确保选项存在
                    const option = Array.from(element.options).find(opt => opt.value == value);
                    if (option) {
                        element.value = value;
                    }
                } else {
                    element.value = value;
                }

                // 触发change事件
                element.dispatchEvent(new Event('change', { bubbles: true }));

            } catch (error) {
                getLogger().log('设置字段值失败', 'error', { field: fieldName, error: error.message });
            }
        }

        /**
         * 设置语言选择 (关键集成方法)
         */
        setLanguageSelection(languageIds) {
            if (!Array.isArray(languageIds) || !this.elements.languagesCheckboxes) {
                return;
            }

            try {
                // 清除所有选择
                this.elements.languagesCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });

                // 设置指定语言
                languageIds.forEach(langId => {
                    const checkbox = Array.from(this.elements.languagesCheckboxes)
                        .find(cb => parseInt(cb.value) === parseInt(langId));
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });

                this.updateLanguageDisplay();
                getLogger().log('语言选择已设置', 'info', { languages: languageIds });

            } catch (error) {
                getLogger().log('设置语言选择失败', 'error', { error: error.message });
            }
        }

        /**
         * 更新语言显示
         */
        updateLanguageDisplay() {
            const selectedLanguages = this.getSelectedLanguages();
            const displayText = selectedLanguages.length > 0 
                ? `已选择 ${selectedLanguages.length} 种语言`
                : '请选择语言';
            
            // 更新显示元素（如果存在）
            const displayElement = document.querySelector('.language-selection-display');
            if (displayElement) {
                displayElement.textContent = displayText;
            }
        }

        /**
         * 获取选中的语言
         */
        getSelectedLanguages() {
            if (!this.elements.languagesCheckboxes) return [];
            
            return Array.from(this.elements.languagesCheckboxes)
                .filter(checkbox => checkbox.checked)
                .map(checkbox => parseInt(checkbox.value))
                .filter(id => !isNaN(id));
        }

        /**
         * 收集表单数据 (统一化版本)
         * 返回符合API规范的格式
         */
        collectFormData() {
            const formData = {};

            try {
                // 收集基础字段数据
                Object.entries(this.fieldMappings).forEach(([apiField, formField]) => {
                    const element = this.elements[formField];
                    if (element) {
                        let value = this.getElementValue(element);
                        if (value !== null && value !== undefined && value !== '') {
                            formData[apiField] = value;
                        }
                    }
                });

                // 特殊处理语言数组 - 转换为API格式
                const selectedLanguages = this.getSelectedLanguages();
                if (selectedLanguages.length > 0) {
                    // 转换为对象格式 {"0":"2","1":"4"}
                    const languageObject = {};
                    selectedLanguages.forEach((id, index) => {
                        languageObject[index.toString()] = id.toString();
                    });
                    formData.languages_id_array = languageObject;
                }

                // 确保关键字段为字符串
                ['sub_category_id', 'car_type_id', 'incharge_by_backend_user_id'].forEach(field => {
                    if (formData[field] !== undefined) {
                        formData[field] = String(formData[field]);
                    }
                });

                getLogger().log('表单数据收集完成', 'info', { 
                    fields: Object.keys(formData).length,
                    apiReady: true 
                });
                return formData;

            } catch (error) {
                getLogger().log('表单数据收集失败', 'error', { error: error.message });
                return {};
            }
        }

        /**
         * 获取元素值
         */
        getElementValue(element) {
            if (!element) return null;

            if (element.type === 'checkbox') {
                return element.checked;
            } else if (element.type === 'number') {
                const value = parseFloat(element.value);
                return isNaN(value) ? null : value;
            } else if (element.tagName === 'SELECT') {
                return element.value || null;
            } else {
                return element.value.trim() || null;
            }
        }

        /**
         * 获取表单数据 (向后兼容方法)
         */
        getFormData() {
            return this.collectFormData();
        }

        /**
         * 填充下拉菜单 (委托给FormService)
         * @deprecated 此方法已废弃，下拉菜单填充已统一到FormService微服务
         */
        populateDropdowns() {
            // 🗑️ 已移除重复的下拉菜单填充逻辑 - 减法重构优化
            // 下拉菜单填充已统一到FormService微服务中
            const formService = window.OTA?.getService?.('formService');
            if (formService && typeof formService.populateDropdowns === 'function') {
                formService.populateDropdowns();
                getLogger().log('下拉菜单填充已委托给FormService', 'info');
            } else {
                getLogger().log('FormService不可用，无法填充下拉菜单', 'warning');
            }
        }

        /**
         * 填充后台用户下拉菜单
         * @deprecated 此方法已废弃，已统一到FormService微服务
         */
        populateBackendUsers(users) {
            // 🗑️ 已移除重复的后台用户填充逻辑 - 减法重构优化
            // 后台用户填充已统一到FormService微服务中
            getLogger().log('后台用户填充已委托给FormService', 'info');
        }

        /**
         * 填充服务类型下拉菜单
         * @deprecated 此方法已废弃，已统一到FormService微服务
         */
        populateSubCategories(categories) {
            // 🗑️ 已移除重复的服务类型填充逻辑 - 减法重构优化
            // 服务类型填充已统一到FormService微服务中
            getLogger().log('服务类型填充已委托给FormService', 'info');
        }

        /**
         * 填充车型下拉菜单
         * @deprecated 此方法已废弃，已统一到FormService微服务
         */
        populateCarTypes(carTypes) {
            // 🗑️ 已移除重复的车型填充逻辑 - 减法重构优化
            // 车型填充已统一到FormService微服务中
            getLogger().log('车型填充已委托给FormService', 'info');
        }

        /**
         * 填充驾驶区域下拉菜单
         * @deprecated 此方法已废弃，已统一到FormService微服务
         */
        populateDrivingRegions(regions) {
            // 🗑️ 已移除重复的驾驶区域填充逻辑 - 减法重构优化
            // 驾驶区域填充已统一到FormService微服务中
            getLogger().log('驾驶区域填充已委托给FormService', 'info');
        }

        /**
         * 清空选择框选项
         */
        clearSelectOptions(selectElement) {
            selectElement.innerHTML = '';
        }

        /**
         * 添加默认选项
         */
        addDefaultOption(selectElement, text) {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = text;
            selectElement.appendChild(option);
        }

        /**
         * 添加选择选项
         */
        addSelectOption(selectElement, value, text) {
            const option = document.createElement('option');
            option.value = value;
            option.textContent = text;
            selectElement.appendChild(option);
        }

        /**
         * 验证字段
         */
        validateField(fieldName) {
            const element = this.elements[fieldName];
            const rule = this.validationRules[fieldName];
            
            if (!element || !rule) return true;

            const value = this.getElementValue(element);
            const errors = [];

            // 必填验证
            if (rule.required && (value === null || value === undefined || value === '')) {
                errors.push('此字段为必填项');
            }

            // 类型验证
            if (value && rule.type) {
                if (rule.type === 'email' && rule.pattern && !rule.pattern.test(value)) {
                    errors.push('请输入有效的邮箱地址');
                } else if (rule.type === 'number' && isNaN(parseFloat(value))) {
                    errors.push('请输入有效的数字');
                }
            }

            // 长度验证
            if (value && rule.minLength && value.length < rule.minLength) {
                errors.push(`最少需要${rule.minLength}个字符`);
            }

            // 显示验证结果
            if (errors.length > 0) {
                this.showFieldError(fieldName, errors[0]);
                return false;
            } else {
                this.clearFieldError(fieldName);
                return true;
            }
        }

        /**
         * 显示字段错误
         */
        showFieldError(fieldName, message) {
            const element = this.elements[fieldName];
            if (!element) return;

            element.classList.add('error');
            
            // 查找或创建错误信息元素
            let errorElement = element.parentNode.querySelector('.field-error');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'field-error';
                element.parentNode.appendChild(errorElement);
            }
            errorElement.textContent = message;
        }

        /**
         * 清除字段错误
         */
        clearFieldError(fieldName) {
            const element = this.elements[fieldName];
            if (!element) return;

            element.classList.remove('error');
            
            const errorElement = element.parentNode.querySelector('.field-error');
            if (errorElement) {
                errorElement.remove();
            }
        }

        /**
         * 验证整个表单
         */
        validateForm() {
            let isValid = true;
            
            Object.keys(this.validationRules).forEach(fieldName => {
                if (!this.validateField(fieldName)) {
                    isValid = false;
                }
            });

            return isValid;
        }

        /**
         * 触发表单更新事件
         */
        triggerFormUpdate() {
            const event = new CustomEvent('formUpdated', {
                detail: { formData: this.collectFormData() }
            });
            document.dispatchEvent(event);
        }

        /**
         * 重置表单
         */
        resetForm() {
            if (this.elements.form) {
                this.elements.form.reset();
            }

            // 清除所有错误状态
            Object.keys(this.elements).forEach(fieldName => {
                this.clearFieldError(fieldName);
            });

            // 清除语言选择
            if (this.elements.languagesCheckboxes) {
                this.elements.languagesCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
            }

            this.updateLanguageDisplay();
            getLogger().log('表单已重置', 'info');
        }

        /**
         * 标准化数据格式
         * 处理Gemini返回、表单数据、API数据的不同格式
         */
        normalizeDataFormat(data) {
            const normalized = {};
            
            // 处理日期格式 - 统一为YYYY-MM-DD
            if (data.date) normalized.date = this.normalizeDate(data.date);
            if (data.pickup_date) normalized.pickup_date = this.normalizeDate(data.pickup_date);
            if (data.return_date) normalized.return_date = this.normalizeDate(data.return_date);
            
            // 处理时间格式 - 统一为HH:MM
            if (data.time) normalized.time = this.normalizeTime(data.time);
            if (data.pickup_time) normalized.pickup_time = this.normalizeTime(data.pickup_time);
            if (data.return_time) normalized.return_time = this.normalizeTime(data.return_time);
            
            // 处理语言数组格式
            if (data.languages_id_array) {
                normalized.languages_id_array = this.normalizeLanguages(data.languages_id_array);
            }
            if (data.languages) {
                normalized.languages_id_array = this.normalizeLanguages(data.languages);
            }
            
            // 处理数值字段
            ['ota_price', 'passenger_number', 'passenger_count', 'pax', 
             'luggage_number', 'luggage_count', 'driver_fee', 'driver_collect'].forEach(field => {
                if (data[field] !== undefined) {
                    const numValue = parseFloat(data[field]);
                    if (!isNaN(numValue)) {
                        normalized[field] = numValue;
                    }
                }
            });
            
            // 处理字符串字段
            ['customer_name', 'customer_contact', 'customer_email', 'pickup', 'destination',
             'flight_info', 'flight_number', 'special_requests', 'remarks', 'ota_reference_number',
             'sub_category_id', 'car_type_id', 'driving_region_id', 'incharge_by_backend_user_id'].forEach(field => {
                if (data[field] !== undefined && data[field] !== null && data[field] !== '') {
                    normalized[field] = String(data[field]).trim();
                }
            });
            
            return normalized;
        }
        
        /**
         * 标准化日期格式
         */
        normalizeDate(dateStr) {
            if (!dateStr) return null;
            
            // 处理不同格式：DD-MM-YYYY, YYYY-MM-DD, DD/MM/YYYY
            const formats = [
                /^(\d{2})-(\d{2})-(\d{4})$/,  // DD-MM-YYYY
                /^(\d{4})-(\d{2})-(\d{2})$/,  // YYYY-MM-DD
                /^(\d{2})\/(\d{2})\/(\d{4})$/, // DD/MM/YYYY
                /^(\d{4})\/(\d{2})\/(\d{2})$/   // YYYY/MM/DD
            ];
            
            for (const format of formats) {
                const match = dateStr.match(format);
                if (match) {
                    // 确保返回YYYY-MM-DD格式
                    if (format.source.includes('\\d{4}-\\d{2}-\\d{2}')) {
                        return dateStr;
                    } else if (format.source.includes('\\d{2}-\\d{2}-\\d{4}')) {
                        return `${match[3]}-${match[2]}-${match[1]}`;
                    } else if (format.source.includes('\\d{2}/\\d{2}/\\d{4}')) {
                        return `${match[3]}-${match[2]}-${match[1]}`;
                    } else if (format.source.includes('\\d{4}/\\d{2}/\\d{2}')) {
                        return `${match[1]}-${match[2]}-${match[3]}`;
                    }
                }
            }
            
            return null;
        }
        
        /**
         * 标准化时间格式
         */
        normalizeTime(timeStr) {
            if (!timeStr) return null;
            
            // 处理HH:MM格式
            const match = timeStr.match(/^(\d{1,2}):(\d{2})$/);
            if (match) {
                const hours = parseInt(match[1]);
                const minutes = parseInt(match[2]);
                if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
                    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                }
            }
            
            return null;
        }
        
        /**
         * 标准化语言数组格式
         */
        normalizeLanguages(languages) {
            if (!languages) return null;
            
            // 处理数组格式 [2,4]
            if (Array.isArray(languages)) {
                return languages.map(id => parseInt(id)).filter(id => !isNaN(id));
            }
            
            // 处理对象格式 {"0":"2","1":"4"}
            if (typeof languages === 'object') {
                return Object.values(languages).map(id => parseInt(id)).filter(id => !isNaN(id));
            }
            
            // 处理逗号分隔字符串 "2,4"
            if (typeof languages === 'string') {
                return languages.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            }
            
            return null;
        }
        
        /**
         * 获取表单状态
         */
        getFormStatus() {
            return {
                initialized: this.initialized,
                elementCount: Object.keys(this.elements).length,
                isValid: this.validateForm(),
                selectedLanguages: this.getSelectedLanguages().length,
                fieldMappings: Object.keys(this.fieldMappings).length
            };
        }
    }

    // 导出到全局命名空间
    window.OTA.managers.FormManager = FormManager;

    // 🗑️ 已移除三重注册问题 - 减法重构优化
    // 统一服务注册机制：仅通过依赖容器注册，避免全局变量污染

    // 优先注册到依赖容器（单例模式）
    if (window.OTA && window.OTA.container) {
        try {
            window.OTA.container.register('formManager', () => {
                const manager = new FormManager();
                manager.init();
                return manager;
            }, { singleton: true });
            getLogger().log('FormManager已统一注册到依赖容器', 'info');
        } catch (error) {
            console.warn('FormManager注册失败:', error.message);

            // 🗑️ 仅在容器注册失败时才创建全局实例 - 减法重构优化
            // 避免重复注册：window.OTA.formManager 和 window.formManager
            if (!window.OTA.formManager) {
                const fallbackInstance = new FormManager();
                fallbackInstance.init();
                window.OTA.formManager = fallbackInstance;
                console.warn('FormManager已降级到全局注册');
            }
        }
    }

})();