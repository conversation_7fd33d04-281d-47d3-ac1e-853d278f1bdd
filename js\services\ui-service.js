/**
 * UI服务模块 - 微服务架构
 * 负责用户界面管理、通知显示和界面状态控制
 * 从UIManager转换而来，采用函数式编程风格
 * 
 * <AUTHOR> System
 * @created 2025-01-01
 */

(function() {
    'use strict';

    // 确保全局命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.services = window.OTA.services || {};

    /**
     * 获取依赖服务的辅助函数
     */
    function getLogger() {
        return window.OTA?.logger || window.getLogger?.() || console;
    }

    function getAppState() {
        return window.OTA?.appState || window.appState;
    }

    /**
     * UI服务状态
     */
    let serviceState = {
        initialized: false,
        notificationTimeout: null,
        notificationContainer: null
    };

    /**
     * 通知样式配置
     */
    const NOTIFICATION_STYLES = {
        success: '#28a745',
        error: '#dc3545', 
        warning: '#ffc107',
        info: '#17a2b8',
        primary: '#007bff'
    };

    /**
     * 创建通知容器
     */
    function createNotificationContainer() {
        if (document.getElementById('notification-container')) {
            serviceState.notificationContainer = document.getElementById('notification-container');
            return;
        }

        const container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
            pointer-events: none;
        `;
        document.body.appendChild(container);
        serviceState.notificationContainer = container;
        
        getLogger().log('通知容器已创建', 'info');
    }

    /**
     * 显示通知消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success|error|warning|info|primary)
     * @param {number} duration - 显示时长(毫秒)，0表示不自动关闭
     */
    function showAlert(message, type = 'info', duration = 3000) {
        if (!message) return;

        try {
            if (!serviceState.notificationContainer) {
                createNotificationContainer();
            }

            const notification = document.createElement('div');
            notification.style.cssText = `
                background: ${NOTIFICATION_STYLES[type] || NOTIFICATION_STYLES.info};
                color: white;
                padding: 12px 16px;
                margin-bottom: 10px;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                transform: translateX(100%);
                transition: transform 0.3s ease;
                cursor: pointer;
                pointer-events: auto;
                font-size: 14px;
                line-height: 1.4;
                word-wrap: break-word;
            `;
            
            notification.textContent = message;
            serviceState.notificationContainer.appendChild(notification);

            // 动画显示
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);

            // 点击关闭
            notification.onclick = () => {
                hideNotification(notification);
            };

            // 自动关闭
            if (duration > 0) {
                serviceState.notificationTimeout = setTimeout(() => {
                    hideNotification(notification);
                }, duration);
            }

            getLogger().log('通知已显示', 'info', { message, type });

        } catch (error) {
            console.error('显示通知失败:', error);
            // 降级到原生alert
            alert(message);
        }
    }

    /**
     * 隐藏通知
     * @param {HTMLElement} notification - 通知元素
     */
    function hideNotification(notification) {
        if (notification && notification.parentNode) {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    }

    /**
     * 清除所有通知
     */
    function clearNotification() {
        if (serviceState.notificationTimeout) {
            clearTimeout(serviceState.notificationTimeout);
            serviceState.notificationTimeout = null;
        }

        if (serviceState.notificationContainer) {
            serviceState.notificationContainer.innerHTML = '';
        }
    }

    /**
     * 显示错误消息 (便捷方法)
     * @param {string} message - 错误消息
     */
    function showError(message) {
        showAlert(message, 'error', 5000);
    }

    /**
     * 显示成功消息 (便捷方法)
     * @param {string} message - 成功消息
     */
    function showSuccess(message) {
        showAlert(message, 'success', 3000);
    }

    /**
     * 显示警告消息 (便捷方法)
     * @param {string} message - 警告消息
     */
    function showWarning(message) {
        showAlert(message, 'warning', 4000);
    }

    /**
     * 显示信息消息 (便捷方法)
     * @param {string} message - 信息消息
     */
    function showInfo(message) {
        showAlert(message, 'info', 3000);
    }

    /**
     * 更新登录UI状态
     * @param {boolean} isLoggedIn - 是否已登录
     */
    function updateLoginUI(isLoggedIn) {
        try {
            // 修复元素ID：使用正确的ID 'workspace' 而不是 'workspacePanel'
            const workspace = document.getElementById('workspace');
            const loginPanel = document.getElementById('loginPanel');
            const logoutBtn = document.getElementById('logoutBtn');
            const userInfo = document.getElementById('userInfo');

            if (isLoggedIn) {
                if (workspace) workspace.style.display = 'block';
                if (loginPanel) loginPanel.style.display = 'none';
                if (logoutBtn) logoutBtn.style.display = 'inline-block';
                if (userInfo) userInfo.style.display = 'block';
                
                // 初始化工作区功能
                initializeWorkspace();
                
                getLogger().log('UI已切换到工作区模式', 'info');
            } else {
                if (workspace) workspace.style.display = 'none';
                if (loginPanel) loginPanel.style.display = 'block';
                if (logoutBtn) logoutBtn.style.display = 'none';
                if (userInfo) userInfo.style.display = 'none';
                
                getLogger().log('UI已切换到登录模式', 'info');
            }

        } catch (error) {
            getLogger().log('更新登录UI失败', 'error', { error: error.message });
        }
    }

    /**
     * 初始化工作区
     */
    function initializeWorkspace() {
        // 触发工作区初始化事件，让其他服务响应
        document.dispatchEvent(new CustomEvent('workspaceInitialized'));
        getLogger().log('工作区初始化事件已触发', 'info');
    }

    /**
     * 显示加载状态
     * @param {string} message - 加载消息
     */
    function showLoading(message = '加载中...') {
        // 移除现有的加载器
        hideLoading();

        const loader = document.createElement('div');
        loader.id = 'global-loader';
        loader.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
            font-size: 16px;
        `;

        loader.innerHTML = `
            <div style="text-align: center;">
                <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 10px;"></div>
                <div>${message}</div>
            </div>
        `;

        // 添加旋转动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(loader);
    }

    /**
     * 隐藏加载状态
     */
    function hideLoading() {
        const loader = document.getElementById('global-loader');
        if (loader) {
            loader.remove();
        }
    }

    /**
     * 更新UI状态 (通用刷新方法)
     */
    function updateUI() {
        try {
            // 检查登录状态并更新UI
            const appState = getAppState();
            if (appState) {
                const isLoggedIn = appState.get('auth.isLoggedIn');
                updateLoginUI(isLoggedIn);
            }

            // 触发UI更新事件
            document.dispatchEvent(new CustomEvent('uiUpdated'));
            
            getLogger().log('UI更新完成', 'info');

        } catch (error) {
            getLogger().log('UI更新失败', 'error', { error: error.message });
        }
    }

    /**
     * UI服务对象 - 微服务接口
     */
    const UIService = {
        /**
         * 初始化服务
         */
        init() {
            if (serviceState.initialized) {
                getLogger().log('UI服务已经初始化', 'warning');
                return Promise.resolve();
            }

            try {
                createNotificationContainer();
                serviceState.initialized = true;
                getLogger().log('UI服务初始化完成', 'success');
                return Promise.resolve();
            } catch (error) {
                getLogger().log('UI服务初始化失败', 'error', { error: error.message });
                return Promise.reject(error);
            }
        },

        /**
         * 销毁服务
         */
        destroy() {
            clearNotification();
            hideLoading();
            
            if (serviceState.notificationContainer) {
                serviceState.notificationContainer.remove();
                serviceState.notificationContainer = null;
            }

            serviceState.initialized = false;
            getLogger().log('UI服务已销毁', 'info');
            return Promise.resolve();
        },

        /**
         * 获取服务状态
         */
        getStatus() {
            return {
                name: 'UIService',
                initialized: serviceState.initialized,
                state: serviceState.initialized ? 'ready' : 'uninitialized',
                hasNotificationContainer: !!serviceState.notificationContainer,
                version: '1.0.0'
            };
        },

        // 公共API方法
        showAlert,
        showError,
        showSuccess,
        showWarning,
        showInfo,
        clearNotification,
        updateLoginUI,
        updateUI,
        showLoading,
        hideLoading,
        initializeWorkspace
    };

    // 注册到全局命名空间
    window.OTA.services.uiService = UIService;

    // 向后兼容性支持
    window.uiService = UIService;

    // 日志记录
    getLogger().log('UI服务模块已加载', 'info');

})();
