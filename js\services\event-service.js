/**
 * 事件服务模块 - 微服务架构
 * 负责事件处理和协调，DOM事件监听器管理
 * 从EventManager转换而来，采用函数式编程风格
 * 
 * <AUTHOR> System
 * @created 2025-01-01
 */

(function() {
    'use strict';

    // 确保全局命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.services = window.OTA.services || {};

    /**
     * 获取依赖服务的辅助函数
     */
    function getLogger() {
        return window.OTA?.logger || window.getLogger?.() || console;
    }

    /**
     * 事件服务状态
     */
    let serviceState = {
        initialized: false,
        eventHandlers: new Map(),
        debounceTimers: new Map(),
        touchStartTime: null,
        touchStartTarget: null
    };

    /**
     * 添加事件处理器
     * @param {Element} element - 目标元素
     * @param {string} eventType - 事件类型
     * @param {Function} handler - 处理函数
     * @param {Object} options - 选项
     * @returns {string} 处理器键值
     */
    function addEventHandler(element, eventType, handler, options = {}) {
        if (!element || !eventType || !handler) return null;

        const key = `${eventType}_${Date.now()}_${Math.random()}`;
        const wrappedHandler = (event) => {
            try {
                handler(event);
            } catch (error) {
                getLogger().log('事件处理器执行失败', 'error', { 
                    eventType, 
                    error: error.message 
                });
            }
        };

        element.addEventListener(eventType, wrappedHandler, options);
        serviceState.eventHandlers.set(key, {
            element,
            eventType,
            handler: wrappedHandler,
            originalHandler: handler
        });

        return key;
    }

    /**
     * 移除事件处理器
     * @param {string} key - 处理器键值
     * @returns {boolean} 是否成功移除
     */
    function removeEventHandler(key) {
        const handlerInfo = serviceState.eventHandlers.get(key);
        if (handlerInfo) {
            handlerInfo.element.removeEventListener(
                handlerInfo.eventType, 
                handlerInfo.handler
            );
            serviceState.eventHandlers.delete(key);
            return true;
        }
        return false;
    }

    /**
     * 防抖函数 - 优化版本
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间
     * @param {string} key - 可选的唯一键，用于更精确的防抖控制
     * @returns {Function} 防抖后的函数
     */
    function debounce(func, delay, key = null) {
        return (...args) => {
            // 使用提供的key或生成基于函数名和参数的key
            const debounceKey = key || `${func.name || 'anonymous'}_${args.length}`;

            if (serviceState.debounceTimers.has(debounceKey)) {
                clearTimeout(serviceState.debounceTimers.get(debounceKey));
            }

            const timer = setTimeout(() => {
                try {
                    func.apply(this, args);
                } catch (error) {
                    getLogger().log('防抖函数执行失败', 'error', {
                        functionName: func.name,
                        error: error.message
                    });
                } finally {
                    serviceState.debounceTimers.delete(debounceKey);
                }
            }, delay);

            serviceState.debounceTimers.set(debounceKey, timer);
        };
    }

    /**
     * 绑定全局事件处理器 - 优化版本
     */
    function bindGlobalEventHandlers() {
        // 文档点击事件
        addEventHandler(document, 'click', handleDocumentClick);

        // 表单变化事件
        addEventHandler(document, 'change', handleFormChange);

        // 输入事件 (智能防抖处理)
        addEventHandler(document, 'input', debounce(handleInput, 300, 'global_input'));

        // 滚动事件 (防抖处理)
        addEventHandler(window, 'scroll', debounce(handleScroll, 100, 'window_scroll'));

        // 窗口大小变化事件 (防抖处理)
        addEventHandler(window, 'resize', debounce(handleResize, 250, 'window_resize'));
    }

    /**
     * 绑定键盘快捷键
     */
    function bindKeyboardShortcuts() {
        addEventHandler(document, 'keydown', handleKeyboardShortcuts);
    }

    /**
     * 绑定触摸事件 (移动端支持)
     */
    function bindTouchEvents() {
        if ('ontouchstart' in window) {
            addEventHandler(document, 'touchstart', handleTouchStart);
            addEventHandler(document, 'touchend', handleTouchEnd);
        }
    }

    /**
     * 绑定面板事件
     */
    function bindPanelEvents() {
        const panels = document.querySelectorAll('.panel, .order-panel, .multi-order-panel');
        panels.forEach(panel => {
            addEventHandler(panel, 'click', handlePanelClick);
            addEventHandler(panel, 'change', handlePanelChange);
        });
    }

    /**
     * 处理文档点击事件 - 优化版本
     * @param {Event} event - 点击事件
     */
    function handleDocumentClick(event) {
        const target = event.target;

        // 使用单次DOM查询优化性能
        let currentElement = target;
        let buttonElement = null;
        let linkElement = null;
        let panelElement = null;

        // 向上遍历DOM树，一次性查找所有相关元素
        while (currentElement && currentElement !== document) {
            // 检查按钮
            if (!buttonElement && currentElement.matches?.('button, .btn, [role="button"]')) {
                buttonElement = currentElement;
            }

            // 检查链接
            if (!linkElement && currentElement.matches?.('a, [role="link"]')) {
                linkElement = currentElement;
            }

            // 检查面板
            if (!panelElement && currentElement.matches?.('.panel, .order-panel, .multi-order-panel')) {
                panelElement = currentElement;
            }

            // 如果找到所有类型，提前退出
            if (buttonElement && linkElement && panelElement) break;

            currentElement = currentElement.parentElement;
        }

        // 批量处理事件通知
        const notifications = [];

        if (buttonElement) {
            notifications.push({
                type: 'button',
                context: {
                    buttonType: buttonElement.type,
                    buttonId: buttonElement.id
                }
            });
        }

        if (linkElement) {
            notifications.push({
                type: 'link',
                context: {
                    href: linkElement.href
                }
            });
        }

        if (panelElement) {
            notifications.push({
                type: 'panel',
                context: {
                    panelId: panelElement.id,
                    panelClass: panelElement.className
                }
            });
        }

        // 批量发送通知
        if (notifications.length > 0) {
            notifyClickHandlers('batch', event, { notifications });
        }
    }

    /**
     * 处理面板点击事件
     * @param {Event} event - 点击事件
     */
    function handlePanelClick(event) {
        const panel = event.currentTarget;
        notifyClickHandlers('panel', event, { 
            panelId: panel.id,
            panelClass: panel.className 
        });
    }

    /**
     * 处理面板变化事件
     * @param {Event} event - 变化事件
     */
    function handlePanelChange(event) {
        const panel = event.currentTarget;
        notifyChangeHandlers('panel', event, { 
            panelId: panel.id,
            fieldName: event.target.name 
        });
    }

    /**
     * 处理表单变化事件
     * @param {Event} event - 变化事件
     */
    function handleFormChange(event) {
        const formElement = event.target.closest('form');
        if (formElement) {
            notifyChangeHandlers('form', event, { 
                formId: formElement.id,
                fieldName: event.target.name 
            });
        }
    }

    /**
     * 处理输入事件 (智能防抖)
     * @param {Event} event - 输入事件
     */
    function handleInput(event) {
        const input = event.target;
        if (input.tagName === 'INPUT' || input.tagName === 'TEXTAREA') {
            // 根据输入类型调整防抖延迟
            const inputType = input.type || 'text';
            const isRealTimeField = input.classList.contains('realtime') ||
                                  input.id === 'orderInput' ||
                                  inputType === 'search';

            if (isRealTimeField) {
                // 实时字段使用较短延迟
                notifyInputHandlers(input, event);
            } else {
                // 普通字段使用标准延迟
                notifyInputHandlers(input, event);
            }
        }
    }

    /**
     * 处理滚动事件 (防抖)
     * @param {Event} event - 滚动事件
     */
    function handleScroll(event) {
        document.dispatchEvent(new CustomEvent('eventServiceScroll', {
            detail: {
                scrollY: window.scrollY,
                scrollX: window.scrollX,
                event
            }
        }));
    }

    /**
     * 处理窗口大小变化事件 (防抖)
     * @param {Event} event - 大小变化事件
     */
    function handleResize(event) {
        document.dispatchEvent(new CustomEvent('eventServiceResize', {
            detail: {
                width: window.innerWidth,
                height: window.innerHeight,
                event
            }
        }));
    }

    /**
     * 处理键盘快捷键
     * @param {KeyboardEvent} event - 键盘事件
     */
    function handleKeyboardShortcuts(event) {
        // Ctrl+S 保存
        if (event.ctrlKey && event.key === 's') {
            event.preventDefault();
            triggerSave();
        }
        
        // Ctrl+Enter 提交
        if (event.ctrlKey && event.key === 'Enter') {
            event.preventDefault();
            triggerSubmit();
        }
        
        // Escape 取消/关闭
        if (event.key === 'Escape') {
            triggerCancel();
        }
    }

    /**
     * 处理触摸开始事件
     * @param {TouchEvent} event - 触摸事件
     */
    function handleTouchStart(event) {
        serviceState.touchStartTime = Date.now();
        serviceState.touchStartTarget = event.target;
    }

    /**
     * 处理触摸结束事件
     * @param {TouchEvent} event - 触摸事件
     */
    function handleTouchEnd(event) {
        const touchDuration = Date.now() - (serviceState.touchStartTime || 0);
        const isLongPress = touchDuration > 500;
        
        if (isLongPress && serviceState.touchStartTarget === event.target) {
            handleLongPress(event);
        }
    }

    /**
     * 处理长按事件
     * @param {Event} event - 事件对象
     */
    function handleLongPress(event) {
        // 触发长按事件
        document.dispatchEvent(new CustomEvent('longPress', {
            detail: { target: event.target, originalEvent: event }
        }));
    }

    /**
     * 通知点击处理器 - 优化版本，支持批量处理
     * @param {string} type - 点击类型
     * @param {Event} event - 事件对象
     * @param {Object} context - 上下文信息
     */
    function notifyClickHandlers(type, event, context = {}) {
        // 性能监控
        const startTime = performance.now();

        try {
            if (type === 'batch' && context.notifications) {
                // 批量处理多个通知
                document.dispatchEvent(new CustomEvent('eventServiceClickBatch', {
                    detail: {
                        notifications: context.notifications,
                        event,
                        timestamp: Date.now()
                    }
                }));
            } else {
                // 单个通知
                document.dispatchEvent(new CustomEvent('eventServiceClick', {
                    detail: { type, event, context, timestamp: Date.now() }
                }));
            }
        } catch (error) {
            getLogger().log('事件通知失败', 'error', { type, error: error.message });
        } finally {
            const duration = performance.now() - startTime;
            if (duration > 5) { // 记录超过5ms的慢操作
                getLogger().log('事件处理耗时较长', 'warning', { type, duration: `${duration.toFixed(2)}ms` });
            }
        }
    }

    /**
     * 通知变化处理器 - 优化版本
     * @param {string} type - 变化类型
     * @param {Event} event - 事件对象
     * @param {Object} context - 上下文信息
     */
    function notifyChangeHandlers(type, event, context = {}) {
        const startTime = performance.now();

        try {
            document.dispatchEvent(new CustomEvent('eventServiceChange', {
                detail: { type, event, context, timestamp: Date.now() }
            }));
        } catch (error) {
            getLogger().log('变化事件通知失败', 'error', { type, error: error.message });
        } finally {
            const duration = performance.now() - startTime;
            if (duration > 3) {
                getLogger().log('变化事件处理耗时较长', 'warning', { type, duration: `${duration.toFixed(2)}ms` });
            }
        }
    }

    /**
     * 通知输入处理器 - 优化版本
     * @param {HTMLElement} input - 输入元素
     * @param {Event} event - 事件对象
     */
    function notifyInputHandlers(input, event) {
        const startTime = performance.now();

        try {
            // 添加输入元数据
            const metadata = {
                inputType: input.type,
                inputId: input.id,
                inputName: input.name,
                valueLength: input.value?.length || 0,
                isRealTime: input.classList.contains('realtime')
            };

            document.dispatchEvent(new CustomEvent('eventServiceInput', {
                detail: {
                    input,
                    event,
                    value: input.value,
                    metadata,
                    timestamp: Date.now()
                }
            }));
        } catch (error) {
            getLogger().log('输入事件通知失败', 'error', {
                inputId: input.id,
                error: error.message
            });
        } finally {
            const duration = performance.now() - startTime;
            if (duration > 2) {
                getLogger().log('输入事件处理耗时较长', 'warning', {
                    inputId: input.id,
                    duration: `${duration.toFixed(2)}ms`
                });
            }
        }
    }

    /**
     * 触发保存操作
     */
    function triggerSave() {
        document.dispatchEvent(new CustomEvent('triggerSave'));
        getLogger().log('触发保存快捷键', 'info');
    }

    /**
     * 触发提交操作
     */
    function triggerSubmit() {
        const activeForm = document.querySelector('form:focus-within');
        if (activeForm) {
            activeForm.dispatchEvent(new Event('submit', { bubbles: true }));
        } else {
            document.dispatchEvent(new CustomEvent('triggerSubmit'));
        }
        getLogger().log('触发提交快捷键', 'info');
    }

    /**
     * 触发取消操作
     */
    function triggerCancel() {
        document.dispatchEvent(new CustomEvent('triggerCancel'));
        getLogger().log('触发取消快捷键', 'info');
    }

    /**
     * 清理所有事件处理器
     */
    function cleanupAllHandlers() {
        // 清理所有事件处理器
        for (const [key] of serviceState.eventHandlers) {
            removeEventHandler(key);
        }

        // 清理防抖定时器
        for (const timer of serviceState.debounceTimers.values()) {
            clearTimeout(timer);
        }
        serviceState.debounceTimers.clear();

        getLogger().log('所有事件处理器已清理', 'info');
    }

    /**
     * 事件服务对象 - 微服务接口
     */
    const EventService = {
        /**
         * 初始化服务
         */
        init() {
            if (serviceState.initialized) {
                getLogger().log('事件服务已经初始化', 'warning');
                return Promise.resolve();
            }

            try {
                bindGlobalEventHandlers();
                bindKeyboardShortcuts();
                bindTouchEvents();
                bindPanelEvents();
                
                serviceState.initialized = true;
                getLogger().log('事件服务初始化完成', 'success');
                return Promise.resolve();
            } catch (error) {
                getLogger().log('事件服务初始化失败', 'error', { error: error.message });
                return Promise.reject(error);
            }
        },

        /**
         * 销毁服务
         */
        destroy() {
            cleanupAllHandlers();
            
            serviceState.initialized = false;
            serviceState.touchStartTime = null;
            serviceState.touchStartTarget = null;
            
            getLogger().log('事件服务已销毁', 'info');
            return Promise.resolve();
        },

        /**
         * 获取服务状态
         */
        getStatus() {
            return {
                name: 'EventService',
                initialized: serviceState.initialized,
                state: serviceState.initialized ? 'ready' : 'uninitialized',
                handlersCount: serviceState.eventHandlers.size,
                timersCount: serviceState.debounceTimers.size,
                version: '1.0.0'
            };
        },

        // 公共API方法
        addEventHandler,
        removeEventHandler,
        debounce,
        bindGlobalEventHandlers,
        bindKeyboardShortcuts,
        bindTouchEvents,
        bindPanelEvents,
        triggerSave,
        triggerSubmit,
        triggerCancel,
        cleanupAllHandlers,
        
        // 检查可用性
        isAvailable: () => serviceState.initialized
    };

    // 注册到全局命名空间
    window.OTA.services.eventService = EventService;

    // 向后兼容性支持
    window.eventService = EventService;

    // 日志记录
    getLogger().log('事件服务模块已加载', 'info');

})();
