/**
 * 文件: gemini-configs.js
 * 路径: js\ai\gemini-configs.js
 * 自动生成的依赖关系和结构分析注释
 * 
 * === 外部依赖 ===
 * - console (浏览器API)
 * 
 * === 对外暴露 ===
 * - window.OTA
 * - window.OTA.ai
 * - window.OTA.ai.gemini
 * - window.OTA.ai.gemini.configs
 * - window.OTA.ai.gemini.configs.fieldMapping
 * - window.OTA.ai.gemini.configs.presetValues
 * - window.OTA.ai.gemini.configs.fallbackConfig
 * - window.OTA.ai.gemini.configs.performanceConfig
 * - window.OTA.ai.gemini.configs.otaReferencePatterns
 * - window.OTA.ai.gemini.getConfig
 * - window.OTA.ai.gemini.mapField
 * - window.OTA.ai.gemini.getPresetValue
 * 
 * === 函数声明 ===
 * - function getConfig()
 * - function mapField()
 * - function getPresetValue()
 * 
 * === 初始化说明 ===
 * 此文件通过以下方式加载和初始化:
 * 1. HTML中通过<script>标签加载
 * 2. 立即执行函数(IIFE)进行模块化封装
 * 3. 注册服务到全局OTA命名空间
 * 4. 依赖其他模块的初始化完成
 * 
 * 注释生成时间: 2025-07-31T05:53:29.559Z
 */

/**
 * Gemini AI 配置模块
 * 合并原 js/ai/gemini/configs/ 目录下的所有配置文件
 * <AUTHOR> System
 * @created 2025-01-29
 */

window.OTA = window.OTA || {};
window.OTA.ai = window.OTA.ai || {};
window.OTA.ai.gemini = window.OTA.ai.gemini || {};
window.OTA.ai.gemini.configs = window.OTA.ai.gemini.configs || {};

(function() {
    'use strict';

    /**
     * 字段映射配置
     * @deprecated 此映射已废弃，已统一到 js/components/multi-order/field-mapping-config.js
     * 🗑️ 已移除重复的字段映射定义 - 减法重构优化
     */
    const fieldMapping = {
        // 🗑️ 字段映射已统一到 FIELD_MAPPING_CONFIG - 减法重构优化
        // 使用统一配置：window.OTA.FieldMappingConfig
        apiToForm: {},

        // 表单字段到API字段的映射
        formToApi: {
            'date': 'pickup_date',
            'time': 'pickup_time',
            'pickup': 'pickup_location', 
            'destination': 'dropoff_location',
            'luggage_number': 'luggage_count',
            'pax': 'passenger_count',
            'customer_name': 'contact_name',
            'customer_phone': 'contact_phone',
            'special_request': 'special_requirements',
            'ota_reference_number': 'ota_reference'
        },

        // 必填字段列表
        requiredFields: [
            'pickup_date',
            'pickup_time', 
            'pickup_location',
            'dropoff_location',
            'ota_reference_number'
        ],

        // 字段验证规则
        validationRules: {
            'pickup_date': {
                pattern: /^\d{2}-\d{2}-\d{4}$/,
                message: '日期格式必须为 DD-MM-YYYY'
            },
            'pickup_time': {
                pattern: /^\d{2}:\d{2}$/,
                message: '时间格式必须为 HH:MM'
            },
            'contact_phone': {
                pattern: /^(\+?6?01[0-9-]{8,10})$/,
                message: '请输入有效的马来西亚手机号码'
            },
            'passenger_count': {
                min: 1,
                max: 20,
                message: '乘客数量必须在1-20之间'
            }
        }
    };

    /**
     * 预设值配置
     * 原文件：js/ai/gemini/configs/preset-values.js
     */
    const presetValues = {
        // 默认服务类型
        defaultServiceTypes: {
            'airport_pickup': 2,    // 接机
            'airport_dropoff': 3,   // 送机
            'charter': 4,           // 包车
            'hourly': 4             // 按小时包车
        },

        // 默认车型
        defaultCarTypes: {
            1: 'Comfort 5 Seater',
            2: 'Premium 5 Seater', 
            3: 'Luxury 5 Seater',
            4: 'MPV 7 Seater',
            5: 'Premium MPV 7 Seater',
            6: 'Van 13 Seater'
        },

        // 根据乘客数量推荐车型
        carTypeByPassengers: {
            1: 1,  // 1人 -> Comfort 5 Seater
            2: 1,  // 2人 -> Comfort 5 Seater
            3: 1,  // 3人 -> Comfort 5 Seater
            4: 1,  // 4人 -> Comfort 5 Seater
            5: 4,  // 5人 -> MPV 7 Seater
            6: 4,  // 6人 -> MPV 7 Seater
            7: 4,  // 7人 -> MPV 7 Seater
            8: 6,  // 8人+ -> Van 13 Seater
        },

        // 默认语言
        defaultLanguages: {
            'english': 2,
            'chinese': 4,
            'malay': 1
        },

        // 常用地址
        commonAddresses: {
            airports: [
                'Kuala Lumpur International Airport (KLIA)',
                'Kuala Lumpur International Airport 2 (KLIA2)',
                'Sultan Abdul Aziz Shah Airport (Subang)'
            ],
            hotels: [
                'Petronas Twin Towers',
                'Bukit Bintang',
                'KLCC',
                'Mid Valley Megamall',
                'Pavilion KL'
            ]
        },

        // 货币转换率
        currencyRates: {
            'USD': 4.3,
            'SGD': 3.4, 
            'CNY': 0.615,
            'MYR': 1.0
        }
    };

    /**
     * 回退配置
     * 原文件：js/ai/gemini/configs/fallback-config.js
     */
    const fallbackConfig = {
        // 当AI解析失败时的默认值
        defaultValues: {
            service_type_id: 2,        // 默认接机服务
            car_type_id: 1,            // 默认Comfort 5 Seater
            languages_id_array: {"0": "2"}, // 默认英语
            pax: 1,                    // 默认1人
            luggage_number: 1,         // 默认1件行李
            currency: 'MYR',           // 默认马币
            ota: 'general'             // 默认通用OTA
        },

        // 错误处理配置
        errorHandling: {
            maxRetries: 3,             // 最大重试次数
            retryDelay: 1000,          // 重试延迟(毫秒)
            fallbackToManual: true,    // 失败后回退到手动输入
            showErrorDetails: false    // 是否显示详细错误信息
        },

        // 数据清理规则
        dataCleanup: {
            removeEmptyFields: true,   // 移除空字段
            trimWhitespace: true,      // 去除首尾空格
            normalizeCase: true,       // 标准化大小写
            validateRequired: true     // 验证必填字段
        }
    };

    /**
     * 性能优化配置
     * 原文件：js/ai/gemini/configs/performance-optimization.js
     */
    const performanceConfig = {
        // 缓存配置
        cache: {
            enabled: true,
            maxSize: 100,              // 最大缓存条目数
            ttl: 300000,               // 缓存生存时间(5分钟)
            keyPrefix: 'ota_gemini_'   // 缓存键前缀
        },

        // 请求限制
        rateLimit: {
            maxRequestsPerMinute: 60,  // 每分钟最大请求数
            burstLimit: 10,            // 突发请求限制
            cooldownPeriod: 1000       // 冷却期(毫秒)
        },

        // 批处理配置
        batch: {
            enabled: false,            // 暂时禁用批处理
            maxBatchSize: 5,           // 最大批处理大小
            batchTimeout: 2000         // 批处理超时时间
        },

        // 监控配置
        monitoring: {
            enabled: true,
            logPerformance: true,      // 记录性能指标
            alertThreshold: 5000,      // 响应时间告警阈值(毫秒)
            metricsRetention: 86400000 // 指标保留时间(24小时)
        }
    };

    /**
     * OTA参考模式配置
     * 原文件：js/ai/gemini/configs/ota-reference-patterns.js
     */
    const otaReferencePatterns = {
        // 各OTA平台的参考号模式
        patterns: {
            'chong-dealer': [
                /CD\d{6,10}/i,
                /CHONG[-_]?\d{6,10}/i
            ],
            'agoda': [
                /AG\d{8,12}/i,
                /AGODA[-_]?\d{8,12}/i,
                /\d{10,12}/  // Agoda通常使用纯数字
            ],
            'booking': [
                /BK\d{8,12}/i,
                /BOOKING[-_]?\d{8,12}/i,
                /\d{9,12}/   // Booking.com通常使用9-12位数字
            ],
            'klook': [
                /KLOOK[-_]?\d{6,10}/i,
                /KL\d{6,10}/i
            ],
            'kkday': [
                /KKDAY[-_]?\d{6,10}/i,
                /KK\d{6,10}/i
            ]
        },

        // 通用参考号提取规则
        genericPatterns: [
            /(?:订单号|Order|Reference|Ref|Booking)[:\s]*([A-Z0-9]{6,15})/i,
            /(?:确认号|Confirmation)[:\s]*([A-Z0-9]{6,15})/i,
            /([A-Z]{2,4}\d{6,12})/,  // 字母+数字组合
            /(\d{8,15})/             // 纯数字(8-15位)
        ],

        // 参考号验证规则
        validation: {
            minLength: 6,
            maxLength: 15,
            allowedChars: /^[A-Z0-9\-_]+$/i
        }
    };

    // 导出配置到全局命名空间
    window.OTA.ai.gemini.configs.fieldMapping = fieldMapping;
    window.OTA.ai.gemini.configs.presetValues = presetValues;
    window.OTA.ai.gemini.configs.fallbackConfig = fallbackConfig;
    window.OTA.ai.gemini.configs.performanceConfig = performanceConfig;
    window.OTA.ai.gemini.configs.otaReferencePatterns = otaReferencePatterns;

    // 提供配置访问器
    window.OTA.ai.gemini.getConfig = function(configName) {
        return window.OTA.ai.gemini.configs[configName] || null;
    };

    // 提供字段映射快捷方法
    window.OTA.ai.gemini.mapField = function(fieldName, direction = 'apiToForm') {
        const mapping = fieldMapping[direction];
        return mapping[fieldName] || fieldName;
    };

    // 提供预设值获取方法
    window.OTA.ai.gemini.getPresetValue = function(category, key) {
        const preset = presetValues[category];
        return preset ? preset[key] : null;
    };

    console.log('✅ Gemini配置模块已加载');

})();
