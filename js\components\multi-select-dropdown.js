/**
 * 多选下拉组件 (优化版)
 * 提供简洁的多选下拉菜单功能
 * 与GlobalEventCoordinator协同工作
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.components = window.OTA.components || {};

(function() {
    'use strict';

    /**
     * 多选下拉组件类 (优化版)
     */
    class MultiSelectDropdown {
        constructor(containerId, options = {}) {
            this.containerId = containerId;
            this.container = typeof containerId === 'string' ? 
                            document.getElementById(containerId) : containerId;
            
            if (!this.container) {
                throw new Error(`多选下拉容器未找到: ${containerId}`);
            }

            this.options = {
                placeholder: '请选择选项',
                searchPlaceholder: '搜索选项...',
                maxDisplay: 3,
                allowSearch: true,
                closeOnSelect: false,
                ...options
            };

            this.data = [];
            this.selectedValues = new Set();
            this.isOpen = false;
            this.searchTerm = '';
            
            this.init();
        }

        /**
         * 初始化组件
         */
        init() {
            this.createStructure();
            this.bindEvents();
            this.registerWithGlobalCoordinator();
            getLogger().log('多选下拉组件初始化完成', 'info', { containerId: this.containerId });
        }

        /**
         * 创建DOM结构
         */
        createStructure() {
            this.container.className = 'multi-select-dropdown';
            this.container.innerHTML = `
                <div class="msd-selected" tabindex="0">
                    <span class="msd-placeholder">${this.options.placeholder}</span>
                    <span class="msd-arrow">▼</span>
                </div>
                <div class="msd-dropdown" style="display: none;">
                    ${this.options.allowSearch ? 
                        `<input type="text" class="msd-search" placeholder="${this.options.searchPlaceholder}">` : ''
                    }
                    <div class="msd-options"></div>
                </div>
            `;

            this.elements = {
                selected: this.container.querySelector('.msd-selected'),
                placeholder: this.container.querySelector('.msd-placeholder'),
                arrow: this.container.querySelector('.msd-arrow'),
                dropdown: this.container.querySelector('.msd-dropdown'),
                search: this.container.querySelector('.msd-search'),
                options: this.container.querySelector('.msd-options')
            };

            this.addStyles();
        }

        /**
         * 添加样式
         */
        addStyles() {
            if (document.getElementById('multi-select-dropdown-styles')) return;

            const style = document.createElement('style');
            style.id = 'multi-select-dropdown-styles';
            style.textContent = `
                .multi-select-dropdown {
                    position: relative;
                    display: inline-block;
                    min-width: 200px;
                }

                .msd-selected {
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 8px 12px;
                    background: white;
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    min-height: 20px;
                }

                .msd-selected:focus {
                    outline: none;
                    border-color: #007bff;
                    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
                }

                .msd-arrow {
                    transition: transform 0.2s;
                    font-size: 12px;
                    color: #666;
                }

                .msd-arrow.open {
                    transform: rotate(180deg);
                }

                .msd-dropdown {
                    position: absolute;
                    top: 100%;
                    left: 0;
                    right: 0;
                    background: white;
                    border: 1px solid #ddd;
                    border-top: none;
                    border-radius: 0 0 4px 4px;
                    max-height: 200px;
                    overflow-y: auto;
                    z-index: 1000;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }

                .msd-search {
                    width: 100%;
                    padding: 8px;
                    border: none;
                    border-bottom: 1px solid #eee;
                    outline: none;
                }

                .msd-option {
                    padding: 8px 12px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .msd-option:hover {
                    background: #f5f5f5;
                }

                .msd-option.selected {
                    background: #e3f2fd;
                }

                .msd-checkbox {
                    width: 16px;
                    height: 16px;
                    cursor: pointer;
                }

                .msd-selected-items {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 4px;
                    margin-right: 8px;
                }

                .msd-selected-item {
                    background: #007bff;
                    color: white;
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    display: flex;
                    align-items: center;
                    gap: 4px;
                }

                .msd-remove {
                    cursor: pointer;
                    font-weight: bold;
                }
            `;
            document.head.appendChild(style);
        }

        /**
         * 绑定事件
         */
        bindEvents() {
            // 点击选中区域
            this.elements.selected.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggle();
            });

            // 键盘事件
            this.elements.selected.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggle();
                } else if (e.key === 'Escape') {
                    this.close();
                }
            });

            // 搜索事件
            if (this.elements.search) {
                this.elements.search.addEventListener('input', (e) => {
                    this.searchTerm = e.target.value.toLowerCase();
                    this.renderOptions();
                });

                this.elements.search.addEventListener('keydown', (e) => {
                    e.stopPropagation();
                });
            }

            // 选项点击事件 (事件委托)
            this.elements.options.addEventListener('click', (e) => {
                const option = e.target.closest('.msd-option');
                if (option) {
                    this.toggleOption(option.dataset.value);
                }
            });
        }

        /**
         * 注册到全局事件协调器
         */
        registerWithGlobalCoordinator() {
            const coordinator = window.OTA?.globalEventCoordinator;
            if (coordinator) {
                coordinator.registerComponent(this.containerId, this, {
                    type: 'dropdown',
                    preventAutoClose: false
                });
            }
        }

        /**
         * 设置数据
         */
        setData(data) {
            this.data = Array.isArray(data) ? data : [];
            this.renderOptions();
            this.updateDisplay();
        }

        /**
         * 渲染选项
         */
        renderOptions() {
            if (!this.elements.options) return;

            const filteredData = this.searchTerm ? 
                this.data.filter(item => 
                    item.name?.toLowerCase().includes(this.searchTerm) ||
                    item.label?.toLowerCase().includes(this.searchTerm)
                ) : this.data;

            this.elements.options.innerHTML = filteredData.map(item => {
                const value = item.value || item.id;
                const label = item.name || item.label || value;
                const isSelected = this.selectedValues.has(String(value));

                return `
                    <div class="msd-option ${isSelected ? 'selected' : ''}" data-value="${value}">
                        <input type="checkbox" class="msd-checkbox" ${isSelected ? 'checked' : ''} tabindex="-1">
                        <span>${label}</span>
                    </div>
                `;
            }).join('');
        }

        /**
         * 切换选项
         */
        toggleOption(value) {
            const stringValue = String(value);
            
            if (this.selectedValues.has(stringValue)) {
                this.selectedValues.delete(stringValue);
            } else {
                this.selectedValues.add(stringValue);
            }

            this.renderOptions();
            this.updateDisplay();
            this.triggerChange();

            if (this.options.closeOnSelect && this.selectedValues.size === 1) {
                this.close();
            }
        }

        /**
         * 更新显示
         */
        updateDisplay() {
            if (this.selectedValues.size === 0) {
                this.elements.placeholder.innerHTML = this.options.placeholder;
                return;
            }

            const selectedItems = Array.from(this.selectedValues).map(value => {
                const item = this.data.find(d => String(d.value || d.id) === value);
                return item ? (item.name || item.label || value) : value;
            });

            if (selectedItems.length <= this.options.maxDisplay) {
                const itemsHtml = selectedItems.map(item => 
                    `<span class="msd-selected-item">
                        ${item}
                        <span class="msd-remove" data-value="${item}">×</span>
                    </span>`
                ).join('');
                
                this.elements.placeholder.innerHTML = `
                    <div class="msd-selected-items">${itemsHtml}</div>
                `;
            } else {
                this.elements.placeholder.innerHTML = 
                    `已选择 ${selectedItems.length} 项`;
            }

            // 绑定删除事件
            this.elements.placeholder.querySelectorAll('.msd-remove').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const item = this.data.find(d => 
                        (d.name || d.label) === btn.dataset.value
                    );
                    if (item) {
                        this.toggleOption(item.value || item.id);
                    }
                });
            });
        }

        /**
         * 打开下拉菜单
         */
        open() {
            if (this.isOpen) return;

            this.isOpen = true;
            this.elements.dropdown.style.display = 'block';
            this.elements.arrow.classList.add('open');
            
            if (this.elements.search) {
                this.elements.search.focus();
            }

            // 通知全局协调器
            const coordinator = window.OTA?.globalEventCoordinator;
            if (coordinator) {
                coordinator.updateComponentState(this.containerId, { isOpen: true });
            }
        }

        /**
         * 关闭下拉菜单
         */
        close() {
            if (!this.isOpen) return;

            this.isOpen = false;
            this.elements.dropdown.style.display = 'none';
            this.elements.arrow.classList.remove('open');
            this.searchTerm = '';
            
            if (this.elements.search) {
                this.elements.search.value = '';
            }

            this.renderOptions();

            // 通知全局协调器
            const coordinator = window.OTA?.globalEventCoordinator;
            if (coordinator) {
                coordinator.updateComponentState(this.containerId, { isOpen: false });
            }
        }

        /**
         * 切换下拉菜单
         */
        toggle() {
            if (this.isOpen) {
                this.close();
            } else {
                this.open();
            }
        }

        /**
         * 获取选中的值
         */
        getSelectedValues() {
            return Array.from(this.selectedValues);
        }

        /**
         * 设置选中的值
         */
        setSelectedValues(values) {
            this.selectedValues = new Set(values.map(v => String(v)));
            this.renderOptions();
            this.updateDisplay();
            this.triggerChange();
        }

        /**
         * 清空选择
         */
        clear() {
            this.selectedValues.clear();
            this.renderOptions();
            this.updateDisplay();
            this.triggerChange();
        }

        /**
         * 触发变化事件
         */
        triggerChange() {
            const event = new CustomEvent('multiSelectChange', {
                detail: {
                    selectedValues: this.getSelectedValues(),
                    component: this
                }
            });
            this.container.dispatchEvent(event);
        }

        /**
         * 销毁组件
         */
        destroy() {
            // 注销全局协调器
            const coordinator = window.OTA?.globalEventCoordinator;
            if (coordinator) {
                coordinator.unregisterComponent(this.containerId);
            }

            // 清理DOM
            this.container.innerHTML = '';
            this.container.className = '';
            
            getLogger().log('多选下拉组件已销毁', 'info', { containerId: this.containerId });
        }
    }

    // 导出到全局命名空间
    window.OTA.components.MultiSelectDropdown = MultiSelectDropdown;

    // 注册到依赖容器 (作为UI组件工厂)
    if (window.OTA && window.OTA.container) {
        try {
            window.OTA.container.registerUIComponent('multiSelectDropdown', MultiSelectDropdown);
            getLogger().log('MultiSelectDropdown组件已注册', 'info');
        } catch (error) {
            console.warn('MultiSelectDropdown组件注册失败:', error.message);
        }
    }

    // 向后兼容
    window.MultiSelectDropdown = MultiSelectDropdown;

})();