<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务依赖链修复验证测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .summary {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .log-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 服务依赖链修复验证测试</h1>
        <p>此测试验证方案A（保守统一方案）的实施效果，确保所有服务正确迁移到dependency-container架构。</p>
        
        <button onclick="runTests()">🚀 开始测试</button>
        <button onclick="clearResults()">🧹 清除结果</button>
        
        <div id="testResults"></div>
        <div id="logOutput" class="log-output" style="display: none;"></div>
    </div>

    <!-- 加载核心依赖 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/services/logger.js"></script>
    <script src="js/ai/gemini/core/config-manager.js"></script>
    <script src="js/ai/gemini/gemini-coordinator.js"></script>
    <script src="js/services/api-service.js"></script>

    <script>
        let testResults = [];
        let logMessages = [];

        // 重写console.log来捕获日志
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        console.log = function(...args) {
            logMessages.push(`[LOG] ${args.join(' ')}`);
            originalConsoleLog.apply(console, args);
        };

        console.error = function(...args) {
            logMessages.push(`[ERROR] ${args.join(' ')}`);
            originalConsoleError.apply(console, args);
        };

        console.warn = function(...args) {
            logMessages.push(`[WARN] ${args.join(' ')}`);
            originalConsoleWarn.apply(console, args);
        };

        function addTestResult(name, passed, message, type = 'test') {
            testResults.push({
                name,
                passed,
                message,
                type,
                timestamp: new Date().toLocaleTimeString()
            });
        }

        function runTests() {
            testResults = [];
            logMessages = [];
            
            console.log('🔍 开始服务依赖链修复验证测试...');

            // 测试1: 验证service-locator可用
            try {
                const serviceLocatorAvailable = !!(window.OTA && window.OTA.getService);
                addTestResult(
                    '服务定位器可用性',
                    serviceLocatorAvailable,
                    serviceLocatorAvailable ? 'window.OTA.getService 方法可用' : 'window.OTA.getService 方法不可用'
                );
            } catch (error) {
                addTestResult('服务定位器可用性', false, `测试失败: ${error.message}`);
            }

            // 测试2: 验证configManager服务
            try {
                const configManager = window.OTA.getService('configManager');
                const configManagerWorking = !!(configManager && configManager.getConfig);
                addTestResult(
                    'ConfigManager服务',
                    configManagerWorking,
                    configManagerWorking ? 'ConfigManager服务正常工作' : 'ConfigManager服务不可用'
                );
            } catch (error) {
                addTestResult('ConfigManager服务', false, `获取失败: ${error.message}`);
            }

            // 测试3: 验证logger服务
            try {
                const logger = window.OTA.getService('logger');
                const loggerWorking = !!(logger && logger.log);
                addTestResult(
                    'Logger服务',
                    loggerWorking,
                    loggerWorking ? 'Logger服务正常工作' : 'Logger服务不可用'
                );
            } catch (error) {
                addTestResult('Logger服务', false, `获取失败: ${error.message}`);
            }

            // 测试4: 验证apiService
            try {
                const apiService = window.OTA.getService('apiService');
                const apiServiceWorking = !!(apiService && apiService.login);
                addTestResult(
                    'ApiService服务',
                    apiServiceWorking,
                    apiServiceWorking ? 'ApiService服务正常工作' : 'ApiService服务不可用'
                );
            } catch (error) {
                addTestResult('ApiService服务', false, `获取失败: ${error.message}`);
            }

            // 测试5: 验证geminiCoordinator
            try {
                const geminiCoordinator = window.OTA.getService('geminiCoordinator');
                const geminiWorking = !!(geminiCoordinator && geminiCoordinator.processOrder);
                addTestResult(
                    'GeminiCoordinator服务',
                    geminiWorking,
                    geminiWorking ? 'GeminiCoordinator服务正常工作' : 'GeminiCoordinator服务不可用'
                );
            } catch (error) {
                addTestResult('GeminiCoordinator服务', false, `获取失败: ${error.message}`);
            }

            // 测试6: 验证ota-registry已被移除
            try {
                const otaRegistryExists = !!(window.OTA && window.OTA.Registry);
                addTestResult(
                    'OTA Registry清理状态',
                    !otaRegistryExists,
                    otaRegistryExists ? '⚠️ OTA Registry仍然存在，可能未完全清理' : '✅ OTA Registry已成功移除'
                );
            } catch (error) {
                addTestResult('OTA Registry清理状态', true, 'OTA Registry已移除');
            }

            // 计算总体成功率
            const passedTests = testResults.filter(r => r.passed).length;
            const totalTests = testResults.length;
            const successRate = Math.round((passedTests / totalTests) * 100);

            addTestResult(
                '总体测试结果',
                successRate >= 80,
                `${passedTests}/${totalTests} 测试通过 (${successRate}%)`,
                'summary'
            );

            displayResults();
        }

        function displayResults() {
            const resultsDiv = document.getElementById('testResults');
            const logDiv = document.getElementById('logOutput');
            
            let html = '<h2>📊 测试结果</h2>';
            
            testResults.forEach(result => {
                const cssClass = result.type === 'summary' ? 
                    (result.passed ? 'test-pass' : 'test-warning') :
                    (result.passed ? 'test-pass' : 'test-fail');
                
                html += `
                    <div class="${cssClass} test-result">
                        <strong>${result.name}</strong>: ${result.message}
                        <small style="float: right;">${result.timestamp}</small>
                    </div>
                `;
            });

            resultsDiv.innerHTML = html;

            // 显示日志
            if (logMessages.length > 0) {
                logDiv.style.display = 'block';
                logDiv.innerHTML = '<h3>📝 系统日志</h3>' + logMessages.join('<br>');
            }
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('logOutput').style.display = 'none';
            testResults = [];
            logMessages = [];
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            setTimeout(runTests, 1000); // 延迟1秒确保所有脚本加载完成
        });
    </script>
</body>
</html>
