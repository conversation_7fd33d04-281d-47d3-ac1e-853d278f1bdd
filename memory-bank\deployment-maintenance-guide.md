# OTA系统部署与维护指南

## 📋 文档概述

**更新日期**: 2025-08-02
**版本**: v2.0 (微服务化架构)
**状态**: 微服务架构部署指南完成

## 🚀 部署指南

### 系统要求

#### 运行环境
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **JavaScript**: ES6+ 支持
- **网络**: HTTPS连接（生产环境必需）
- **存储**: localStorage支持

#### 服务依赖
- **GoMyHire API**: 后端订单管理服务
- **Google Gemini AI**: AI订单解析服务
- **Netlify**: 静态站点托管平台

### 部署流程

#### 1. 静态文件部署 (Netlify)

**步骤1: 准备部署文件**
```bash
# 确保所有必需文件存在
├── index.html                     # 主页面
├── status.html                    # 状态监控页面
├── js/
│   ├── main.js                    # 应用入口
│   ├── core/                      # 核心基础设施
│   ├── services/                  # 微服务模块
│   ├── config/                    # 配置文件
│   └── utils/                     # 工具库
├── css/                           # 样式文件
├── netlify.toml                   # Netlify配置
└── _redirects                     # 路由重定向
```

**步骤2: Netlify配置**
```toml
# netlify.toml
[build]
  publish = "."
  command = "echo 'No build required for static site'"

[build.environment]
  NODE_VERSION = "18"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' https://api.gomyhire.com https://generativelanguage.googleapis.com"

[[redirects]]
  from = "/api/*"
  to = "https://api.gomyhire.com/:splat"
  status = 200
  force = true
```

**步骤3: 环境变量配置**
```bash
# Netlify环境变量
GEMINI_API_KEY=your_gemini_api_key_here
GOMYHIRE_API_URL=https://api.gomyhire.com
NODE_ENV=production
```

#### 2. 微服务初始化验证

**部署后验证脚本**
```javascript
// 部署验证脚本
async function validateDeployment() {
    const checks = [];
    
    // 1. 检查微服务注册
    const serviceLocator = window.OTA?.serviceLocator;
    if (!serviceLocator) {
        checks.push({ name: '服务定位器', status: 'FAIL', message: '服务定位器未初始化' });
        return checks;
    }
    
    // 2. 检查各个微服务
    const requiredServices = ['uiService', 'formService', 'priceService', 'stateService', 'eventService'];
    
    for (const serviceName of requiredServices) {
        const service = window.OTA.getService(serviceName);
        if (!service) {
            checks.push({ name: serviceName, status: 'FAIL', message: '服务未注册' });
        } else if (!service.isAvailable()) {
            checks.push({ name: serviceName, status: 'WARN', message: '服务不可用' });
        } else {
            checks.push({ name: serviceName, status: 'PASS', message: '服务正常' });
        }
    }
    
    // 3. 检查API连接
    try {
        const response = await fetch('/api/health');
        if (response.ok) {
            checks.push({ name: 'API连接', status: 'PASS', message: 'API连接正常' });
        } else {
            checks.push({ name: 'API连接', status: 'WARN', message: `API响应异常: ${response.status}` });
        }
    } catch (error) {
        checks.push({ name: 'API连接', status: 'FAIL', message: `API连接失败: ${error.message}` });
    }
    
    return checks;
}

// 在控制台运行验证
validateDeployment().then(checks => {
    console.table(checks);
});
```

## 🔍 服务健康检查

### 自动健康检查系统

#### 1. 微服务健康监控
```javascript
// 微服务健康检查器
class MicroserviceHealthChecker {
    constructor() {
        this.checkInterval = 30000; // 30秒检查一次
        this.healthHistory = new Map();
        this.alertThreshold = 3; // 连续3次失败后告警
    }
    
    async startMonitoring() {
        setInterval(async () => {
            await this.performHealthCheck();
        }, this.checkInterval);
    }
    
    async performHealthCheck() {
        const services = ['uiService', 'formService', 'priceService', 'stateService', 'eventService'];
        const healthReport = {
            timestamp: new Date().toISOString(),
            services: {},
            overall: 'HEALTHY'
        };
        
        for (const serviceName of services) {
            const service = window.OTA.getService(serviceName);
            const health = await this.checkServiceHealth(serviceName, service);
            healthReport.services[serviceName] = health;
            
            if (health.status !== 'HEALTHY') {
                healthReport.overall = 'DEGRADED';
            }
        }
        
        this.recordHealthHistory(healthReport);
        this.checkAlertConditions(healthReport);
        
        return healthReport;
    }
    
    async checkServiceHealth(serviceName, service) {
        if (!service) {
            return {
                status: 'CRITICAL',
                message: '服务未注册',
                responseTime: null
            };
        }
        
        const startTime = performance.now();
        
        try {
            const status = service.getStatus();
            const responseTime = performance.now() - startTime;
            
            if (!service.isAvailable()) {
                return {
                    status: 'UNHEALTHY',
                    message: '服务不可用',
                    responseTime,
                    details: status
                };
            }
            
            return {
                status: 'HEALTHY',
                message: '服务正常',
                responseTime,
                details: status
            };
            
        } catch (error) {
            return {
                status: 'CRITICAL',
                message: `服务检查失败: ${error.message}`,
                responseTime: performance.now() - startTime
            };
        }
    }
    
    recordHealthHistory(report) {
        const key = report.timestamp.substring(0, 16); // 精确到分钟
        this.healthHistory.set(key, report);
        
        // 保留最近24小时的记录
        const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().substring(0, 16);
        for (const [timestamp] of this.healthHistory) {
            if (timestamp < cutoff) {
                this.healthHistory.delete(timestamp);
            }
        }
    }
    
    checkAlertConditions(report) {
        // 检查是否需要发送告警
        for (const [serviceName, health] of Object.entries(report.services)) {
            if (health.status === 'CRITICAL' || health.status === 'UNHEALTHY') {
                this.handleServiceAlert(serviceName, health);
            }
        }
    }
    
    handleServiceAlert(serviceName, health) {
        console.error(`🚨 服务告警: ${serviceName}`, health);
        
        // 尝试自动恢复
        this.attemptServiceRecovery(serviceName);
    }
    
    async attemptServiceRecovery(serviceName) {
        console.log(`🔧 尝试恢复服务: ${serviceName}`);
        
        const service = window.OTA.getService(serviceName);
        if (service && typeof service.init === 'function') {
            try {
                await service.init();
                console.log(`✅ 服务恢复成功: ${serviceName}`);
            } catch (error) {
                console.error(`❌ 服务恢复失败: ${serviceName}`, error);
            }
        }
    }
}

// 启动健康检查
const healthChecker = new MicroserviceHealthChecker();
healthChecker.startMonitoring();
```

#### 2. 性能监控
```javascript
// 性能监控器
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            startupTime: null,
            memoryUsage: [],
            apiResponseTimes: [],
            serviceResponseTimes: new Map()
        };
    }
    
    recordStartupTime() {
        this.metrics.startupTime = performance.now();
    }
    
    recordMemoryUsage() {
        if (performance.memory) {
            this.metrics.memoryUsage.push({
                timestamp: Date.now(),
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            });
            
            // 保留最近100个记录
            if (this.metrics.memoryUsage.length > 100) {
                this.metrics.memoryUsage.shift();
            }
        }
    }
    
    recordServiceCall(serviceName, methodName, duration) {
        const key = `${serviceName}.${methodName}`;
        if (!this.metrics.serviceResponseTimes.has(key)) {
            this.metrics.serviceResponseTimes.set(key, []);
        }
        
        const times = this.metrics.serviceResponseTimes.get(key);
        times.push({ timestamp: Date.now(), duration });
        
        // 保留最近50个记录
        if (times.length > 50) {
            times.shift();
        }
    }
    
    getPerformanceReport() {
        const report = {
            startup: {
                time: this.metrics.startupTime,
                status: this.metrics.startupTime < 1000 ? 'GOOD' : 'SLOW'
            },
            memory: this.getMemoryReport(),
            services: this.getServicePerformanceReport()
        };
        
        return report;
    }
    
    getMemoryReport() {
        if (this.metrics.memoryUsage.length === 0) {
            return { status: 'NO_DATA' };
        }
        
        const latest = this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1];
        const usagePercent = (latest.used / latest.limit) * 100;
        
        return {
            current: latest.used,
            percentage: usagePercent.toFixed(2),
            status: usagePercent < 50 ? 'GOOD' : usagePercent < 80 ? 'WARNING' : 'CRITICAL'
        };
    }
    
    getServicePerformanceReport() {
        const report = {};
        
        for (const [service, times] of this.metrics.serviceResponseTimes) {
            if (times.length > 0) {
                const durations = times.map(t => t.duration);
                const avg = durations.reduce((a, b) => a + b, 0) / durations.length;
                const max = Math.max(...durations);
                
                report[service] = {
                    averageTime: avg.toFixed(2),
                    maxTime: max.toFixed(2),
                    callCount: times.length,
                    status: avg < 10 ? 'GOOD' : avg < 50 ? 'WARNING' : 'SLOW'
                };
            }
        }
        
        return report;
    }
}

// 启动性能监控
const performanceMonitor = new PerformanceMonitor();
performanceMonitor.recordStartupTime();

// 定期记录内存使用
setInterval(() => {
    performanceMonitor.recordMemoryUsage();
}, 10000); // 每10秒记录一次
```

## 🛠️ 故障排除指南

### 常见问题诊断

#### 1. 微服务初始化失败

**症状**: 控制台显示"服务未注册"或"服务不可用"

**诊断步骤**:
```javascript
// 1. 检查服务定位器
console.log('服务定位器状态:', window.OTA?.serviceLocator);

// 2. 检查已注册的服务
if (window.OTA?.serviceLocator) {
    console.log('已注册服务:', Array.from(window.OTA.serviceLocator.services.keys()));
}

// 3. 检查特定服务状态
const serviceName = 'uiService'; // 替换为问题服务
const service = window.OTA.getService(serviceName);
if (service) {
    console.log(`${serviceName} 状态:`, service.getStatus());
} else {
    console.error(`${serviceName} 未注册`);
}
```

**解决方案**:
1. 检查script标签加载顺序
2. 确认服务文件路径正确
3. 检查JavaScript语法错误
4. 重新初始化服务

#### 2. API连接问题

**症状**: API调用失败，网络错误

**诊断步骤**:
```javascript
// 1. 检查API服务状态
const apiService = window.OTA.getService('apiService');
if (apiService) {
    console.log('API服务状态:', apiService.getStatus());
    
    // 2. 测试API连接
    apiService.testConnection().then(result => {
        console.log('API连接测试:', result);
    }).catch(error => {
        console.error('API连接失败:', error);
    });
}

// 3. 检查网络状态
console.log('网络状态:', navigator.onLine ? '在线' : '离线');
```

**解决方案**:
1. 检查网络连接
2. 验证API密钥配置
3. 检查CORS设置
4. 确认API服务器状态

#### 3. 性能问题

**症状**: 页面响应缓慢，内存使用过高

**诊断步骤**:
```javascript
// 1. 获取性能报告
const report = performanceMonitor.getPerformanceReport();
console.log('性能报告:', report);

// 2. 检查内存使用
if (performance.memory) {
    const memory = performance.memory;
    console.log('内存使用:', {
        used: (memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + ' MB',
        total: (memory.totalJSHeapSize / 1024 / 1024).toFixed(2) + ' MB',
        limit: (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2) + ' MB'
    });
}

// 3. 检查事件监听器
const eventService = window.OTA.getService('eventService');
if (eventService) {
    console.log('事件处理器数量:', eventService.getHandlerCount());
}
```

**解决方案**:
1. 清理未使用的事件监听器
2. 优化DOM操作
3. 减少内存泄漏
4. 使用防抖和节流

### 紧急恢复程序

#### 1. 服务重启
```javascript
// 紧急重启所有微服务
async function emergencyRestart() {
    console.log('🚨 执行紧急重启...');
    
    try {
        // 1. 销毁所有服务
        await window.OTA.serviceLocator.destroyAllMicroservices();
        
        // 2. 清理服务注册
        window.OTA.serviceLocator.services.clear();
        
        // 3. 重新注册和初始化服务
        await window.initializeModules();
        
        console.log('✅ 紧急重启完成');
        
        // 4. 验证服务状态
        const healthReport = await healthChecker.performHealthCheck();
        console.log('重启后健康检查:', healthReport);
        
    } catch (error) {
        console.error('❌ 紧急重启失败:', error);
    }
}
```

#### 2. 配置重置
```javascript
// 重置所有配置到默认值
function resetToDefaults() {
    console.log('🔄 重置配置到默认值...');
    
    // 清理localStorage
    const keysToKeep = ['authToken', 'userPreferences'];
    const allKeys = Object.keys(localStorage);
    
    allKeys.forEach(key => {
        if (!keysToKeep.includes(key)) {
            localStorage.removeItem(key);
        }
    });
    
    // 重新加载页面
    window.location.reload();
}
```

---

**文档维护**: 随系统演进持续更新
**最后更新**: 2025-08-02 (微服务化架构重构完成)
**维护指南版本**: v2.0
