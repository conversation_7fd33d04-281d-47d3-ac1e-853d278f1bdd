/**
 * 状态管理器模块 (优化版)
 * 负责UI状态管理、主题控制和状态展示
 * 与AppState协同工作，专注于UI层面的状态管理
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    /**
     * 状态管理器类 (优化版)
     * 专注于UI状态展示，与AppState协同工作
     */
    class StateManager {
        constructor() {
            this.initialized = false;
            this.appState = null;
            this.uiElements = {};
        }

        /**
         * 初始化状态管理器
         */
        init() {
            if (this.initialized) {
                getLogger().log('状态管理器已经初始化', 'warning');
                return;
            }

            this.appState = window.OTA?.appState || window.appState;
            this.cacheUIElements();
            this.setupThemeToggle();
            this.setupStateListeners();
            this.updateAllUIStatus();
            this.initialized = true;
            getLogger().log('状态管理器初始化完成', 'success');
        }

        /**
         * 缓存UI元素
         */
        cacheUIElements() {
            this.uiElements = {
                themeToggle: document.getElementById('themeToggle'),
                geminiStatus: document.getElementById('geminiStatus'),
                connectionStatus: document.getElementById('connectionStatus'),
                loginStatus: document.getElementById('loginStatus'),
                userInfo: document.getElementById('userInfo')
            };
        }

        /**
         * 设置主题切换功能
         */
        setupThemeToggle() {
            if (this.uiElements.themeToggle) {
                this.uiElements.themeToggle.addEventListener('click', () => {
                    this.toggleTheme();
                });
                
                // 初始化主题图标
                this.updateThemeIcon();
            }
        }

        /**
         * 设置状态监听器
         */
        setupStateListeners() {
            if (this.appState) {
                // 监听主题变化
                this.appState.on('config.theme', () => {
                    this.updateThemeIcon();
                });

                // 监听登录状态变化
                this.appState.on('auth.isLoggedIn', () => {
                    this.updateLoginStatus();
                });

                // 监听连接状态变化
                this.appState.on('system.connected', () => {
                    this.updateConnectionStatus();
                });
            }
        }

        /**
         * 切换主题
         * @deprecated 此方法已废弃，主题切换已统一到StateService微服务
         */
        toggleTheme() {
            // 🗑️ 已移除重复的主题切换逻辑 - 减法重构优化
            // 主题切换功能已统一到StateService微服务中
            const stateService = window.OTA?.getService?.('stateService');
            if (stateService && typeof stateService.toggleTheme === 'function') {
                stateService.toggleTheme();
            } else {
                getLogger().log('StateService不可用，无法切换主题', 'warning');
            }
        }

        /**
         * 更新主题图标
         */
        updateThemeIcon() {
            if (!this.uiElements.themeToggle || !this.appState) return;

            const currentTheme = this.appState.get('config.theme') || 'light';
            const icon = currentTheme === 'light' ? '🌙' : '☀️';
            const title = currentTheme === 'light' ? '切换到深色模式' : '切换到浅色模式';
            
            this.uiElements.themeToggle.textContent = icon;
            this.uiElements.themeToggle.title = title;
        }

        /**
         * 更新Gemini状态
         */
        updateGeminiStatus(status = null) {
            if (!this.uiElements.geminiStatus) return;

            let statusInfo;
            if (status) {
                statusInfo = status;
            } else {
                // 从服务获取状态
                const geminiService = window.OTA?.geminiService || window.geminiService;
                statusInfo = geminiService ? {
                    available: geminiService.isAvailable?.() || false,
                    analyzing: false
                } : { available: false, analyzing: false };
            }

            const statusText = statusInfo.analyzing ? '分析中...' :
                              statusInfo.available ? '就绪' : '不可用';
            const statusClass = statusInfo.analyzing ? 'status-processing' :
                               statusInfo.available ? 'status-ready' : 'status-unavailable';

            this.uiElements.geminiStatus.textContent = statusText;
            this.uiElements.geminiStatus.className = `status-indicator ${statusClass}`;
        }

        /**
         * 更新连接状态
         */
        updateConnectionStatus() {
            if (!this.uiElements.connectionStatus || !this.appState) return;

            const connected = this.appState.get('system.connected') || false;
            const statusText = connected ? '已连接' : '未连接';
            const statusClass = connected ? 'status-connected' : 'status-disconnected';

            this.uiElements.connectionStatus.textContent = statusText;
            this.uiElements.connectionStatus.className = `status-indicator ${statusClass}`;
        }

        /**
         * 更新登录状态
         */
        updateLoginStatus() {
            if (!this.appState) return;

            const isLoggedIn = this.appState.get('auth.isLoggedIn') || false;
            const user = this.appState.get('auth.user');

            // 更新登录状态指示器
            if (this.uiElements.loginStatus) {
                const statusText = isLoggedIn ? '已登录' : '未登录';
                const statusClass = isLoggedIn ? 'status-logged-in' : 'status-logged-out';
                
                this.uiElements.loginStatus.textContent = statusText;
                this.uiElements.loginStatus.className = `status-indicator ${statusClass}`;
            }

            // 更新用户信息显示
            if (this.uiElements.userInfo && isLoggedIn && user) {
                // 修复：只更新currentUser元素，不覆盖整个userInfo内容
                const currentUserElement = document.getElementById('currentUser');
                if (currentUserElement) {
                    currentUserElement.textContent = user.name || user.username || '用户';
                }
                this.uiElements.userInfo.style.display = 'inline';
            } else if (this.uiElements.userInfo) {
                this.uiElements.userInfo.style.display = 'none';
            }
        }

        /**
         * 更新所有UI状态
         */
        updateUI() {
            this.updateThemeIcon();
            this.updateGeminiStatus();
            this.updateConnectionStatus();
            this.updateLoginStatus();
            getLogger().log('所有UI状态已更新', 'info');
        }

        /**
         * 更新所有UI状态 (别名方法)
         */
        updateAllUIStatus() {
            this.updateUI();
        }

        /**
         * 设置Gemini分析状态
         */
        setGeminiAnalyzing(analyzing) {
            this.updateGeminiStatus({
                available: true,
                analyzing
            });
        }

        /**
         * 显示状态消息
         */
        showStatusMessage(message, type = 'info', duration = 3000) {
            // 使用UIManager显示消息
            const uiManager = window.OTA?.uiManager || window.uiManager;
            if (uiManager && uiManager.showAlert) {
                uiManager.showAlert(message, type, duration);
            } else {
                // 降级到控制台
                console.log(`[${type.toUpperCase()}] ${message}`);
            }
        }

        /**
         * 获取当前状态快照
         */
        getStateSnapshot() {
            if (!this.appState) return {};

            return {
                theme: this.appState.get('config.theme'),
                isLoggedIn: this.appState.get('auth.isLoggedIn'),
                connected: this.appState.get('system.connected'),
                user: this.appState.get('auth.user'),
                errors: this.appState.get('system.errors') || []
            };
        }

        /**
         * 监听状态变化
         */
        onStateChange(path, callback) {
            if (this.appState && this.appState.on) {
                this.appState.on(path, callback);
            }
        }

        /**
         * 取消状态监听
         */
        offStateChange(path, callback) {
            if (this.appState && this.appState.off) {
                this.appState.off(path, callback);
            }
        }

        /**
         * 重置UI状态
         */
        resetUIState() {
            // 重置主题到默认
            if (this.appState) {
                this.appState.set('config.theme', 'light');
            }
            document.documentElement.setAttribute('data-theme', 'light');
            localStorage.setItem('ota_theme_preference', 'light');

            // 更新所有UI
            this.updateUI();
            
            getLogger().log('UI状态已重置', 'info');
        }

        /**
         * 获取状态统计
         */
        getStatusStats() {
            return {
                initialized: this.initialized,
                hasAppState: !!this.appState,
                elementsFound: Object.values(this.uiElements).filter(Boolean).length,
                totalElements: Object.keys(this.uiElements).length,
                currentState: this.getStateSnapshot()
            };
        }

        /**
         * 检查是否可用
         */
        isAvailable() {
            return this.initialized && !!this.appState;
        }

        /**
         * 销毁状态管理器
         */
        destroy() {
            // 移除事件监听器
            if (this.uiElements.themeToggle) {
                this.uiElements.themeToggle.removeEventListener('click', this.toggleTheme);
            }

            this.initialized = false;
            getLogger().log('状态管理器已销毁', 'info');
        }
    }

    /**
     * 状态管理器适配器
     * 提供向后兼容的接口
     */
    function getStateManagerAdapter() {
        const stateManager = window.OTA?.stateManager || window.stateManager;
        if (!stateManager) {
            getLogger().log('状态管理器不可用', 'warning');
            return null;
        }

        return {
            updateUI: () => stateManager.updateUI(),
            updateGeminiStatus: (status) => stateManager.updateGeminiStatus(status),
            updateConnectionStatus: () => stateManager.updateConnectionStatus(),
            toggleTheme: () => stateManager.toggleTheme(),
            getStateSnapshot: () => stateManager.getStateSnapshot(),
            isAvailable: () => stateManager.isAvailable()
        };
    }

    // 导出到全局命名空间
    window.OTA.managers.StateManager = StateManager;
    window.getStateManagerAdapter = getStateManagerAdapter;

    // 创建全局实例并注册到依赖容器
    if (window.OTA && window.OTA.container) {
        try {
            window.OTA.container.register('stateManager', () => {
                const manager = new StateManager();
                manager.init();
                return manager;
            }, { singleton: true });
            getLogger().log('StateManager已注册到依赖容器', 'info');
        } catch (error) {
            console.warn('StateManager注册到依赖容器失败:', error.message);
        }
    }

    // 向后兼容性暴露
    if (!window.stateManager && !window.OTA.stateManager) {
        const globalInstance = new StateManager();
        globalInstance.init();
        window.OTA.stateManager = globalInstance;
        window.stateManager = globalInstance;
    }

})();