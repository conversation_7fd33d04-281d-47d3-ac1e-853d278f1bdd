/**
 * 价格管理器模块 (优化版)
 * 负责价格转换、货币处理和价格显示逻辑
 * 保留核心功能，去除过度复杂的部分
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    /**
     * 价格管理器类 (优化版)
     * 保留核心价格处理功能，简化实现逻辑
     */
    class PriceManager {
        constructor(elements) {
            this.elements = elements;
            this.currencyConverter = null;
            
            // 简化的手动编辑状态跟踪
            this.manualEditMode = false;
            this.isAutoFilling = false;
            this.editTimeout = null;
        }

        /**
         * 获取国际化管理器
         */
        getI18nManager() {
            return window.OTA?.i18nManager || window.i18nManager;
        }

        /**
         * 进入手动编辑模式
         */
        enterManualEditMode() {
            if (!this.manualEditMode) {
                this.manualEditMode = true;
                this.addManualEditIndicator();
                getLogger().log('进入手动编辑模式', 'info');
            }
        }

        /**
         * 退出手动编辑模式
         */
        exitManualEditMode() {
            if (this.manualEditMode) {
                this.manualEditMode = false;
                if (this.editTimeout) {
                    clearTimeout(this.editTimeout);
                    this.editTimeout = null;
                }
                this.removeManualEditIndicator();
                getLogger().log('退出手动编辑模式', 'info');
            }
        }

        /**
         * 设置自动填充状态
         */
        setAutoFillingState(isAutoFilling) {
            this.isAutoFilling = isAutoFilling;
        }

        /**
         * 添加手动编辑模式的视觉提示 (简化版)
         */
        addManualEditIndicator() {
            const priceGroup = document.querySelector('#otaPriceGroup');
            if (priceGroup && !priceGroup.querySelector('.manual-edit-indicator')) {
                const indicator = document.createElement('div');
                indicator.className = 'manual-edit-indicator';
                indicator.innerHTML = '✏️ 手动编辑中';
                indicator.style.cssText = `
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    background: #ffc107;
                    color: #000;
                    font-size: 10px;
                    padding: 2px 6px;
                    border-radius: 8px;
                    z-index: 10;
                    pointer-events: none;
                `;
                priceGroup.style.position = 'relative';
                priceGroup.appendChild(indicator);
            }
        }

        /**
         * 移除手动编辑模式的视觉提示
         */
        removeManualEditIndicator() {
            const indicator = document.querySelector('.manual-edit-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        /**
         * 重置编辑模式并恢复汇率转换
         */
        resetManualEditModeAndUpdateConversion() {
            if (this.manualEditMode) {
                this.exitManualEditMode();
                this.updatePriceConversion(true);
                getLogger().log('用户重置编辑模式并恢复汇率转换', 'info');
            }
        }

        /**
         * 初始化价格管理器
         */
        init() {
            this.currencyConverter = this.getCurrencyConverter();
            this.createPriceConversionDisplay();
            this.setupPriceValidation();
            getLogger().log('价格管理器初始化完成', 'success');
        }

        /**
         * 获取货币转换器 (简化版)
         */
        getCurrencyConverter() {
            return window.OTA?.managers?.CurrencyConverter ? 
                   new window.OTA.managers.CurrencyConverter() : 
                   this.createSimpleCurrencyConverter();
        }

        /**
         * 创建简单货币转换器 (备用方案)
         */
        createSimpleCurrencyConverter() {
            const exchangeRates = {
                'MYR': 1.0,
                'USD': 4.3,
                'SGD': 3.4,
                'CNY': 0.615
            };

            return {
                convertToMYR: (amount, currency) => {
                    const rate = exchangeRates[currency] || 1;
                    const convertedAmount = amount * rate;
                    return {
                        originalAmount: amount,
                        convertedAmount: convertedAmount,
                        needsConversion: currency !== 'MYR',
                        exchangeRate: rate
                    };
                },
                formatPrice: (amount, currency) => {
                    const symbols = {
                        'MYR': 'RM',
                        'USD': '$',
                        'SGD': 'S$',
                        'CNY': '¥'
                    };
                    return `${symbols[currency] || currency} ${parseFloat(amount).toFixed(2)}`;
                }
            };
        }

        /**
         * 创建价格转换显示元素 (简化版)
         */
        createPriceConversionDisplay() {
            const priceGroup = document.querySelector('.price-input-group');
            if (!priceGroup || priceGroup.querySelector('.price-conversion-display')) {
                return;
            }

            const conversionDisplay = document.createElement('div');
            conversionDisplay.className = 'price-conversion-display';
            conversionDisplay.innerHTML = `
                <div class="conversion-info">
                    <div class="original-price"></div>
                    <div class="conversion-arrow">→</div>
                    <div class="converted-price"></div>
                </div>
                <div class="conversion-rate"></div>
                <button type="button" class="reset-exchange-btn" style="display:none;">
                    重置汇率转换
                </button>
            `;
            conversionDisplay.style.display = 'none';
            priceGroup.appendChild(conversionDisplay);

            // 绑定重置按钮事件
            const resetBtn = conversionDisplay.querySelector('.reset-exchange-btn');
            resetBtn.onclick = () => this.resetManualEditModeAndUpdateConversion();

            getLogger().log('价格转换显示元素已创建', 'info');
        }

        /**
         * 更新价格转换显示 (简化版)
         */
        updatePriceConversion(force = false) {
            if (!force && this.manualEditMode && !this.isAutoFilling) {
                return;
            }

            const priceInput = this.elements.otaPrice;
            const currencySelect = this.elements.currency;
            const conversionDisplay = document.querySelector('.price-conversion-display');

            if (!priceInput || !currencySelect || !conversionDisplay) {
                return;
            }

            const amount = parseFloat(priceInput.value);
            const fromCurrency = currencySelect.value;

            if (!amount || amount <= 0 || !fromCurrency) {
                conversionDisplay.style.display = 'none';
                return;
            }

            if (this.currencyConverter) {
                const result = this.currencyConverter.convertToMYR(amount, fromCurrency);
                
                if (result.needsConversion) {
                    const originalPrice = this.currencyConverter.formatPrice(amount, fromCurrency);
                    const convertedPrice = this.currencyConverter.formatPrice(result.convertedAmount, 'MYR');
                    
                    conversionDisplay.querySelector('.original-price').textContent = `原价 ${originalPrice}`;
                    conversionDisplay.querySelector('.converted-price').textContent = `转换后 ${convertedPrice}`;
                    conversionDisplay.querySelector('.conversion-rate').textContent = `汇率: 1 ${fromCurrency} = ${result.exchangeRate} MYR`;
                    
                    // 控制重置按钮显示
                    const resetBtn = conversionDisplay.querySelector('.reset-exchange-btn');
                    resetBtn.style.display = this.manualEditMode ? 'inline-block' : 'none';
                    
                    conversionDisplay.style.display = 'block';
                    getLogger().log('价格转换显示已更新', 'info');
                } else {
                    conversionDisplay.style.display = 'none';
                }
            }
        }

        /**
         * 验证价格输入 (简化版)
         */
        validatePriceInput() {
            const priceInput = this.elements.otaPrice;
            const priceGroup = document.querySelector('.price-input-group');

            if (!priceInput || !priceGroup) return;

            const amount = parseFloat(priceInput.value);
            priceGroup.classList.remove('invalid', 'valid');

            if (priceInput.value && (isNaN(amount) || amount <= 0)) {
                priceGroup.classList.add('invalid');
            } else if (amount > 0) {
                priceGroup.classList.add('valid');
            }
        }

        /**
         * 货币切换时的智能汇率转换 (简化版)
         */
        convertPriceOnCurrencyChange(newCurrency, oldCurrency) {
            if (this.manualEditMode && !this.isAutoFilling) return;

            const priceInput = this.elements.otaPrice;
            if (!priceInput || !priceInput.value || !this.currencyConverter) return;

            const currentAmount = parseFloat(priceInput.value);
            if (isNaN(currentAmount) || currentAmount <= 0) return;

            try {
                // 转换为MYR基准
                const toMYRResult = this.currencyConverter.convertToMYR(currentAmount, oldCurrency);
                
                // 从MYR转换为目标货币 (简化计算)
                const newResult = this.currencyConverter.convertToMYR(1, newCurrency);
                const convertedAmount = Math.round((toMYRResult.convertedAmount / newResult.exchangeRate) * 100) / 100;

                priceInput.value = convertedAmount.toFixed(2);
                this.updatePriceConversion();

                getLogger().log('货币切换转换完成', 'info', {
                    from: `${currentAmount} ${oldCurrency}`,
                    to: `${convertedAmount} ${newCurrency}`
                });
            } catch (error) {
                getLogger().log('货币切换转换失败', 'error', { error: error.message });
            }
        }

        /**
         * 设置价格验证 (简化版)
         */
        setupPriceValidation() {
            if (this.elements.otaPrice) {
                // 焦点事件
                this.elements.otaPrice.addEventListener('focus', () => {
                    if (!this.isAutoFilling) {
                        setTimeout(() => {
                            if (!this.isAutoFilling) {
                                this.enterManualEditMode();
                            }
                        }, 100);
                    }
                });

                // 键盘输入事件
                this.elements.otaPrice.addEventListener('keydown', (event) => {
                    if (!this.isAutoFilling) {
                        const inputKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];
                        const isDigitKey = /^[0-9.]$/.test(event.key);
                        
                        if (isDigitKey || inputKeys.includes(event.key)) {
                            this.enterManualEditMode();
                        }
                    }
                });

                // 输入事件
                this.elements.otaPrice.addEventListener('input', () => {
                    this.validatePriceInput();
                    if (!this.manualEditMode || this.isAutoFilling) {
                        this.updatePriceConversion();
                    }
                });

                // 失焦事件
                this.elements.otaPrice.addEventListener('blur', () => {
                    this.validatePriceInput();
                    if (this.manualEditMode) {
                        this.editTimeout = setTimeout(() => {
                            this.exitManualEditMode();
                            this.updatePriceConversion(true);
                        }, 2000);
                    }
                });
            }

            // 货币切换监听
            if (this.elements.currency) {
                let previousCurrency = this.elements.currency.value || 'MYR';
                this.elements.currency.addEventListener('change', () => {
                    const newCurrency = this.elements.currency.value;
                    this.convertPriceOnCurrencyChange(newCurrency, previousCurrency);
                    previousCurrency = newCurrency;
                });
            }
        }

        /**
         * 处理价格转换 (简化版)
         */
        processPriceConversion(orderData) {
            if (!orderData || !this.currencyConverter) return;

            try {
                if (orderData.ota_price && orderData.currency) {
                    if (this.elements.otaPrice) {
                        this.elements.otaPrice.value = orderData.ota_price;
                    }
                    if (this.elements.currency) {
                        this.elements.currency.value = orderData.currency;
                    }
                    this.updatePriceConversion();
                    getLogger().log('价格转换处理完成', 'success');
                }
            } catch (error) {
                getLogger().log('价格转换处理失败', 'error', { error: error.message });
            }
        }

        /**
         * 重置价格字段
         */
        resetPriceFields() {
            if (this.elements.otaPrice) {
                this.elements.otaPrice.value = '';
            }
            if (this.elements.currency) {
                this.elements.currency.value = 'MYR';
            }

            const conversionDisplay = document.querySelector('.price-conversion-display');
            if (conversionDisplay) {
                conversionDisplay.style.display = 'none';
            }

            const priceGroup = document.querySelector('.price-input-group');
            if (priceGroup) {
                priceGroup.classList.remove('invalid', 'valid');
            }

            getLogger().log('价格字段已重置', 'info');
        }

        /**
         * 设置价格和货币
         */
        setPriceAndCurrency(amount, currency) {
            if (this.elements.otaPrice) {
                this.elements.otaPrice.value = amount;
            }
            if (this.elements.currency) {
                this.elements.currency.value = currency;
            }

            this.validatePriceInput();
            this.updatePriceConversion();
            getLogger().log('价格和货币已设置', 'info', { amount, currency });
        }
    }

    // 导出到全局命名空间
    window.OTA.managers.PriceManager = PriceManager;

})();