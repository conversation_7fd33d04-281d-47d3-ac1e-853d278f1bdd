# OTA系统API接口文档

## 📋 文档概述

**更新日期**: 2025-08-02
**版本**: v1.0 (微服务化架构)
**状态**: 标准化接口规范完成

## 🔧 微服务标准接口规范

### MicroserviceInterface - 核心接口

所有微服务必须实现的标准接口，确保统一的生命周期管理和状态监控。

#### 必需方法 (Required Methods)

##### `init() => Promise<void>`
**描述**: 初始化微服务，设置必要的配置和依赖

**签名**:
```javascript
async init() {
    // 初始化逻辑
    this.initialized = true;
    return Promise.resolve();
}
```

**使用示例**:
```javascript
const uiService = window.OTA.getService('uiService');
await uiService.init();
```

**返回值**: Promise<void>
**异常**: 初始化失败时抛出Error

---

##### `destroy() => Promise<void>`
**描述**: 销毁微服务，清理资源和事件监听器

**签名**:
```javascript
async destroy() {
    // 清理逻辑
    this.initialized = false;
    return Promise.resolve();
}
```

**使用示例**:
```javascript
const uiService = window.OTA.getService('uiService');
await uiService.destroy();
```

**返回值**: Promise<void>
**异常**: 销毁失败时抛出Error

---

##### `getStatus() => Object`
**描述**: 获取微服务当前状态信息

**签名**:
```javascript
getStatus() {
    return {
        name: this.serviceName,
        initialized: this.initialized,
        healthy: this.isHealthy(),
        lastActivity: this.lastActivity,
        dependencies: this.dependencies
    };
}
```

**使用示例**:
```javascript
const uiService = window.OTA.getService('uiService');
const status = uiService.getStatus();
console.log('UI服务状态:', status);
```

**返回值**: 
```typescript
{
    name: string,           // 服务名称
    initialized: boolean,   // 是否已初始化
    healthy: boolean,       // 是否健康
    lastActivity: Date,     // 最后活动时间
    dependencies: string[]  // 依赖的其他服务
}
```

---

##### `isAvailable() => boolean`
**描述**: 检查微服务是否可用

**签名**:
```javascript
isAvailable() {
    return this.initialized && this.isHealthy();
}
```

**使用示例**:
```javascript
const uiService = window.OTA.getService('uiService');
if (uiService.isAvailable()) {
    // 使用服务
    uiService.showAlert('服务可用');
}
```

**返回值**: boolean
**说明**: 返回true表示服务已初始化且健康

#### 可选方法 (Optional Methods)

##### `configure(config) => void`
**描述**: 配置微服务参数

**签名**:
```javascript
configure(config) {
    this.config = { ...this.config, ...config };
}
```

**参数**:
- `config`: Object - 配置对象

**使用示例**:
```javascript
const priceService = window.OTA.getService('priceService');
priceService.configure({
    defaultCurrency: 'MYR',
    decimalPlaces: 2
});
```

---

##### `reset() => void`
**描述**: 重置微服务状态到初始状态

**签名**:
```javascript
reset() {
    this.config = { ...this.defaultConfig };
    this.state = { ...this.defaultState };
}
```

**使用示例**:
```javascript
const formService = window.OTA.getService('formService');
formService.reset();
```

## 🎯 服务定位器接口

### ServiceLocator - 服务管理接口

#### `register(name, service) => ServiceLocator`
**描述**: 注册微服务到服务定位器

**参数**:
- `name`: string - 服务名称
- `service`: Object - 服务实例

**返回值**: ServiceLocator实例（支持链式调用）

**使用示例**:
```javascript
window.OTA.serviceLocator
    .register('uiService', new UIService())
    .register('formService', new FormService());
```

---

#### `get(name) => Object | null`
**描述**: 获取已注册的微服务实例

**参数**:
- `name`: string - 服务名称

**返回值**: 服务实例或null

**使用示例**:
```javascript
const uiService = window.OTA.serviceLocator.get('uiService');
if (uiService) {
    uiService.showAlert('Hello World');
}
```

---

#### `initializeAllMicroservices() => Promise<void>`
**描述**: 批量初始化所有已注册的微服务

**返回值**: Promise<void>

**使用示例**:
```javascript
await window.OTA.serviceLocator.initializeAllMicroservices();
console.log('所有微服务初始化完成');
```

---

#### `destroyAllMicroservices() => Promise<void>`
**描述**: 批量销毁所有已注册的微服务

**返回值**: Promise<void>

**使用示例**:
```javascript
await window.OTA.serviceLocator.destroyAllMicroservices();
console.log('所有微服务已销毁');
```

---

#### `healthCheckMicroservices() => Object`
**描述**: 检查所有微服务的健康状态

**返回值**: 
```typescript
{
    healthy: string[],      // 健康的服务列表
    unhealthy: string[],    // 不健康的服务列表
    uninitialized: string[] // 未初始化的服务列表
}
```

**使用示例**:
```javascript
const healthReport = window.OTA.serviceLocator.healthCheckMicroservices();
console.log('健康检查报告:', healthReport);
```

## 🔄 统一服务访问接口

### `window.OTA.getService(serviceName) => Object | null`
**描述**: 统一的服务获取接口，提供服务定位器的简化访问

**参数**:
- `serviceName`: string - 服务名称

**返回值**: 服务实例或null

**使用示例**:
```javascript
// 推荐的服务访问方式
const uiService = window.OTA.getService('uiService');
const formService = window.OTA.getService('formService');
const priceService = window.OTA.getService('priceService');
```

**支持的服务名称**:
- `'uiService'` - UI管理微服务
- `'formService'` - 表单处理微服务
- `'priceService'` - 价格计算微服务
- `'stateService'` - 状态管理微服务
- `'eventService'` - 事件处理微服务
- `'apiService'` - API调用微服务

## 📊 接口使用最佳实践

### 1. 服务初始化模式
```javascript
// 正确的服务初始化流程
async function initializeServices() {
    try {
        // 批量初始化所有服务
        await window.OTA.serviceLocator.initializeAllMicroservices();
        
        // 检查服务健康状态
        const healthReport = window.OTA.serviceLocator.healthCheckMicroservices();
        
        if (healthReport.unhealthy.length > 0) {
            console.warn('部分服务不健康:', healthReport.unhealthy);
        }
        
        console.log('服务初始化完成');
    } catch (error) {
        console.error('服务初始化失败:', error);
    }
}
```

### 2. 安全的服务访问模式
```javascript
// 安全的服务访问模式
function safeServiceCall(serviceName, methodName, ...args) {
    const service = window.OTA.getService(serviceName);
    
    if (!service) {
        console.warn(`服务 ${serviceName} 不存在`);
        return null;
    }
    
    if (!service.isAvailable()) {
        console.warn(`服务 ${serviceName} 不可用`);
        return null;
    }
    
    if (typeof service[methodName] !== 'function') {
        console.warn(`服务 ${serviceName} 不支持方法 ${methodName}`);
        return null;
    }
    
    try {
        return service[methodName](...args);
    } catch (error) {
        console.error(`调用 ${serviceName}.${methodName} 失败:`, error);
        return null;
    }
}

// 使用示例
safeServiceCall('uiService', 'showAlert', '操作成功', 'success');
```

### 3. 服务状态监控模式
```javascript
// 定期监控服务状态
function monitorServicesHealth() {
    setInterval(() => {
        const healthReport = window.OTA.serviceLocator.healthCheckMicroservices();
        
        if (healthReport.unhealthy.length > 0) {
            console.warn('发现不健康的服务:', healthReport.unhealthy);
            
            // 尝试重新初始化不健康的服务
            healthReport.unhealthy.forEach(async (serviceName) => {
                const service = window.OTA.getService(serviceName);
                if (service && typeof service.init === 'function') {
                    try {
                        await service.init();
                        console.log(`服务 ${serviceName} 重新初始化成功`);
                    } catch (error) {
                        console.error(`服务 ${serviceName} 重新初始化失败:`, error);
                    }
                }
            });
        }
    }, 30000); // 每30秒检查一次
}
```

## 🎨 具体微服务接口说明

### UIService - UI管理微服务

#### 核心方法

##### `showAlert(message, type, options)`
**描述**: 显示用户提示信息

**参数**:
- `message`: string - 提示消息
- `type`: string - 消息类型 ('success', 'error', 'warning', 'info')
- `options`: Object - 可选配置

**使用示例**:
```javascript
const uiService = window.OTA.getService('uiService');
uiService.showAlert('操作成功', 'success', { duration: 3000 });
```

##### `updateLoginUI(isLoggedIn, userInfo)`
**描述**: 更新登录状态UI

**参数**:
- `isLoggedIn`: boolean - 是否已登录
- `userInfo`: Object - 用户信息

**使用示例**:
```javascript
const uiService = window.OTA.getService('uiService');
uiService.updateLoginUI(true, { name: 'John', email: '<EMAIL>' });
```

### FormService - 表单处理微服务

#### 核心方法

##### `fillFormFromData(data)`
**描述**: 根据数据填充表单

**参数**:
- `data`: Object - 表单数据

**使用示例**:
```javascript
const formService = window.OTA.getService('formService');
formService.fillFormFromData({
    customerName: 'John Doe',
    pickupLocation: 'KLIA',
    destination: 'KL Sentral'
});
```

##### `validateForm(formData)`
**描述**: 验证表单数据

**参数**:
- `formData`: Object - 待验证的表单数据

**返回值**:
```typescript
{
    isValid: boolean,
    errors: string[]
}
```

### PriceService - 价格计算微服务

#### 核心方法

##### `convertCurrency(amount, fromCurrency, toCurrency)`
**描述**: 货币转换

**参数**:
- `amount`: number - 金额
- `fromCurrency`: string - 源货币
- `toCurrency`: string - 目标货币

**返回值**: number - 转换后的金额

**使用示例**:
```javascript
const priceService = window.OTA.getService('priceService');
const convertedAmount = priceService.convertCurrency(100, 'USD', 'MYR');
```

### EventService - 事件处理微服务

#### 核心方法

##### `addEventHandler(element, eventType, handler, options)`
**描述**: 添加事件处理器

**参数**:
- `element`: HTMLElement - DOM元素
- `eventType`: string - 事件类型
- `handler`: Function - 事件处理函数
- `options`: Object - 事件选项

**返回值**: string - 事件处理器ID

**使用示例**:
```javascript
const eventService = window.OTA.getService('eventService');
const handlerId = eventService.addEventHandler(
    document.getElementById('submitBtn'),
    'click',
    () => console.log('按钮被点击'),
    { once: true }
);
```

## 🔍 错误处理和调试

### 错误类型

#### ServiceNotFoundError
**描述**: 请求的服务不存在

**处理方式**:
```javascript
try {
    const service = window.OTA.getService('nonExistentService');
    if (!service) {
        throw new Error('ServiceNotFoundError: 服务不存在');
    }
} catch (error) {
    console.error('服务访问错误:', error.message);
}
```

#### ServiceUnavailableError
**描述**: 服务不可用（未初始化或不健康）

**处理方式**:
```javascript
const service = window.OTA.getService('uiService');
if (!service.isAvailable()) {
    console.warn('ServiceUnavailableError: UI服务不可用');
    // 尝试重新初始化
    await service.init();
}
```

### 调试工具

#### 服务状态检查
```javascript
// 检查所有服务状态
function debugServices() {
    const services = ['uiService', 'formService', 'priceService', 'stateService', 'eventService'];

    services.forEach(serviceName => {
        const service = window.OTA.getService(serviceName);
        if (service) {
            console.log(`${serviceName}:`, service.getStatus());
        } else {
            console.warn(`${serviceName}: 服务不存在`);
        }
    });
}
```

#### 性能监控
```javascript
// 监控服务调用性能
function monitorServicePerformance(serviceName, methodName, ...args) {
    const startTime = performance.now();
    const service = window.OTA.getService(serviceName);

    try {
        const result = service[methodName](...args);
        const endTime = performance.now();
        console.log(`${serviceName}.${methodName} 执行时间: ${endTime - startTime}ms`);
        return result;
    } catch (error) {
        const endTime = performance.now();
        console.error(`${serviceName}.${methodName} 执行失败 (${endTime - startTime}ms):`, error);
        throw error;
    }
}
```

---

**文档维护**: 随接口演进持续更新
**最后更新**: 2025-08-02 (微服务化架构重构完成)
**接口版本**: v1.0
