/**
 * 微服务配置文件
 * 定义OTA订单处理系统的微服务架构配置
 * 
 * <AUTHOR> System
 * @created 2025-01-01
 */

(function() {
    'use strict';

    // 确保全局命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.config = window.OTA.config || {};

    /**
     * 微服务配置对象
     */
    const ServicesConfig = {
        
        /**
         * 服务注册表 - 定义所有可用的微服务
         */
        services: {
            // UI服务 - 负责用户界面管理
            uiService: {
                enabled: true,
                path: 'js/services/ui-service.js',
                dependencies: ['appState', 'logger'],
                priority: 1,
                description: '用户界面管理服务，负责UI组件的显示、隐藏和状态管理'
            },

            // 表单服务 - 负责表单处理和验证
            formService: {
                enabled: true,
                path: 'js/services/form-service.js',
                dependencies: ['appState', 'logger', 'apiService'],
                priority: 2,
                description: '表单处理服务，负责表单数据收集、验证和填充'
            },

            // 价格服务 - 负责价格计算和货币转换
            priceService: {
                enabled: true,
                path: 'js/services/price-service.js',
                dependencies: ['appState', 'logger'],
                priority: 3,
                description: '价格处理服务，负责货币转换和价格计算'
            },

            // 状态服务 - 负责UI状态管理
            stateService: {
                enabled: true,
                path: 'js/services/state-service.js',
                dependencies: ['appState', 'logger'],
                priority: 4,
                description: 'UI状态管理服务，负责主题切换和界面状态控制'
            },

            // 事件服务 - 负责事件处理和协调
            eventService: {
                enabled: true,
                path: 'js/services/event-service.js',
                dependencies: ['appState', 'logger'],
                priority: 5,
                description: '事件处理服务，负责DOM事件绑定和事件协调'
            }
        },

        /**
         * 服务生命周期配置
         */
        lifecycle: {
            // 初始化超时时间（毫秒）
            initTimeout: 5000,
            
            // 销毁超时时间（毫秒）
            destroyTimeout: 3000,
            
            // 健康检查间隔（毫秒）
            healthCheckInterval: 30000,
            
            // 自动重启失败的服务
            autoRestart: true,
            
            // 最大重启次数
            maxRestartAttempts: 3
        },

        /**
         * 服务接口标准
         */
        interface: {
            // 必需的方法
            requiredMethods: ['init', 'destroy', 'getStatus'],
            
            // 可选的方法
            optionalMethods: ['reset', 'configure', 'healthCheck'],
            
            // 状态枚举
            states: {
                UNINITIALIZED: 'uninitialized',
                INITIALIZING: 'initializing',
                READY: 'ready',
                ERROR: 'error',
                DESTROYED: 'destroyed'
            }
        },

        /**
         * 依赖注入配置
         */
        dependency: {
            // 循环依赖检测
            circularDependencyCheck: true,
            
            // 依赖解析超时（毫秒）
            resolutionTimeout: 10000,
            
            // 懒加载依赖
            lazyLoading: true,
            
            // 依赖缓存
            caching: true
        },

        /**
         * 性能监控配置
         */
        monitoring: {
            // 启用性能监控
            enabled: true,
            
            // 监控指标
            metrics: {
                initTime: true,
                memoryUsage: true,
                errorRate: true,
                responseTime: true
            },
            
            // 日志级别
            logLevel: 'info',
            
            // 性能阈值（毫秒）
            thresholds: {
                initTime: 1000,
                responseTime: 500
            }
        },

        /**
         * 开发模式配置
         */
        development: {
            // 启用开发模式
            enabled: true,
            
            // 详细日志
            verboseLogging: true,
            
            // 热重载
            hotReload: false,
            
            // 调试信息
            debugInfo: true
        }
    };

    /**
     * 获取服务配置
     * @param {string} serviceName - 服务名称
     * @returns {Object|null} 服务配置对象
     */
    ServicesConfig.getServiceConfig = function(serviceName) {
        return this.services[serviceName] || null;
    };

    /**
     * 获取所有启用的服务
     * @returns {Array} 启用的服务列表
     */
    ServicesConfig.getEnabledServices = function() {
        return Object.keys(this.services)
            .filter(name => this.services[name].enabled)
            .sort((a, b) => this.services[a].priority - this.services[b].priority);
    };

    /**
     * 验证服务配置
     * @param {string} serviceName - 服务名称
     * @returns {boolean} 配置是否有效
     */
    ServicesConfig.validateServiceConfig = function(serviceName) {
        const config = this.getServiceConfig(serviceName);
        if (!config) return false;

        // 检查必需字段
        const requiredFields = ['enabled', 'path', 'dependencies', 'priority'];
        return requiredFields.every(field => config.hasOwnProperty(field));
    };

    /**
     * 获取服务依赖关系
     * @param {string} serviceName - 服务名称
     * @returns {Array} 依赖服务列表
     */
    ServicesConfig.getServiceDependencies = function(serviceName) {
        const config = this.getServiceConfig(serviceName);
        return config ? config.dependencies || [] : [];
    };

    // 将配置对象注册到全局命名空间
    window.OTA.ServicesConfig = ServicesConfig;
    window.OTA.config.services = ServicesConfig;

    // 兼容性：也注册到window.ServicesConfig和window.servicesConfig
    window.ServicesConfig = ServicesConfig;
    window.servicesConfig = ServicesConfig;

    // 日志记录
    if (window.OTA.logger) {
        window.OTA.logger.log('微服务配置文件已加载', 'info', {
            servicesCount: Object.keys(ServicesConfig.services).length,
            enabledServices: ServicesConfig.getEnabledServices().length
        });
    }

})();
